name: Push Production Docker image to GHCR

on:
  push:
    tags:
      - 'v*.*.*'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: carbonbright/product_lca
  TAG: latest
  AZURE_WEBAPP_NAME: carbonbright-lca

permissions: {}  # Reset all permissions to none by default

jobs:
  build-and-push:
    runs-on: ubuntu-22.04
    permissions:
      contents: read      # Needed to checkout code
      packages: write     # Needed to push to GHCR

    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}

  deploy:
    runs-on: ubuntu-22.04
    needs: build-and-push
    permissions:
      contents: read      # Needed to access code/configuration

    steps:
      - name: Deploy to Azure Web App
        uses: azure/webapps-deploy@v2
        with:
          app-name: ${{ env.AZURE_WEBAPP_NAME }}
          publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE }}
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.TAG }}
          slot-name: 'production'