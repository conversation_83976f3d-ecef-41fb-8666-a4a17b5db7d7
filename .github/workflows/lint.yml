name: Lint Changed Files

on:
  pull_request:
    paths:
      - '**.py'

permissions:
  contents: read
  pull-requests: read

# cancel in progress runs if the same workflow is triggered again on same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  pylint:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'

    - name: Install dependencies
      shell: bash
      run: |
        python -m pip install --upgrade pip
        pip install pylint
        pip install -r requirements.txt

    - name: Get list of changed files
      id: get-changed-files
      shell: bash
      run: |
        changed_files=$(git diff --name-only ${{ github.event.pull_request.base.sha }} ${{ github.sha }} | grep \.py$)
        existing_files=""
        for file in $changed_files; do
          if [ -e "$file" ] && [[ ! "$file" == */alembic/* ]]; then
            existing_files="$existing_files $file"
          fi
        done
        echo "Existing changed files: $existing_files"
        echo "files=$existing_files" >> $GITHUB_OUTPUT

    - name: Run pylint on changed files
      shell: bash
      run: |
        files="${{ steps.get-changed-files.outputs.files }}"
        if [ -n "$files" ]; then
            pylint $files
        else
            echo "No Python files were changed."
        fi