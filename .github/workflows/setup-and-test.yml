name: Setup and Test

on:
  pull_request

permissions:
  contents: read      # Needed to checkout code and access test files
  pull-requests: read # Needed to read PR information

env:
  PYTHON_VERSION: '3.10'
  TESTING_MODE: 'True'
  DB_CONNECTION_STRING: 'mysql+pymysql://root@localhost'
  HF_TOKEN: ${{ secrets.TESTING_HF_TOKEN }}
  MAPBOX_ACCESS_TOKEN: ${{ secrets.TESTING_MAPBOX_ACCESS_TOKEN }}
  GOOGLE_MAPS_API_KEY: ${{ secrets.TESTING_GOOGLE_MAPS_API_KEY }}
  ORS_TOKEN: ${{ secrets.TESTING_ORS_TOKEN }}
  PROPEL_AUTH_URL: ${{ secrets.TESTING_PROPEL_AUTH_URL }}
  PROPEL_AUTH_TOKEN: ${{ secrets.TESTING_PROPEL_AUTH_TOKEN }}
  ML_MODELS_ENDPOINT: ${{ secrets.ML_MODELS_ENDPOINT }}
  MYSQL_SERVICE_NAME: 'lca-mysql'
  PORT: '5000'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  setup-and-test:
    runs-on: runner-16GB-u2204
    timeout-minutes: 60

    steps:
      - uses: actions/checkout@v4
        
      - name: Cache LibreOffice
        uses: actions/cache@v4
        id: cache-libreoffice
        with:
          path: |
            /opt/libreoffice7.4
            /opt/libreoffice
          key: libreoffice-${{ runner.os }}-7.4.0

      - name: Download and Install LibreOffice if not cached
        if: steps.cache-libreoffice.outputs.cache-hit != 'true'
        run: |
          sudo mkdir -p /opt/libreoffice
          sudo wget -qO- https://cb-blob-storage.s3.amazonaws.com/LibreOffice_7.4.0.1_Linux_x86-64_deb.tar.gz | tar xvz -C /opt/libreoffice --strip-components=1
          sudo dpkg -i /opt/libreoffice/DEBS/*.deb
        shell: bash

      - name: Add LibreOffice to PATH
        run: echo "/opt/libreoffice7.4/program" >> $GITHUB_PATH

      - name: Install MySQL Client
        run: |
          sudo apt-get install -y mysql-client

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
        
      - name: Install python deps
        run: pip install -r requirements.txt

      - name: Setup Mysql Docker Container
        run: ./scripts/setup_local_mysql.sh

      - name: Run Background Seeding
        id: run_background_seeding
        env:
          WORKERS: 4
          BACKGROUND_SEEDING: 'True'
        run: |
          nohup ./startservice.sh > service.log 2>&1 &
          sleep 10
          TIMEOUT=600
          INTERVAL=10
          ELAPSED=0
          URL="http://0.0.0.0:${{ env.PORT }}/product-categories/"
          until [ $ELAPSED -ge $TIMEOUT ]; do
              RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" $URL || true)
              if [ "$RESPONSE" -eq "200" ]; then
                  echo "Background seeding complete"
                  break
              elif [ "$RESPONSE" -eq "503" ]; then
                  echo "Elapsed ($ELAPSED)s | Background seeding in progress..."
              elif [ "$RESPONSE" -eq "000" ]; then
                  echo "Elapsed ($ELAPSED)s | Network Error..."
              else
                  echo "Unexpected HTTP Status Code ($RESPONSE) Received. Background seeding failed."
                  exit 1
              fi
              sleep $INTERVAL
              ELAPSED=$(($ELAPSED + $INTERVAL))
          done
  
          if [ $ELAPSED -ge $TIMEOUT ]; then
              echo "Background seeding timed out. Exiting..."
              exit 1
          fi

      - name: Run Database Tests
        run: |
          python -m unittest discover databases/tests

      - name: Run API Tests
        run: |
          python -m unittest discover api/tests/

      - name: Run Service Tests
        run: python -m unittest discover services/tests

      # - name: Run Pipeline e2e Tests
      #   env:
      #     BACKGROUND_SEEDING: 'True'
      #   run: |
      #     ./scripts/setup_local_mysql.sh
      #     python -m unittest discover pipelines/tests

      - name: Run Pipeline V2 Tests
        run: |
          python -m unittest discover pipelines_v2/tests
      
      - name: Run Pipeline V2 Walker Tests
        run: |
          python -m unittest discover pipelines_v2/process_model_walker/tests
      
      - name: Run Pipeline V2 Utils Tests
        run: |
          python -m unittest discover pipelines_v2/utils/tests
      
      - name: Run Pipeline V2 Process Model Walker Tests
        run: |
          python -m unittest discover pipelines_v2/process_model_walker/tests
      
      - name: Run LCA Report Tests
        run: |
          python -m unittest discover lca_report/tests

      - name: Run Utils Tests
        run: |
          python -m unittest discover utils/tests
        
      - name: Run Python DocTests
        run: |
          chmod +x ./scripts/run_doctests.sh
          ./scripts/run_doctests.sh

      - name: Checkout Web App
        uses: actions/checkout@v4
        with:
          repository: carbonbright/carbonbright-web
          ref: main
          path: carbonbright-web
          token: ${{ secrets.TESTING_PAT }}
  
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
  
      - name: Install Web App Dependencies
        run: |
          cd carbonbright-web
          yarn install

      - name: Run E2E Web App Tests
        env:
          LCA_API_ENDPOINT: 'http://127.0.0.1:${{ env.PORT }}'
          PROPELAUTH_API_TOKEN: ${{ env.PROPEL_AUTH_TOKEN }}
          PROPELAUTH_AUTH_URL: ${{ env.PROPEL_AUTH_URL }}
          MAPBOX_SECRET_TOKEN: ${{ secrets.TESTING_MAPBOX_SECRET_TOKEN }}
          TEST_USER_EMAIL:  ${{ secrets.TESTING_USER_EMAIL }}
          TEST_USER_PASSWORD: ${{ secrets.TESTING_USER_PASSWORD }}
          PROPELAUTH_LOGIN_URL: ${{ secrets.PROPELAUTH_LOGIN_URL }}
        run: |
          cd carbonbright-web
          yarn test:e2e
      
      - name: Upload test report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ui-test-report
          path: |
            carbonbright-web/playwright-report/
            carbonbright-web/test-results/
            carbonbright-web/tests/
          retention-days: 7

      - name: Show LCA API logs
        if: always()
        run: cat service.log