"""LCA CLI - Setup Databases"""
from databases.engine import engine_pool
from setup.cli import setup_parser
from setup.db import (
    make_revision,
    revert_revision,
    upgrade_head,
    setup,
    setup_server,
)
from utils.logger import logger


@logger.catch
def main() -> None:
    args = setup_parser.parse_args()
    engine = engine_pool.get_root_engine()
    tenant_ids = None
    if args.tenant_ids:
        tenant_ids = args.tenant_ids.split(",")

    if args.make_revision:
        if not args.database:
            raise ValueError("Database argument is required for make-revision")

        make_revision(engine, args.database, args.make_revision)

    if args.revert_revision:
        if not args.database:
            raise ValueError("Database argument is required for revert-revision")

        revert_revision(engine, args.database)

    if args.upgrade_head:
        if not args.database:
            raise ValueError("Database argument is required for upgrade-head")

        upgrade_head(engine, args.database)

    if args.setup:
        if not args.database:
            raise ValueError("Database argument is required for setup")

        setup(engine, args.database)

    if args.setup_server:
        setup_server(engine, tenant_ids)

if __name__ == "__main__":
    main()
