from dataclasses import dataclass
from .template_value_replacements import TemplateValueReplacements


@dataclass
class ManufacturingReplacements(TemplateValueReplacements):
    manufacturing: list[dict[str, str]]

    def __post_init__(self):
        super().__init__()

    def replacements(self) -> dict[str, str]:
        return {}

    def insert_table(self, doc):
        super().insert_values_into_table(
            doc,
            '[Manufacturing Table]',
            self.manufacturing,
        )
