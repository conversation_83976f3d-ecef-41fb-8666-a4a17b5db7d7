from dataclasses import dataclass
from .template_value_replacements import TemplateValueReplacements


@dataclass
class CompanyInfoReplacements(TemplateValueReplacements):
    company_name: str
    manufacturer: str
    country_of_origin: str

    def replacements(self) -> dict[str, str]:
        return {
            "[Company Name]": self.company_name,
            "[Company]": self.company_name,
            "[Manufacturer]": self.manufacturer,
            "[Country of Origin]": self.country_of_origin,
        }