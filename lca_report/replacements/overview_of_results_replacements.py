from typing import Any, List
from dataclasses import dataclass
from .template_value_replacements import TemplateValueReplacements


@dataclass
class OverviewOfResultsReplacements(TemplateValueReplacements):
    raw_materials_gwp_impact: str
    raw_materials_share_of_total: str
    packaging_materials_gwp_impact: str
    packaging_materials_share_of_total: str
    manufacturing_gwp_impact: str
    manufacturing_share_of_total: str
    transport_gwp_impact: str
    transport_share_of_total: str
    use_phase_gwp_impact: str
    use_phase_share_of_total: str
    eol_gwp_impact: str
    eol_share_of_total: str
    total_gwp_impact: str
    packaging_quartile: str
    manufacturing_quartile: str
    manufacturing_high_or_low: str
    use_phase_quartile: str

    def replacements(self) -> dict[str, str]:
        return {
            "[Result RM]": self.raw_materials_gwp_impact,
            "[% RM]": self.raw_materials_share_of_total,
            "[Result PK]": self.packaging_materials_gwp_impact,
            "[% PK]": self.packaging_materials_share_of_total,
            "[Result MP]": self.manufacturing_gwp_impact,
            "[% MP]": self.manufacturing_share_of_total,
            "[Result TR]": self.transport_gwp_impact,
            "[% TR]": self.transport_share_of_total,
            "[Result CU]": self.use_phase_gwp_impact,
            "[% CU]": self.use_phase_share_of_total,
            "[Result EL]": self.eol_gwp_impact,
            "[% EL]": self.eol_share_of_total,
            "[Result Total]": self.total_gwp_impact,
            "[PK Quartile]": self.packaging_quartile,
            "[MP Quartile]": self.manufacturing_quartile,
            "[MP High or Low]": self.manufacturing_high_or_low,
            "[CU Quartile]": self.use_phase_quartile,
        }

    def insert_chart(self, doc):
        return super().insert_chart(
            doc,
            '[Overview Pie Chart]',
            'Emissions by Life Cycle Stage',
            [
                'Raw Materials',
                'Packaging',
                'Manufacturing',
                'Transport',
                'Use Phase',
                'End-of-life',
            ],
            [
                float(self.raw_materials_share_of_total),
                float(self.packaging_materials_share_of_total),
                float(self.manufacturing_share_of_total),
                float(self.transport_share_of_total),
                float(self.use_phase_share_of_total),
                float(self.eol_share_of_total),
            ],
        )