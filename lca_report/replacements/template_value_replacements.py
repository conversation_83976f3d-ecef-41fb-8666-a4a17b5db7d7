# pylint: disable=protected-access, invalid-name, too-many-arguments
import os
from abc import ABC, abstractmethod
from typing import List, Any
from dataclasses import dataclass
from docx.text.paragraph import Paragraph
from docx.oxml import OxmlElement

import matplotlib
import matplotlib.pyplot as plt
import matplotlib.lines as mlines


matplotlib.use('Agg')

def replace_placeholder_in_paragraph(paragraph, replacements):
    full_text = ''.join(run.text for run in paragraph.runs)

    replaced = False
    for placeholder, replacement_value in replacements.items():
        if placeholder in full_text:
            full_text = full_text.replace(placeholder, replacement_value)
            replaced = True

    if replaced:
        for run in paragraph.runs:
            run.clear()
        paragraph.add_run(full_text)

def inches_to_emu(inches):
    return int(inches * 914400)

def insert_table_after_paragraph(paragraph, data: List[Any]):
    doc = paragraph._parent
    table = doc.add_table(
        rows=0,
        cols=len(data[0]),
        width=inches_to_emu(inches=12),
    )

    for row_data in data:
        row = table.add_row().cells
        for i, cell_data in enumerate(row_data):
            row[i].text = cell_data

    p_index = list(doc._body).index(paragraph._element)
    doc._body.insert(p_index + 1, table._element)


def insert_chart_after_paragraph(
    paragraph,
    title: str,
    labels: List[str],
    data: List[Any],
):
    n = len(labels)
    cm = plt.get_cmap('tab10')
    colors = [cm(i/n) for i in range(n)]

    _, ax = plt.subplots()
    _, _, _ = ax.pie(
        data,
        colors=colors,
        autopct='%1.1f%%',
        startangle=140,
        wedgeprops={"width": 0.5},
        pctdistance=0.75,
    )

    plt.title(title)
    legend_handles = [
        mlines.Line2D(
            [0],
            [0],
            marker='o',
            color='w',
            markerfacecolor=color,
            markersize=10
        )
        for color in colors
    ]

    ax.legend(
        handles=legend_handles,
        labels=labels,
        ncol=len(labels),
        loc='lower center',
        frameon=False,
        bbox_to_anchor=(0.5, -0.2),
    )

    dir_path = os.path.dirname(os.path.realpath(__file__))
    chart_path = os.path.join(dir_path, '..', 'saved_charts', f"{title}.png")
    plt.savefig(chart_path, bbox_inches='tight')

    new_paragraph_element = OxmlElement('w:p')
    paragraph._element \
        .getparent() \
        .insert(
            paragraph._element.getparent().index(paragraph._element) + 1,
            new_paragraph_element,
        )

    new_paragraph = Paragraph(new_paragraph_element, paragraph._parent)
    new_paragraph.add_run().add_picture(
        chart_path,
        width=inches_to_emu(6),
        height=inches_to_emu(3.25),
    )

    os.remove(chart_path)


@dataclass
class TemplateValueReplacements(ABC):
    @abstractmethod
    def replacements(self) -> dict[str, str]:
        pass

    def replace_values(self, doc):
        for paragraph in doc.paragraphs:
            replace_placeholder_in_paragraph(paragraph, self.replacements())

        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        replace_placeholder_in_paragraph(paragraph, self.replacements())

    def insert_values_into_table(
        self,
        doc,
        table_placeholder: str,
        data: List[Any],
    ):
        for table in doc.tables:
            if len(table.rows) < 2:
                continue

            if table_placeholder in table.cell(1, 0).text:
                if not data:
                    table.cell(1, 0).text = "N/A"
                    continue

                for i, row_data in enumerate(data):
                    if i == len(table.rows) - 1:
                        table.add_row()

                    for j, cell_data in enumerate(row_data):
                        table.cell(i + 1, j).text = cell_data

    def insert_chart(
        self,
        doc,
        placeholder: str,
        title: str,
        labels: List[str],
        data: List[Any],
    ):
        for paragraph in doc.paragraphs:
            if placeholder in paragraph.text:
                insert_chart_after_paragraph(paragraph, title, labels, data)
                paragraph.clear()
