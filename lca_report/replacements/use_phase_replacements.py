from dataclasses import dataclass
from .template_value_replacements import TemplateValueReplacements


@dataclass
class UsePhaseReplacements(TemplateValueReplacements):
    consumer_uses: list[dict[str, str]]

    def __post_init__(self):
        super().__init__()

    def replacements(self) -> dict[str, str]:
        return {}

    def insert_table(self, doc):
        super().insert_values_into_table(
            doc,
            '[Use Phase Table]',
            self.consumer_uses,
        )
