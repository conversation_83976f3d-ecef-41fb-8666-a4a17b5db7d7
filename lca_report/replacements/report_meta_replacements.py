from dataclasses import dataclass
from datetime import datetime
from .template_value_replacements import TemplateValueReplacements


@dataclass
class ReportMetaReplacements(TemplateValueReplacements):
    country_of_use: str
    unit: str
    functional_unit: str
    functional_type: str
    date: str = datetime.now().strftime('%Y-%m-%d')
    month_and_year_of_study: str = datetime.now().strftime('%B %Y')

    def replacements(self) -> dict[str, str]:
        if self.unit.lower() == "per pack":
            functional_unit_display = f"{self.functional_unit} Uses {self.unit}"
        elif self.unit.lower() == "per use":
            functional_unit_display = self.unit
        else:
            functional_unit_display = f"{self.functional_unit}{self.unit}"

        return {
            "[Date]": self.date,
            "[Functional Unit]": functional_unit_display,
            "[Functional Type]": self.functional_type,
            "[Month & Year of Study]": self.month_and_year_of_study,
            "[Country of Use]": self.country_of_use,
        }
