from dataclasses import dataclass
from .template_value_replacements import TemplateValueReplacements


@dataclass
class LCAReportOverviewReplacements(TemplateValueReplacements):
    ghg_emissions_kg_co2e_per_functional_unit: str
    highest_emitting_stage: str
    highest_emitting_stage_percentage: str
    second_highest_emitting_stage: str
    second_highest_emitting_stage_percentage: str
    lowest_emitting_stage: str
    lowest_emitting_stage_percentage: str

    def replacements(self) -> dict[str, str]:
        return {
            "[GHG Emissions]": self.ghg_emissions_kg_co2e_per_functional_unit,
            "[Highest Emitting Life Cycle Stage]": self.highest_emitting_stage,
            "[Highest Emitting Life Cycle Stage Percent]": self.highest_emitting_stage_percentage,
            "[2nd Highest Emitting Stage]": self.second_highest_emitting_stage,
            "[2nd Highest Emitting Stage Percent]": self.second_highest_emitting_stage_percentage,
            "[Lowest Emitting Life Cycle Stage]": self.lowest_emitting_stage,
            "[Lowest Emitting Life Cycle Stage Percent]": self.lowest_emitting_stage_percentage,
            "[% Total]": "100%",
        }