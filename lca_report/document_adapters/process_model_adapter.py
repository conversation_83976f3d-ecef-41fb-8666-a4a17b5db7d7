from typing import List
from pydantic import BaseModel
from lca_report.replacements import (
    CompanyInfoReplacements,
    EOLReplacements,
    LCAReportOverviewReplacements,
    ManufacturingReplacements,
    OverviewOfResultsReplacements,
    PackagingMaterialReplacements,
    ProductInfoReplacements,
    RawMaterialsReplacements,
    ReportMetaReplacements,
    TransportReplacements,
    UsePhaseReplacements,
)
from databases.tenant.models import Product, Node
from pipelines_v2.utils.unit import UnitType
from .document_adapter import DocumentAdapter


class ReportMeta(BaseModel):
    country_of_use: str
    unit: str
    functional_unit: float
    functional_type: str
    date: str

class CompanyMeta(BaseModel):
    company_name: str
    manufacturer: str
    country_of_origin: str

class ProcessModelData(BaseModel):
    report_meta: ReportMeta
    company_meta: CompanyMeta
    product_info: Product
    aggregation: dict
    nodes: List[Node]

DEFAULT_IMPACT_FACTOR = {
    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
    "impact_categories": [
        {
            "name": "climate change",
            "indicator": "global warming potential (GWP100)",
        }
    ],
}

class ProcessModelAdapter(DocumentAdapter[ProcessModelData]):
    def __init__(self, data: ProcessModelData) -> None:
        super().__init__(data)

        self._scale_factor = (
            1000
            if self.data.report_meta.unit.lower() in ["kg", "l"]
            else 1
        )

        self.raw_materials = [
            next(
                (
                    impact for impact in material["impacts"]
                    if (
                        impact["emissions_factor_value"]["impact_indicator"]["lcia_method"]
                        == DEFAULT_IMPACT_FACTOR["lcia_method"]
                        and impact["emissions_factor_value"]["impact_indicator"]["category"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["name"]
                        and impact["emissions_factor_value"]["impact_indicator"]["indicator"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["indicator"]
                    )
                ),
                None
            )
            for material in self.data.aggregation["default"]["materials"]["segment_nodes"]
        ] if "materials" in self.data.aggregation["default"] else []

        self.packaging_materials = [
            next(
                (
                    impact for impact in material["impacts"]
                    if (
                        impact["emissions_factor_value"]["impact_indicator"]["lcia_method"]
                        == DEFAULT_IMPACT_FACTOR["lcia_method"]
                        and impact["emissions_factor_value"]["impact_indicator"]["category"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["name"]
                        and impact["emissions_factor_value"]["impact_indicator"]["indicator"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["indicator"]
                    )
                ),
                None
            )
            for material in self.data.aggregation["default"]["packaging"]["segment_nodes"]
        ] if "packaging" in self.data.aggregation["default"] else []

        self.transportation = [
            next(
                (
                    impact for impact in transport_segment["impacts"]
                    if (
                        impact["emissions_factor_value"]["impact_indicator"]["lcia_method"]
                        == DEFAULT_IMPACT_FACTOR["lcia_method"]
                        and impact["emissions_factor_value"]["impact_indicator"]["category"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["name"]
                        and impact["emissions_factor_value"]["impact_indicator"]["indicator"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["indicator"]
                    )
                ),
                None
            )
            for transport_segment in self.data.aggregation[
                "default"
            ]["transportation"]["segment_nodes"]
        ] if "transportation" in self.data.aggregation["default"] else []

        self.manufacturing_processes = [
            next(
                (
                    impact for impact in process["impacts"]
                    if (
                        impact["emissions_factor_value"]["impact_indicator"]["lcia_method"]
                        == DEFAULT_IMPACT_FACTOR["lcia_method"]
                        and impact["emissions_factor_value"]["impact_indicator"]["category"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["name"]
                        and impact["emissions_factor_value"]["impact_indicator"]["indicator"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["indicator"]
                    )
                ),
                None
            )
            for process in self.data.aggregation["default"]["production"]["segment_nodes"]
        ] if "production" in self.data.aggregation["default"] else []

        self.consumer_uses = [
            next(
                (
                    impact for impact in use["impacts"]
                    if (
                        impact["emissions_factor_value"]["impact_indicator"]["lcia_method"]
                        == DEFAULT_IMPACT_FACTOR["lcia_method"]
                        and impact["emissions_factor_value"]["impact_indicator"]["category"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["name"]
                        and impact["emissions_factor_value"]["impact_indicator"]["indicator"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["indicator"]
                    )
                ),
                None
            )
            for use in self.data.aggregation["default"]["use"]["segment_nodes"]
        ] if "use" in self.data.aggregation["default"] else []

        self.eol = [
            next(
                (
                    impact for impact in eol["impacts"]
                    if (
                        impact["emissions_factor_value"]["impact_indicator"]["lcia_method"]
                        == DEFAULT_IMPACT_FACTOR["lcia_method"]
                        and impact["emissions_factor_value"]["impact_indicator"]["category"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["name"]
                        and impact["emissions_factor_value"]["impact_indicator"]["indicator"]
                        == DEFAULT_IMPACT_FACTOR["impact_categories"][0]["indicator"]
                    )
                ),
                None
            )
            for eol in self.data.aggregation["default"]["eol"]["segment_nodes"]
        ] if "eol" in self.data.aggregation["default"] else []

        self.total_raw_materials_emissions = self.calculate_emissions_per_functional_unit(
            sum(
                (
                    raw_material["impact_amount"]
                    for raw_material in self.raw_materials
                )
            )
        )

        self.total_packaging_emissions = self.calculate_emissions_per_functional_unit(
            sum(
                (
                    packaging_material["impact_amount"]
                    for packaging_material in self.packaging_materials
                )
            )
        )

        self.total_transport_emissions = self.calculate_emissions_per_functional_unit(
            sum(
                (
                    transport_segment["impact_amount"]
                    for transport_segment in self.transportation
                )
            )
        )
        self.total_use_phase_emissions = self.calculate_emissions_per_functional_unit(
            sum(
                (
                    consumer_use["impact_amount"]
                    for consumer_use in self.consumer_uses
                )
            )
        )
        self.total_manufacturing_emissions = self.calculate_emissions_per_functional_unit(
            sum(
                (
                    manufacturing_process["impact_amount"]
                    for manufacturing_process in self.manufacturing_processes
                )
            )
        )
        self.total_eol_emissions = self.calculate_emissions_per_functional_unit(
            sum(
                (
                    eol["impact_amount"]
                    for eol in self.eol
                )
            )
        )

        self.total_emissions = (
            self.total_raw_materials_emissions
            + self.total_packaging_emissions
            + self.total_transport_emissions
            + self.total_use_phase_emissions
            + self.total_manufacturing_emissions
            + self.total_eol_emissions
        )

    def get_report_meta(self) -> ReportMetaReplacements:
        return ReportMetaReplacements(
            country_of_use=self.data.report_meta.country_of_use,
            unit=self.data.report_meta.unit,
            functional_unit=self.data.report_meta.functional_unit,
            functional_type=self.data.report_meta.functional_type,
            date=self.data.report_meta.date,
        )

    def get_company_info(self) -> CompanyInfoReplacements:
        return CompanyInfoReplacements(
            company_name=self.data.company_meta.company_name,
            manufacturer=self.data.company_meta.manufacturer,
            country_of_origin=self.data.company_meta.country_of_origin,
        )

    def get_product_info(self) -> ProductInfoReplacements:
        return ProductInfoReplacements(
            product_name=self.data.product_info.product_name,
            product_category=self.data.product_info.primary_category,
        )

    def get_raw_materials(self) -> RawMaterialsReplacements:
        raw_materials = []
        for raw_material, raw_material_emissions in zip(
            (
                self.data.aggregation["default"]["materials"]["segment_nodes"]
                if "materials" in self.data.aggregation["default"]
                else []
            ),
            self.raw_materials,
        ):
            amount = UnitType.convert_from_abbrev(
                raw_material["amount"],
                raw_material["unit"],
                "kg",
            )
            raw_materials.append(
                [
                    raw_material["node_name"],
                    f"{amount:.2e}",
                    raw_material_emissions["emissions_factor_value"]["emissions_factor"]["source"],
                ]
            )

        return RawMaterialsReplacements(
            ingredients=raw_materials,
        )

    def get_packaging_material(self) -> PackagingMaterialReplacements:
        packaging_components = []
        for packaging_component, packaging_component_emissions in zip(
            (
                self.data.aggregation["default"]["packaging"]["segment_nodes"]
                if "packaging" in self.data.aggregation["default"]
                else []
            ),
            self.packaging_materials,
        ):
            amount = UnitType.convert_from_abbrev(
                packaging_component["amount"],
                packaging_component["unit"],
                "kg",
            )
            packaging_components.append(
                [
                    packaging_component["node_name"],
                    f"{amount:.2e}",
                    packaging_component_emissions[
                        "emissions_factor_value"
                    ]["emissions_factor"]["source"],
                ]
            )

        return PackagingMaterialReplacements(packaging_components=packaging_components)

    def get_manufacturing(self) -> ManufacturingReplacements:
        manufacturing = [
            (
                manufacturing_process["emissions_factor_value"][
                    "emissions_factor"
                ]["activity_name"],
                manufacturing_process["emissions_factor_value"]["emissions_factor"]["source"],
            )
            for manufacturing_process in self.manufacturing_processes
        ]

        return ManufacturingReplacements(manufacturing=manufacturing)

    def get_transport(self) -> TransportReplacements:
        transport_segments = []
        for transport_segment, transport_segment_emissions in zip(
            (
                self.data.aggregation["default"]["transportation"]["segment_nodes"]
                if "transportation" in self.data.aggregation["default"]
                else []
            ),
            self.transportation,
        ):
            transport_node = next(
                (
                    node for node in self.data.nodes if node.id == transport_segment["node_id"]
                ),
                None
            )
            transport_segments.append(
                (
                    transport_node.name,
                    f"{transport_node.amount:.2e}",
                    transport_segment_emissions["emissions_factor_value"][
                        "emissions_factor"
                    ]["source"],
                )
            )

        return TransportReplacements(transport_segments=transport_segments)

    def get_use_phase(self) -> UsePhaseReplacements:
        consumer_uses = [
            [
                (
                    f"{consumer_use['node_name']} ({consumer_use['amount']:.2e} "
                    f"{consumer_use['unit']})"
                ),
                consumer_use_emissions["emissions_factor_value"]["emissions_factor"]["source"],
            ] for consumer_use, consumer_use_emissions in zip(
                (
                    self.data.aggregation["default"]["use"]["segment_nodes"]
                    if "use" in self.data.aggregation["default"]
                    else []
                ),
                self.consumer_uses,
            )
        ]

        return UsePhaseReplacements(
            consumer_uses=consumer_uses,
        )

    def get_eol(self) -> EOLReplacements:
        eol_materials = [
            [
                eol["node_name"],
                "DEFRA",
            ] for eol in (
                self.data.aggregation["default"]["eol"]["segment_nodes"]
                if "eol" in self.data.aggregation["default"]
                else []
            )
        ]

        return EOLReplacements(
            materials=eol_materials,
        )

    def get_lca_report_overview(self) -> LCAReportOverviewReplacements:
        sorted_activities_by_emissions = sorted(
            [
                ("Raw Materials", self.total_raw_materials_emissions),
                ("Packaging", self.total_packaging_emissions),
                ("Transportation", self.total_transport_emissions),
                ("Use Phase", self.total_use_phase_emissions),
                ("Manufacturing", self.total_manufacturing_emissions),
                ("End-of-Life", self.total_eol_emissions),
            ],
            key=lambda x: x[1],
            reverse=True,
        )

        get_nth_highest_emitting_activity = lambda n: (
            (
                sorted_activities_by_emissions[n][0],
                self._percent_of_total(sorted_activities_by_emissions[n][1]),
            )
            if n < len(sorted_activities_by_emissions)
            else (None, None)
        )

        (
            highest_emitting_stage,
            highest_emitting_stage_percentage
        ) = get_nth_highest_emitting_activity(0)

        (
            second_highest_emitting_stage,
            second_highest_emitting_stage_percentage
        ) = get_nth_highest_emitting_activity(1)

        (
            lowest_emitting_stage,
            lowest_emitting_stage_percentage
        ) = get_nth_highest_emitting_activity(-1)

        if not (
            highest_emitting_stage
            and second_highest_emitting_stage
            and lowest_emitting_stage
        ):
            raise Exception(
                "Cannont generate LCA Report Overview: Not enough activities"
            )

        return LCAReportOverviewReplacements(
            ghg_emissions_kg_co2e_per_functional_unit=self._display_total_emissions(
                self.total_emissions,
            ),
            highest_emitting_stage=highest_emitting_stage,
            highest_emitting_stage_percentage=highest_emitting_stage_percentage,
            second_highest_emitting_stage=second_highest_emitting_stage,
            second_highest_emitting_stage_percentage=second_highest_emitting_stage_percentage,
            lowest_emitting_stage=lowest_emitting_stage,
            lowest_emitting_stage_percentage=lowest_emitting_stage_percentage,
        )

    def get_overview_of_results(self) -> OverviewOfResultsReplacements:
        return OverviewOfResultsReplacements(
            raw_materials_gwp_impact=self._display_total_emissions(
                self.total_raw_materials_emissions,
            ),
            raw_materials_share_of_total=self._percent_of_total(
                self.total_raw_materials_emissions,
            ),
            packaging_materials_gwp_impact=self._display_total_emissions(
                self.total_packaging_emissions,
            ),
            packaging_materials_share_of_total=self._percent_of_total(
                self.total_packaging_emissions,
            ),
            manufacturing_gwp_impact=self._display_total_emissions(
                self.total_manufacturing_emissions,
            ),
            manufacturing_share_of_total=self._percent_of_total(
                self.total_manufacturing_emissions,
            ),
            transport_gwp_impact=self._display_total_emissions(
                self.total_transport_emissions,
            ),
            transport_share_of_total=self._percent_of_total(
                self.total_transport_emissions,
            ),
            use_phase_gwp_impact=self._display_total_emissions(
                self.total_use_phase_emissions,
            ),
            use_phase_share_of_total=self._percent_of_total(
                self.total_use_phase_emissions,
            ),
            eol_gwp_impact=self._display_total_emissions(
                self.total_eol_emissions,
            ),
            eol_share_of_total=self._percent_of_total(
                self.total_eol_emissions,
            ),
            total_gwp_impact=self._display_total_emissions(
                self.total_emissions,
            ),
            packaging_quartile="1",
            manufacturing_quartile="1",
            manufacturing_high_or_low="Low",
            use_phase_quartile="1",
        )

    def _percent_of_total(self, x: float) -> str:
        return f"{((x / self.total_emissions) * 100):.2f}"

    def _display_total_emissions(self, x: float) -> str:
        return f"{x:.4e}"

    def _get_functional_unit(self):
        if self.data.report_meta.unit.lower().strip() in ["per pack", "unit"]:
            functional_unit = 1
        elif self.data.report_meta.unit.lower().strip() == "per use":
            functional_unit = self.data.product_info.uses_per_package
        else:
            functional_unit = self.data.product_info.content_weight / (
                self.data.report_meta.functional_unit
                * self._scale_factor
            )

        return functional_unit

    def calculate_mass_per_functional_unit(self, mass: float) -> float:
        if not mass:
            return 0

        functional_unit = self._get_functional_unit()
        if not functional_unit:
            return 0

        return mass / functional_unit

    def calculate_emissions_per_functional_unit(self, emissions: float) -> float:
        if not emissions:
            return 0

        functional_unit = self._get_functional_unit()
        if not functional_unit:
            return 0

        return emissions / functional_unit
