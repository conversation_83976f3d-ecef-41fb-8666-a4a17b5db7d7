import logging
from typing import List, Dict, Tuple, Any
from datetime import datetime, timedelta
from pydantic import BaseModel
from pipelines_v2.utils.unit import UnitType
from clients.ml_models import MLModels
from .document_adapter_v2 import DocumentAdapterV2, Section, ReplacementType

class ReportMeta(BaseModel):
    product_name: str
    country_of_use: str
    product_category: str
    date: str
    month_and_year_of_study: str = datetime.now().strftime('%B %Y')
    year_of_study: str = datetime.now().strftime('%Y')
    # complete year of study is the previous year
    complete_year_of_study: str = (datetime.now() - timedelta(days=365)).strftime('%Y')

class CompanyMeta(BaseModel):
    company_name: str
    manufacturer: str
    country_of_origin: str

class BifmaLCAReportData(BaseModel):
    lca_output: dict
    company_meta: CompanyMeta
    report_meta: ReportMeta
    product_description: str | None = None
    functional_unit_description: str | None = None

ml_models = MLModels()

class BifmaLCAReportAdapter(DocumentAdapterV2[BifmaLCAReportData]):

    @property
    def _get_lpc_impact_indicators(self):
        """
        Common impact indicator configurations used across the adapter.
        """
        return {
            "energy": {
                "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                "category": "energy resources: non-renewable, fossil",
                "indicator": "fossil fuel potential (FFP)",
                "label": "[Top 5 Energy Table]"
            },
            "water": {
                "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                "category": "water use",
                "indicator": "water consumption potential (WCP)",
                "label": "[Top 5 Water Table]"
            },
            "carbon": {
                "lcia_method": "TRACI v2.1",
                "category": "climate change",
                "indicator": "global warming potential (GWP100)",
                "label": "[Top 5 Carbon Footprint Table]"
            }
        }

    def _get_all_impacts(self) -> List[dict]:
        return [
            impact["emissions_factor_value"]["impact_indicator"]
            for impact in list(self.data.lca_output["BIFMA"].values())[
                0
            ]["segment_nodes"][0]["impacts"]
        ]
    def _get_all_impact_indicators_table(self) -> List[Section]:
        # Create list of tuples with impact indicator methods, categories, and indicators
        impact_indicators = self._get_all_impacts()
        values = [
            (impact["lcia_method"], impact["category"], impact["indicator"], impact["unit"])
            for impact in impact_indicators
        ]
        values.sort()
        return [
            Section(
                label="[Impact Indicators Table]",
                values=values,
                replacement_type=ReplacementType.TABLE
            )
        ]

    def _get_overview_table(self) -> List[Section]:
        values = []
        impact_indicators = self._get_all_impacts()

        for impact_indicator in impact_indicators:
            value = [
                impact_indicator["lcia_method"],
                f"{impact_indicator['indicator']} - {impact_indicator['unit']}"
            ]
            sorted_segments = sorted(
                self.data.lca_output["BIFMA"].values(),
                key=lambda segment: segment["sequence_number"]
            )
            for segment in sorted_segments:
                total_impact = sum(
                    next(
                        (
                            impact["impact_amount"]
                            for impact in node["impacts"]
                            if impact["emissions_factor_value"]["impact_indicator"]["id"]
                            == impact_indicator["id"]
                        ),
                        0
                    )
                    for node in segment["segment_nodes"]
                )
                value.append(f"{total_impact:.2e}")

            value.append(f"{sum(float(v) for v in value[2:]):.2e}")
            values.append(value)

        return [
            Section(
                label="[Overview Table]",
                values=values,
                replacement_type=ReplacementType.TABLE
            )
        ]

    def _get_lpc_sections(self) -> List[Section]:
        impact_indicators = self._get_lpc_impact_indicators

        def calculate_impacts(indicator_config):
            impacts = self._get_impacts_for_indicator(indicator_config)
            total_impact = sum(impacts.values())
            impact_list = [
                [activity_name, f"{((impact / total_impact) * 100):.2f}", f"{impact:.2e}"]
                for activity_name, impact in impacts.items()
            ]

            sorted_impacts = sorted(impact_list, key=lambda x: float(x[1]), reverse=True)

            return Section(
                label=indicator_config["label"],
                values=sorted_impacts[:5],
                replacement_type=ReplacementType.TABLE
            )

        sections = []
        for indicator_config in impact_indicators.values():
            sections.append(calculate_impacts(indicator_config))

        return sections

    def _get_impacts_for_indicator(self, indicator_config):
        """
        Get impacts for a specific indicator configuration.
        Returns a dictionary mapping activity names to their impact values.
        """
        impacts = {}
        for segment in self.data.lca_output["BIFMA"].values():
            for node in segment["segment_nodes"]:
                for impact in node["impacts"]:
                    impact_indicator = impact["emissions_factor_value"]["impact_indicator"]
                    if (
                        impact_indicator["lcia_method"] == indicator_config["lcia_method"]
                        and impact_indicator["category"] == indicator_config["category"]
                        and impact_indicator["indicator"] == indicator_config["indicator"]
                    ):
                        emissions_factor = impact["emissions_factor_value"]["emissions_factor"]
                        amount = impact["impact_amount"]
                        activity_name = emissions_factor["activity_name"]
                        impacts[activity_name] = impacts.get(activity_name, 0) + amount

        return impacts

    def _get_report_meta_sections(self) -> List[Section]:
        logging.info("[BifmaLCAReportAdapter] Product Category being inserted: %r",
                    self.data.report_meta.product_category)
        return [
            Section(
                label="[Product Name]",
                values=self.data.report_meta.product_name,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Product Category]",
                values=self.data.report_meta.product_category or "",
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Country of Use]",
                values=self.data.report_meta.country_of_use,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Date]",
                values=self.data.report_meta.date,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Month & Year of Study]",
                values=self.data.report_meta.month_and_year_of_study,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Year of Study]",
                values=self.data.report_meta.year_of_study,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Complete Year of Study]",
                values=self.data.report_meta.complete_year_of_study,
                replacement_type=ReplacementType.TEXT
            ),
        ]

    def _get_company_sections(self) -> List[Section]:
        return [
            Section(
                label="[Company Name]",
                values=self.data.company_meta.company_name,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Company]",
                values=self.data.company_meta.company_name,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Manufacturer]",
                values=self.data.company_meta.manufacturer,
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Country of Origin]",
                values=self.data.company_meta.country_of_origin,
                replacement_type=ReplacementType.TEXT
            ),
        ]

    def _process_nodes(
        self,
        nodes: List[dict],
        label: str,
        unit_type: UnitType | None = None,
        deduplicate: bool = False,
    ) -> List[Section]:
        """
        Process nodes to create a table section with node data.

        Args:
            nodes: List of node dictionaries to process
            label: Label for the resulting table section
            unit_type: Optional UnitType to convert amounts to. If provided, includes
                      amount in the output data.
            deduplicate: Whether to deduplicate nodes with identical materials and activities

        Returns:
            List containing a single Section with the processed table data
        """
        # Apply deduplication if requested (primarily for material nodes)
        if deduplicate and nodes:
            logging.debug("Deduplicating %d nodes for %s", len(nodes), label)
            nodes = self._deduplicate_material_nodes(nodes, unit_type)
            logging.debug("After deduplication: %d nodes for %s", len(nodes), label)

        processed_data = []
        for node in nodes:
            self._assert_node_has_impacts(node)

            # Get emissions factor activity name (common for both cases)
            emissions_factor_activity_name = (
                node["impacts"][0]["emissions_factor_value"]["emissions_factor"]["activity_name"]
            )

            if unit_type:
                # Convert amount and include it in output for quantity-based nodes
                converted_amount = UnitType.convert_from_abbrev(
                    node["amount"],
                    node["unit"],
                    unit_type.value.abbrev
                )

                # Use the original node name without indicating combined entries
                processed_data.append(
                    (node["node_name"], f"{converted_amount:.2f}", emissions_factor_activity_name)
                )
            else:
                # Simple case with just name and emissions factor
                processed_data.append(
                    (node["node_name"], emissions_factor_activity_name)
                )

        return [
            Section(
                label=label,
                values=processed_data,
                replacement_type=ReplacementType.TABLE
            )
        ]

    def _get_waste_disposal_sections(self) -> List[Section]:
        """
        Get waste disposal sections with deduplicated materials.

        This method retrieves waste disposal nodes and deduplicates them
        to combine identical materials with the same activity.
        """
        waste_disposal_nodes = (
            self.data.lca_output["BIFMA"]["Waste Disposal"]["segment_nodes"]
        )
        return self._process_nodes(
            nodes=waste_disposal_nodes,
            label="[Waste Disposal Table]",
            deduplicate=True  # Enable deduplication for waste disposal
        )

    def _get_material_transformation_sections(self) -> List[Section]:
        """
        Get material transformation sections with deduplicated materials.

        This method retrieves material transformation nodes and deduplicates them
        to combine identical materials with the same activity, geography, and source.
        The output does not include the weight column, only material name and activity.
        """
        material_transformation_nodes = (
            self.data.lca_output["BIFMA"]["Material Transformation"]["segment_nodes"]
        )
        # Use unit_type for deduplication but don't include it in the output
        nodes = material_transformation_nodes
        if nodes:
            logging.debug("Deduplicating %d nodes for Material Transformations", len(nodes))
            nodes = self._deduplicate_material_nodes(nodes, UnitType.GRAM)
            logging.debug("After deduplication: %d nodes for Material Transformations", len(nodes))

        processed_data = []
        for node in nodes:
            self._assert_node_has_impacts(node)

            # Get emissions factor activity name
            emissions_factor_activity_name = (
                node["impacts"][0]["emissions_factor_value"]["emissions_factor"]["activity_name"]
            )

            # Only include material name and activity (no weight)
            processed_data.append(
                (node["node_name"], emissions_factor_activity_name)
            )

        return [
            Section(
                label="[Material Transformations Table]",
                values=processed_data,
                replacement_type=ReplacementType.TABLE
            )
        ]

    def _get_raw_materials_sections(self) -> List[Section]:
        """
        Get raw materials sections with deduplicated materials.

        This method retrieves material extraction nodes and deduplicates them
        to combine identical materials with the same activity, geography, and source.
        """
        raw_material_nodes = self.data.lca_output["BIFMA"]["Material Extraction"]["segment_nodes"]
        return self._process_nodes(
            nodes=raw_material_nodes,
            unit_type=UnitType.GRAM,
            label="[Raw Materials Table]",
            deduplicate=True  # Enable deduplication for raw materials
        )

    def _get_material_transportation_sections(self) -> List[Section]:
        """
        Get material transportation sections with deduplicated materials.

        This method retrieves material transportation nodes and deduplicates them
        to combine identical materials with the same activity, geography, and source.
        The ton*km values are summed for identical materials.
        """
        material_transportation_nodes = (
            self.data.lca_output["BIFMA"]["Material Transport"]["segment_nodes"]
        )
        return self._process_nodes(
            nodes=material_transportation_nodes,
            label="[Transport To Factory Table]",
            unit_type=UnitType.METRIC_TON_KILOMETER,
            deduplicate=True  # Enable deduplication for material transportation
        )

    def _get_transport_to_consumer_sections(self) -> List[Section]:
        """
        Get transport to consumer sections with deduplicated materials.

        This method retrieves transport to consumer nodes and deduplicates them
        to combine identical materials with the same activity, geography, and source.
        The ton*km values are summed for identical materials.
        """
        transport_to_consumer_nodes = (
            self.data.lca_output["BIFMA"]["Transport to Consumer"]["segment_nodes"]
        )
        return self._process_nodes(
            nodes=transport_to_consumer_nodes,
            label="[Trpt To Consumer Table]",
            unit_type=UnitType.METRIC_TON_KILOMETER,
            deduplicate=True  # Enable deduplication for transport to consumer
        )
    # pylint: disable=line-too-long
    def _get_secondary_dataset_quality_reference_table(self) -> List[Section]:
        raw_material_nodes = self.data.lca_output["BIFMA"]["Material Extraction"]["segment_nodes"]
        values = []
        for node in raw_material_nodes:
            match_quality = "Appropriate technology"
            values.append(
                (
                    node["node_name"],
                    f'{node["impacts"][0]["emissions_factor_value"]["emissions_factor"]["activity_name"]} ({node["impacts"][0]["emissions_factor_value"]["emissions_factor"]["source"]})',
                    "1",
                    "1",
                    match_quality,
                    "1",
                    "2"
                )
            )

        return [
            Section(
                label="[Secondary dataset quality reference Table]",
                values=values,
                replacement_type=ReplacementType.TABLE
            )
        ]

    def _assert_node_has_impacts(self, node: dict):
        assert len(node["impacts"]) > 0, "No impacts found for node: " + node["node_name"]

    def _deduplicate_material_nodes(
        self, nodes: List[dict], unit_type: UnitType = None
    ) -> List[dict]:
        """
        Deduplicate material nodes by grouping identical materials and summing amounts.

        Materials are considered identical if they have the same name (case-insensitive),
        activity name, geography, and source.

        Args:
            nodes: List of material nodes from lca_output
            unit_type: Optional UnitType to use for conversion. If None, defaults to GRAM.

        Returns:
            List of deduplicated nodes with summed amounts
        """
        if not nodes:
            return []

        # Default to GRAM if no unit_type is provided
        target_unit = unit_type.value.abbrev if unit_type else UnitType.GRAM.value.abbrev

        grouped_nodes: Dict[Tuple[str, str, str, str], Dict[str, Any]] = {}

        for node in nodes:
            # Skip nodes with missing impacts
            if not node.get("impacts") or len(node.get("impacts", [])) == 0:
                continue

            # Create grouping key from normalized values
            key = (
                node["node_name"].lower().strip(),  # Normalize material name
                node.get("impacts", [{}])[0].get("emissions_factor_value", {})
                    .get("emissions_factor", {}).get("activity_name", "").lower().strip(),
                node.get("impacts", [{}])[0].get("emissions_factor_value", {})
                    .get("emissions_factor", {}).get("geography", "").lower().strip(),
                node.get("impacts", [{}])[0].get("emissions_factor_value", {})
                    .get("emissions_factor", {}).get("source", "").lower().strip()
            )

            if key not in grouped_nodes:
                grouped_nodes[key] = {
                    "node": node.copy(),
                    "source_nodes": [],
                    "total_amount": 0
                }            # Handle missing or invalid amount/unit values
            try:
                # Convert all amounts to the target unit before summing
                amount = UnitType.convert_from_abbrev(
                    node["amount"],
                    node["unit"],
                    target_unit
                )
                grouped_nodes[key]["total_amount"] += amount
            except (ValueError, KeyError, TypeError) as error:
                logging.error(
                    "Unit conversion failed for node '%s' (amount=%s, unit=%s, target=%s): %s",
                    node.get('node_name', 'unknown'),
                    node.get('amount'),
                    node.get('unit'),
                    target_unit,
                    str(error)
                )
                # Skip nodes with invalid unit conversions rather than defaulting to 0
                continue

            grouped_nodes[key]["source_nodes"].append(node.get("id", "unknown"))

        # Create deduplicated nodes list
        deduplicated_nodes = []
        for group_data in grouped_nodes.values():
            if group_data["total_amount"] > 0:  # Only include nodes with positive amounts
                deduplicated_node = group_data["node"].copy()
                deduplicated_node.update({
                    "amount": group_data["total_amount"],
                    "unit": target_unit,
                    "_source_nodes": group_data["source_nodes"],
                    "_original_count": len(group_data["source_nodes"])
                })
                deduplicated_nodes.append(deduplicated_node)

        return deduplicated_nodes

    def _get_functional_unit_section(self) -> List[Section]:
        # If a custom functional unit description is provided, use it
        if self.data.functional_unit_description:
            logging.debug("[BifmaLCAReportAdapter] Using custom functional unit description: %r",
                         self.data.functional_unit_description)
            functional_unit_text = self.data.functional_unit_description
        else:
            # Otherwise, fall back to the category-based mapping
            functional_unit_mapping = {
                "Desks": (
                    "The functional unit is one square meter (1 m2) of "
                    "physical floor space (workspace and storage) for a period of 10 years."
                ),
                "Office Chairs": (
                    "The functional unit is one unit of seating to seat "
                    "single or multiple occupants, maintained for a 10-yr period."
                )
            }

            # Get the default text for the product category, or use a generic fallback if not found
            category = self.data.report_meta.product_category
            category_lower = category.lower()
            period = "10-yr period"
            unit_prefix = "The functional unit is one unit of "
            default_text = f"{unit_prefix}{category_lower} maintained for a {period}."
            functional_unit_text = functional_unit_mapping.get(category, default_text)

        return [
            Section(
                label="[Functional Unit Description]",
                values=functional_unit_text,
                replacement_type=ReplacementType.TEXT
            ),
        ]

    def _get_summary_sections(self) -> List[Section]:

        gwp_indicator_config = self._get_lpc_impact_indicators["carbon"].copy()
        energy_indicator_config = self._get_lpc_impact_indicators["energy"].copy()
        water_indicator_config = self._get_lpc_impact_indicators["water"].copy()

        # Calculate top processes for each impact category
        def get_top_process(indicator_config):
            total_impact = 0
            impacts = self._get_impacts_for_indicator(indicator_config)

            if not impacts:
                return {"name": "N/A", "percentage": 0}

            total_impact = sum(impacts.values())

            # Find the top process
            top_process = max(impacts.items(), key=lambda x: x[1]) if impacts else ("N/A", 0)

            return {
                "name": top_process[0],
                "percentage": (top_process[1] / total_impact) * 100 if total_impact > 0 else 0
            }

        top_carbon_process = get_top_process(gwp_indicator_config)
        top_energy_process = get_top_process(energy_indicator_config)
        top_water_process = get_top_process(water_indicator_config)

        sections = [
            Section(
                label="[Top Carbon Process]",
                values=f"{top_carbon_process['name']}",
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[% Top Carbon Process]",
                values=f"{top_carbon_process['percentage']:.2f}",
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Top Energy Process]",
                values=f"{top_energy_process['name']}",
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[% Top Energy Process]",
                values=f"{top_energy_process['percentage']:.2f}",
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[Top Water Process]",
                values=f"{top_water_process['name']}",
                replacement_type=ReplacementType.TEXT
            ),
            Section(
                label="[% Top Water Process]",
                values=f"{top_water_process['percentage']:.2f}",
                replacement_type=ReplacementType.TEXT
            ),
        ]

        return sections

    def _get_product_description_section(self) -> List[Section]:
        logging.debug("[BifmaLCAReportAdapter] Product Description being inserted: %r",
                     self.data.product_description)
        return [
            Section(
                label="[Product Description]",
                values=self.data.product_description or "",
                replacement_type=ReplacementType.TEXT
            )
        ]

    def get_sections(self) -> List[Section]:
        return [
            *self._get_report_meta_sections(),
            *self._get_company_sections(),
            *self._get_overview_table(),
            *self._get_lpc_sections(),
            *self._get_summary_sections(),
            *self._get_raw_materials_sections(),
            *self._get_material_transformation_sections(),
            *self._get_material_transportation_sections(),
            *self._get_functional_unit_section(),
            *self._get_transport_to_consumer_sections(),
            *self._get_waste_disposal_sections(),
            *self._get_all_impact_indicators_table(),
            *self._get_secondary_dataset_quality_reference_table(),
            *self._get_product_description_section(),
        ]

    def get_template_filename(self) -> str:
        return "bifma_lca_report_template.docx"
