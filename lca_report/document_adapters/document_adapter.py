from abc import ABC, abstractmethod
from typing import TypeVar, Generic
from lca_report.replacements import (
    CompanyInfoReplacements,
    EOLReplacements,
    LCAReportOverviewReplacements,
    ManufacturingReplacements,
    OverviewOfResultsReplacements,
    PackagingMaterialReplacements,
    ProductInfoReplacements,
    RawMaterialsReplacements,
    ReportMetaReplacements,
    TransportReplacements,
    UsePhaseReplacements,
)
from lca_report.lca_report_document import LCAReportDocument

T = TypeVar("T")

class DocumentAdapter(ABC, Generic[T]):
    """
    Abstract base class for creating LCA reports from generic data objects.
    
    This class serves as an interface for document adapters that can
    convert or adapt various data sources into a formatted LCA report.
    
    Attributes:
        data (Any): The generic data object to be used as a source for the report.
    
    Note:
        Subclasses should provide implementations for any abstract methods
        defined in this class to ensure proper generation of the LCA report.
    """

    LCA_REPORT_NAME = "lca_output"

    def __init__(self, data: T) -> None:
        self.data = data

    @abstractmethod
    def calculate_mass_per_functional_unit(self, mass: float) -> float:
        """Calculate mass (g) per functional unit."""

    @abstractmethod
    def calculate_emissions_per_functional_unit(self, emissions: float) -> float:
        """Calculate emissions (kg CO2e) per functional unit."""

    @abstractmethod
    def get_report_meta(self) -> ReportMetaReplacements:
        """Map data to ReportMetaReplacements object."""

    @abstractmethod
    def get_company_info(self) -> CompanyInfoReplacements:
        """Map data to CompanyInfoReplacements object."""

    @abstractmethod
    def get_product_info(self) -> ProductInfoReplacements:
        """Map data to ProductInfoReplacements object."""

    @abstractmethod
    def get_lca_report_overview(self) -> LCAReportOverviewReplacements:
        """Map data to LCAReportOverviewReplacements object."""

    @abstractmethod
    def get_raw_materials(self) -> RawMaterialsReplacements:
        """Map data to RawMaterialsReplacements object."""

    @abstractmethod
    def get_packaging_material(self) -> PackagingMaterialReplacements:
        """Map data to PackagingMaterialReplacements object."""

    @abstractmethod
    def get_manufacturing(self) -> ManufacturingReplacements:
        """Map data to ManufacturingReplacements object."""

    @abstractmethod
    def get_transport(self) -> TransportReplacements:
        """Map data to TransportReplacements object."""

    @abstractmethod
    def get_use_phase(self) -> UsePhaseReplacements:
        """Map data to UsePhaseReplacements object."""

    @abstractmethod
    def get_eol(self) -> EOLReplacements:
        """Map data to EOLReplacements object."""

    @abstractmethod
    def get_overview_of_results(self) -> OverviewOfResultsReplacements:
        """Map data to OverviewOfResultsReplacements object."""

    def create_lca_report(self, report_name: str = LCA_REPORT_NAME) -> str:
        """Create LCA report."""

        report_meta_replacements = self.get_report_meta()
        company_info_replacements = self.get_company_info()
        product_info_replacements = self.get_product_info()
        raw_materials_replacements = self.get_raw_materials()
        packaging_material_replacements = self.get_packaging_material()
        manufacturing_replacements = self.get_manufacturing()
        transport_replacements = self.get_transport()
        use_phase_replacements = self.get_use_phase()
        eol_replacements = self.get_eol()
        lca_report_overview_replacements = self.get_lca_report_overview()
        overview_of_results_replacements = self.get_overview_of_results()

        report = LCAReportDocument(
            report_meta_replacements=report_meta_replacements,
            company_info_replacements=company_info_replacements,
            product_info_replacements=product_info_replacements,
            lca_report_overview_replacements=lca_report_overview_replacements,
            raw_materials_replacements=raw_materials_replacements,
            packaging_material_replacements=packaging_material_replacements,
            manufacturing_replacements=manufacturing_replacements,
            transport_replacements=transport_replacements,
            use_phase_replacements=use_phase_replacements,
            eol_replacements=eol_replacements,
            overview_of_results_replacements=overview_of_results_replacements,
        )

        return report.create_report(report_name)
