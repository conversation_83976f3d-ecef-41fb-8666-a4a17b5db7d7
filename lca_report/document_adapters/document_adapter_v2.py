import os
from enum import Enum
from abc import ABC, abstractmethod
from typing import Type<PERSON><PERSON>, Generic, Any, List
from pydantic import BaseModel
from docx import Document
from lca_report.replacements.template_value_replacements import (
    replace_placeholder_in_paragraph,
    insert_chart_after_paragraph
)

class ReplacementType(Enum):
    TEXT = "text"
    TABLE = "table"
    CHART = "chart"

class Section(BaseModel):
    label: str
    values: Any
    replacement_type: ReplacementType

    def replace_in_document(self, doc):
        if self.replacement_type == ReplacementType.TEXT:
            self._replace_values(doc)
        elif self.replacement_type == ReplacementType.TABLE:
            self._insert_values_into_table(doc)
        else:
            raise ValueError(f"Unsupported replacement type: {self.replacement_type}")

    def _replace_values(self, doc):
        replacements = {
            self.label: self.values,
        }

        for paragraph in doc.paragraphs:
            replace_placeholder_in_paragraph(paragraph, replacements)

        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        replace_placeholder_in_paragraph(paragraph, replacements)

    def _insert_values_into_table(
        self,
        doc,
    ):
        for table in doc.tables:
            if len(table.rows) < 2:
                continue

            if self.label in table.cell(1, 0).text:
                if not self.values:
                    table.cell(1, 0).text = "N/A"
                    continue

                num_rows = len(self.values)
                existing_rows = len(table.rows)

                while existing_rows < num_rows + 1:
                    table.add_row()
                    existing_rows += 1

                column_widths = []
                for col in table.columns:
                    try:
                        width = col.width
                        if width is not None:
                            col.width = int(width)  # Ensure width is an integer
                        column_widths.append(col.width)
                    except ValueError:
                        col.width = 3000

                for i, row_data in enumerate(self.values):
                    for j, cell_data in enumerate(row_data):
                        table.cell(i + 1, j).text = cell_data

    def insert_chart(
        self,
        doc,
        placeholder: str,
        title: str,
        labels: List[str],
        data: List[Any],
    ):
        for paragraph in doc.paragraphs:
            if placeholder in paragraph.text:
                insert_chart_after_paragraph(paragraph, title, labels, data)
                paragraph.clear()

T = TypeVar("T")

class DocumentAdapterV2(ABC, Generic[T]):
    """
    Abstract base class for creating LCA reports from generic data objects.
    """
    LCA_REPORT_NAME = "lca_output"

    def __init__(self, data: T) -> None:
        self.data = data

    @abstractmethod
    def get_sections(self) -> List[Section]:
        """
        Get the sections of the report.
        """

    @abstractmethod
    def get_template_filename(self) -> str:
        """
        Get the filename of the template file.
        """

    def create_lca_report(self, report_name: str = LCA_REPORT_NAME) -> str:
        dir_path = os.path.dirname(os.path.dirname(os.path.realpath(__file__)))
        template_path = os.path.join(dir_path, 'templates', self.get_template_filename())
        template = Document(template_path)

        for section in self.get_sections():
            section.replace_in_document(template)

        output_path = os.path.join(dir_path, 'output', f'{report_name}.docx')

        if os.path.exists(output_path):
            os.remove(output_path)

        template.save(output_path)

        return output_path
