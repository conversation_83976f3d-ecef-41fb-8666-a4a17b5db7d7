# pylint: disable=protected-access
import unittest
from typing import Dict, Any
from pipelines_v2.utils.unit import UnitType
from lca_report.document_adapters.bifma_document_adapter import (
    BifmaLCAReportAdapter,
    BifmaLCAReportData,
    ReportMeta,
    CompanyMeta,
)


def create_test_node(
    name: str,
    amount: float,
    unit: str,
    activity_name: str,
    geography: str = "GLO",
    source: str = "ecoinvent",
    node_id: str = None
) -> Dict[str, Any]:
    """
    Helper function to create test nodes with consistent structure.

    Args:
        name: Material name
        amount: Material amount
        unit: Unit of measurement
        activity_name: Activity name for the emissions factor
        geography: Geography code (default: "GLO")
        source: Source database (default: "ecoinvent")
        node_id: Optional node ID (default: generated from name)

    Returns:
        A dictionary representing a node with the specified properties
    """
    return {
        "node_name": name,
        "amount": amount,
        "unit": unit,
        "id": node_id or f"node_{name.lower().replace(' ', '_')}",
        "impacts": [
            {
                "emissions_factor_value": {
                    "emissions_factor": {
                        "activity_name": activity_name,
                        "geography": geography,
                        "source": source
                    },
                    "impact_indicator": {
                        "id": "test_impact_id",
                        "lcia_method": "TRACI v2.1",
                        "category": "climate change",
                        "indicator": "global warming potential (GWP100)",
                        "unit": "kg CO2 eq"
                    }
                },
                "impact_amount": 1.0
            }
        ]
    }


class TestBifmaDocumentAdapter(unittest.TestCase):

    def setUp(self):
        """Set up test fixtures."""
        # Create mock data for the adapter
        self.report_meta = ReportMeta(
            product_name="Test Product",
            country_of_use="US",
            product_category="Office Chairs",
            date="2023-05-17"
        )

        self.company_meta = CompanyMeta(
            company_name="Test Company",
            manufacturer="Test Manufacturer",
            country_of_origin="US"
        )

        # Create a minimal mock LCA output structure
        self.lca_output = {
            "BIFMA": {
                "Material Extraction": {
                    "segment_nodes": []  # Will be populated in tests
                },
                "Material Transformation": {
                    "segment_nodes": []  # Will be populated in tests
                },
                "Material Transport": {
                    "segment_nodes": []  # Will be populated in tests
                },
                "Transport to Consumer": {
                    "segment_nodes": []  # Will be populated in tests
                },
                "Waste Disposal": {
                    "segment_nodes": []  # Will be populated in tests
                }
            }
        }

        # Create the adapter with mock data
        self.report_data = BifmaLCAReportData(
            lca_output=self.lca_output,
            company_meta=self.company_meta,
            report_meta=self.report_meta
        )

        self.adapter = BifmaLCAReportAdapter(self.report_data)

    def test_basic_material_deduplication(self):
        """Test deduplication of identical materials with same activity."""
        # Create test nodes with the same material and activity
        nodes = [
            create_test_node("Steel", 100, "g", "Steel production"),
            create_test_node("Steel", 200, "g", "Steel production")
        ]

        # Test deduplication
        result = self.adapter._deduplicate_material_nodes(nodes)

        # Verify results
        self.assertEqual(len(result), 1, "Should deduplicate to a single node")
        self.assertEqual(result[0]["amount"], 300, "Amounts should be summed")
        self.assertEqual(result[0]["unit"], "g", "Unit should be grams")
        self.assertEqual(len(result[0]["_source_nodes"]), 2,
                         "Should track source nodes")
        self.assertEqual(result[0]["_original_count"], 2,
                         "Should count original nodes")

    def test_unit_conversion_during_deduplication(self):
        """Test deduplication with mixed units."""
        # Create test nodes with different units
        nodes = [
            create_test_node("Steel", 1, "kg", "Steel production"),
            create_test_node("Steel", 500, "g", "Steel production")
        ]

        # Test deduplication
        result = self.adapter._deduplicate_material_nodes(nodes)

        # Verify results
        self.assertEqual(len(result), 1, "Should deduplicate to a single node")
        self.assertEqual(result[0]["amount"], 1500,
                         "Amounts should be converted to g and summed")
        self.assertEqual(result[0]["unit"], "g",
                         "Unit should be standardized to grams")

    def test_different_materials_not_deduplicated(self):
        """Test that different materials are not deduplicated."""
        # Create test nodes with different materials
        nodes = [
            create_test_node("Steel", 100, "g", "Steel production"),
            create_test_node("Aluminum", 200, "g", "Aluminum production")
        ]

        # Test deduplication
        result = self.adapter._deduplicate_material_nodes(nodes)

        # Verify results
        self.assertEqual(len(result), 2,
                         "Different materials should not be deduplicated")

    def test_same_material_different_activities_not_deduplicated(self):
        """Test that same materials with different activities are not deduplicated."""
        # Create test nodes with same material but different activities
        nodes = [
            create_test_node("Steel", 100, "g", "Steel production"),
            create_test_node("Steel", 200, "g", "Steel recycling")
        ]

        # Test deduplication
        result = self.adapter._deduplicate_material_nodes(nodes)

        # Verify results
        error_msg = ("Same material with different activities "
                     "should not be deduplicated")
        self.assertEqual(len(result), 2, error_msg)

    def test_case_insensitive_deduplication(self):
        """Test that deduplication is case-insensitive."""
        # Create test nodes with different case
        nodes = [
            create_test_node("Steel", 100, "g", "Steel production"),
            create_test_node("STEEL", 200, "g", "Steel production"),
            create_test_node("steel", 300, "g", "steel production")
        ]

        # Test deduplication
        result = self.adapter._deduplicate_material_nodes(nodes)

        # Verify results
        self.assertEqual(len(result), 1,
                         "Case differences should be ignored in deduplication")
        self.assertEqual(result[0]["amount"], 600, "All amounts should be summed")

    def test_empty_node_list(self):
        """Test handling of empty node list."""
        result = self.adapter._deduplicate_material_nodes([])
        self.assertEqual(result, [], "Empty list should return empty list")

    def test_missing_impacts(self):
        """Test handling of nodes with missing impacts."""
        # Create a node with missing impacts
        node_with_missing_impacts = {
            "node_name": "Broken Node",
            "amount": 100,
            "unit": "g",
            "id": "broken_node",
            "impacts": []  # Empty impacts
        }

        # Create a valid node
        valid_node = create_test_node("Valid Node", 200, "g", "Valid activity")

        # Test deduplication with mixed nodes
        result = self.adapter._deduplicate_material_nodes(
            [node_with_missing_impacts, valid_node]
        )

        # Verify results
        self.assertEqual(len(result), 1, "Only valid nodes should be included")
        self.assertEqual(result[0]["node_name"], "Valid Node",
                         "Valid node should be preserved")

    def test_process_nodes_with_deduplication(self):
        """Test the process_nodes method with deduplication enabled."""
        # Create test nodes
        nodes = [
            create_test_node("Steel", 100, "g", "Steel production"),
            create_test_node("Steel", 200, "g", "Steel production"),
            create_test_node("Aluminum", 300, "g", "Aluminum production")
        ]

        # Process nodes with deduplication
        sections = self.adapter._process_nodes(
            nodes=nodes,
            label="[Test Table]",
            unit_type=UnitType.GRAM,
            deduplicate=True
        )

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 2,
                         "Should have two rows after deduplication")

        # Check that the combined node has the correct format
        steel_row = next(
            (row for row in sections[0].values if "Steel" in row[0]),
            None
        )
        self.assertIsNotNone(steel_row, "Steel row should exist")
        self.assertEqual(steel_row[0], "Steel",
                         "Should use the original material name")
        self.assertEqual(steel_row[1], "300.00", "Should show combined amount")


    def test_material_transformation_deduplication(self):
        """Test deduplication in the material transformation section."""
        # Create test nodes with duplicate material transformations
        nodes = [
            create_test_node("Hardware", 100, "g",
                             "metal working, average for steel product manufacturing"),
            create_test_node("Hardware", 200, "g",
                             "metal working, average for steel product manufacturing"),
            create_test_node("HARDWARE", 300, "g",
                             "metal working, average for steel product manufacturing"),
            create_test_node("Injection Molded", 150, "g",
                             "injection moulding (customized for GLO)"),
            create_test_node("INJECTION MOLDED", 250, "g",
                             "injection moulding (customized for GLO)"),
            create_test_node("Aluminum", 400, "g", "aluminium production, primary")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Material Transformation"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_material_transformation_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 3,
                         "Should have three rows after deduplication")

        # Check that the combined nodes have the correct format
        hardware_row = next(
            (row for row in sections[0].values if "Hardware" in row[0]),
            None
        )
        injection_row = next(
            (row for row in sections[0].values if "Injection Molded" in row[0]),
            None
        )
        aluminum_row = next(
            (row for row in sections[0].values if "Aluminum" in row[0]),
            None
        )

        # Verify hardware deduplication
        self.assertIsNotNone(hardware_row, "Hardware row should exist")
        self.assertEqual(hardware_row[0], "Hardware",
                         "Should use the original material name")
        self.assertEqual(
            hardware_row[1],
            "metal working, average for steel product manufacturing",
            "Should preserve activity name"
        )

        # Verify injection molded deduplication
        self.assertIsNotNone(injection_row, "Injection Molded row should exist")
        self.assertEqual(
            injection_row[1],
            "injection moulding (customized for GLO)",
            "Should preserve activity name"
        )

        # Verify aluminum (not deduplicated)
        self.assertIsNotNone(aluminum_row, "Aluminum row should exist")
        self.assertEqual(
            aluminum_row[1],
            "aluminium production, primary",
            "Should preserve activity name"
        )

    def test_material_transformation_unit_conversion(self):
        """Test unit conversion in material transformation deduplication."""
        # Create test nodes with different units
        nodes = [
            create_test_node("Hardware", 0.5, "kg",
                             "metal working, average for steel product manufacturing"),
            create_test_node("Hardware", 250, "g",
                             "metal working, average for steel product manufacturing")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Material Transformation"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_material_transformation_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 1,
                         "Should have one row after deduplication")

        # Check that the combined node has the correct format (no weight shown)
        hardware_row = sections[0].values[0]
        self.assertEqual(hardware_row[0], "Hardware",
                         "Should use the original material name")
        self.assertEqual(
            hardware_row[1],
            "metal working, average for steel product manufacturing",
            "Should preserve activity name"
        )

    def test_material_transformation_edge_cases(self):
        """Test edge cases in material transformation deduplication."""
        # Create a node with missing impacts
        node_with_missing_impacts = {
            "node_name": "Broken Node",
            "amount": 100,
            "unit": "g",
            "id": "broken_node",
            "impacts": []  # Empty impacts
        }

        # Create valid nodes
        valid_node1 = create_test_node(
            "Hardware", 100, "g",
            "metal working, average for steel product manufacturing"
        )
        valid_node2 = create_test_node(
            "Hardware", 200, "g",
            "metal working, average for steel product manufacturing"
        )

        # Set up the test data with a mix of valid and invalid nodes
        nodes = [node_with_missing_impacts, valid_node1, valid_node2]
        self.lca_output["BIFMA"]["Material Transformation"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_material_transformation_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 1,
             "Should have one row after deduplication and filtering")

        # Check that only valid nodes were processed and deduplicated
        hardware_row = sections[0].values[0]
        self.assertEqual(hardware_row[0], "Hardware",
                         "Should use the original material name")
        self.assertEqual(
            hardware_row[1],
            "metal working, average for steel product manufacturing",
            "Should preserve activity name"
        )

    def test_material_transportation_deduplication(self):
        """Test deduplication in the material transportation section."""
        # Create test nodes with duplicate material transportation
        nodes = [
            create_test_node("Metal (Steel)", 10, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("Metal (Steel)", 15, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("METAL (STEEL)", 5, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("Polymeric Material (PU)", 8, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("Polymeric Material (PU)", 12, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Material Transport"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_material_transportation_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 2,
                         "Should have two rows after deduplication")

        # Check that the combined nodes have the correct format
        steel_row = next(
            (row for row in sections[0].values if "Metal (Steel)" in row[0]),
            None
        )
        pu_row = next(
            (row for row in sections[0].values
             if "Polymeric Material (PU)" in row[0]),
            None
        )

        # Verify steel deduplication
        self.assertIsNotNone(steel_row, "Steel row should exist")
        self.assertEqual(steel_row[0], "Metal (Steel)",
                         "Should use the original material name")
        self.assertEqual(steel_row[1], "30.00",
                         "Should show combined ton*km amount")
        self.assertEqual(
            steel_row[2],
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
            "Should preserve activity name"
        )

        # Verify PU deduplication
        self.assertIsNotNone(pu_row, "PU row should exist")
        self.assertEqual(pu_row[1], "20.00",
                         "Should show combined ton*km amount")
        self.assertEqual(
            pu_row[2],
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
            "Should preserve activity name"
        )

    def test_material_transportation_unit_conversion(self):
        """Test unit conversion in material transportation deduplication."""
        # Create test nodes with different units (though in practice this is unlikely)
        nodes = [
            create_test_node("Metal (Steel)", 10, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("Metal (Steel)", 10000, "kg*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Material Transport"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_material_transportation_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 1,
                         "Should have one row after deduplication")

        # Check that the combined node has the correct format
        steel_row = sections[0].values[0]
        self.assertEqual(steel_row[0], "Metal (Steel)",
                         "Should use the original material name")
        self.assertEqual(steel_row[1], "20.00",
            "Should show combined ton*km amount after unit conversion")
        self.assertEqual(
            steel_row[2],
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
            "Should preserve activity name"
        )

    def test_material_transportation_edge_cases(self):
        """Test edge cases in material transportation deduplication."""
        # Create a node with missing impacts
        node_with_missing_impacts = {
            "node_name": "Broken Node",
            "amount": 100,
            "unit": "metric ton*km",
            "id": "broken_node",
            "impacts": []  # Empty impacts
        }

        # Create valid nodes
        valid_node1 = create_test_node(
            "Metal (Steel)", 10, "metric ton*km",
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6"
        )
        valid_node2 = create_test_node(
            "Metal (Steel)", 15, "metric ton*km",
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6"
        )

        # Set up the test data with a mix of valid and invalid nodes
        nodes = [node_with_missing_impacts, valid_node1, valid_node2]
        self.lca_output["BIFMA"]["Material Transport"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_material_transportation_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 1,
             "Should have one row after deduplication and filtering")

        # Check that only valid nodes were processed and deduplicated
        steel_row = sections[0].values[0]
        self.assertEqual(steel_row[0], "Metal (Steel)",
                         "Should use the original material name")
        self.assertEqual(steel_row[1], "25.00",
                         "Should show combined ton*km amount")
        self.assertEqual(
            steel_row[2],
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
            "Should preserve activity name"
        )

    def test_transport_to_consumer_deduplication(self):
        """Test deduplication in the transport to consumer section."""
        # Create test nodes with duplicate transport to consumer
        nodes = [
            create_test_node("Product Transport Segment 1", 50, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("Product Transport Segment 1", 30, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("PRODUCT TRANSPORT SEGMENT 1", 20, "metric ton*km",
                             "transport, freight, lorry, >32 metric ton, diesel, EURO 6"),
            create_test_node("Product Transport Segment 2", 40, "metric ton*km",
                             "transport, freight, aircraft, passenger, EURO 6"),
            create_test_node("Product Transport Segment 2", 60, "metric ton*km",
                             "transport, freight, aircraft, passenger, EURO 6")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Transport to Consumer"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_transport_to_consumer_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 2,
                         "Should have two rows after deduplication")

        # Check that the combined nodes have the correct format
        segment1_row = next(
            (row for row in sections[0].values
                if "Product Transport Segment 1" in row[0]),
            None
        )
        segment2_row = next(
            (row for row in sections[0].values
                if "Product Transport Segment 2" in row[0]),
            None
        )

        # Verify segment 1 deduplication
        self.assertIsNotNone(segment1_row, "Segment 1 row should exist")
        self.assertEqual(segment1_row[0], "Product Transport Segment 1",
                         "Should use the original segment name")
        self.assertEqual(segment1_row[1], "100.00",
                         "Should show combined ton*km amount")
        self.assertEqual(
            segment1_row[2],
            "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
            "Should preserve activity name"
        )

        # Verify segment 2 deduplication
        self.assertIsNotNone(segment2_row, "Segment 2 row should exist")
        self.assertEqual(segment2_row[1], "100.00",
                         "Should show combined ton*km amount")
        self.assertEqual(
            segment2_row[2],
            "transport, freight, aircraft, passenger, EURO 6",
            "Should preserve activity name"
        )

    def test_waste_disposal_deduplication(self):
        """Test deduplication in the waste disposal section."""
        # Create test nodes with duplicate waste disposal entries
        nodes = [
            create_test_node("Metal (Steel) disposal (incineration)", 1, "kg",
                             "treatment of waste steel, municipal incineration"),
            create_test_node("Metal (Steel) disposal (incineration)", 2, "kg",
                             "treatment of waste steel, municipal incineration"),
            create_test_node("METAL (STEEL) DISPOSAL (INCINERATION)", 3, "kg",
                             "treatment of waste steel, municipal incineration"),
            create_test_node("Polymeric Material (PP) disposal (incineration)", 1.5, "kg",
                             "treatment of waste polypropylene, municipal incineration"),
            create_test_node("Polymeric Material (PP) disposal (landfilling)", 2.5, "kg",
                             "treatment of waste polypropylene, sanitary landfill")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Waste Disposal"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_waste_disposal_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 3,
                         "Should have three rows after deduplication")

        # Check that the combined nodes have the correct format
        steel_row = next(
            (row for row in sections[0].values
                if "Metal (Steel) disposal (incineration)" in row[0]),
            None
        )
        pp_incineration_row = next(
            (row for row in sections[0].values
                if "Polymeric Material (PP) disposal (incineration)" in row[0]),
            None
        )
        pp_landfill_row = next(
            (row for row in sections[0].values
                if "Polymeric Material (PP) disposal (landfilling)" in row[0]),
            None
        )

        # Verify steel deduplication
        self.assertIsNotNone(steel_row, "Steel disposal row should exist")
        self.assertEqual(steel_row[0], "Metal (Steel) disposal (incineration)",
                         "Should use the original material name")
        self.assertEqual(
            steel_row[1],
            "treatment of waste steel, municipal incineration",
            "Should preserve activity name"
        )

        # Verify PP incineration (not deduplicated with landfilling)
        self.assertIsNotNone(pp_incineration_row, "PP incineration row should exist")
        self.assertEqual(pp_incineration_row[0],
                         "Polymeric Material (PP) disposal (incineration)",
                         "Should preserve original name")
        self.assertEqual(
            pp_incineration_row[1],
            "treatment of waste polypropylene, municipal incineration",
            "Should preserve activity name"
        )

        # Verify PP landfilling (not deduplicated with incineration)
        self.assertIsNotNone(pp_landfill_row, "PP landfill row should exist")
        self.assertEqual(pp_landfill_row[0],
                         "Polymeric Material (PP) disposal (landfilling)",
                         "Should preserve original name")
        self.assertEqual(
            pp_landfill_row[1],
            "treatment of waste polypropylene, sanitary landfill",
            "Should preserve activity name"
        )

    def test_waste_disposal_case_insensitive(self):
        """Test case-insensitive deduplication in waste disposal."""
        # Create test nodes with case variations
        nodes = [
            create_test_node("Metal (Steel) disposal (incineration)", 1, "kg",
                             "treatment of waste steel, municipal incineration"),
            create_test_node("METAL (STEEL) DISPOSAL (INCINERATION)", 2, "kg",
                             "TREATMENT OF WASTE STEEL, MUNICIPAL INCINERATION"),
            create_test_node("metal (steel) disposal (incineration)", 3, "kg",
                             "treatment of waste steel, municipal incineration")
        ]

        # Set up the test data
        self.lca_output["BIFMA"]["Waste Disposal"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_waste_disposal_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 1,
             "Should have one row after case-insensitive deduplication")

        # Check that the combined node has the correct format
        steel_row = sections[0].values[0]
        self.assertEqual(
            steel_row[0],
            "Metal (Steel) disposal (incineration)",
            "Should use the original material name"
        )
        self.assertEqual(
            steel_row[1],
            "treatment of waste steel, municipal incineration",
            "Should preserve activity name"
        )

    def test_waste_disposal_edge_cases(self):
        """Test edge cases in waste disposal deduplication."""
        # Create a node with missing impacts
        node_with_missing_impacts = {
            "node_name": "Broken Node",
            "amount": 100,
            "unit": "kg",
            "id": "broken_node",
            "impacts": []  # Empty impacts
        }

        # Create valid nodes
        valid_node1 = create_test_node(
            "Metal (Steel) disposal (incineration)", 1, "kg",
            "treatment of waste steel, municipal incineration"
        )
        valid_node2 = create_test_node(
            "Metal (Steel) disposal (incineration)", 2, "kg",
            "treatment of waste steel, municipal incineration"
        )

        # Set up the test data with a mix of valid and invalid nodes
        nodes = [node_with_missing_impacts, valid_node1, valid_node2]
        self.lca_output["BIFMA"]["Waste Disposal"]["segment_nodes"] = nodes

        # Call the method under test
        sections = self.adapter._get_waste_disposal_sections()

        # Verify results
        self.assertEqual(len(sections), 1, "Should return one section")
        self.assertEqual(len(sections[0].values), 1,
             "Should have one row after deduplication and filtering")

        # Check that only valid nodes were processed and deduplicated
        steel_row = sections[0].values[0]
        self.assertEqual(
            steel_row[0],
            "Metal (Steel) disposal (incineration)",
            "Should use the original material name"
        )
        self.assertEqual(
            steel_row[1],
            "treatment of waste steel, municipal incineration",
            "Should preserve activity name"
        )


if __name__ == "__main__":
    unittest.main()

# This allows the test to be discovered by unittest discover
