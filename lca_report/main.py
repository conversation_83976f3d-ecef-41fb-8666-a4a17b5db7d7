from datetime import datetime
from replacements import (
    ReportMetaReplacements,
    CompanyInfoReplacements,
    ProductInfoReplacements,
    LCAReportOverviewReplacements,
    ManufacturingReplacements,
    TransportReplacements,
    RawMaterialsReplacements,
    PackagingMaterialReplacements,
    UsePhaseReplacements,
    EOLReplacements,
    OverviewOfResultsReplacements,
)
from lca_report_document import LCAReportDocument


if __name__ == "__main__":
    report_meta_replacements = ReportMetaReplacements(
        date=datetime.now().strftime('%Y-%m-%d'),
        functional_unit='1L',
        functional_type='Cleaning Product',
        month_and_year_of_study=datetime.now().strftime('%B %Y'),
        country_of_use='United Kingdom',
    )

    company_info_replacements = CompanyInfoReplacements(
        company_name="M&S",
        manufacturer="M&S",
        country_of_origin="United Kingdom",
    )

    product_info_replacements = ProductInfoReplacements(
        product_name="Sensitive laundry liquid",
        product_category="Laundry Liquid",
    )

    lca_report_overview_replacements = LCAReportOverviewReplacements(
        ghg_emissions_kg_co2e_per_functional_unit="0.2",
        highest_emitting_stage="Use Phase",
        highest_emitting_stage_percentage="60",
        second_highest_emitting_stage="Transport & Distribution",
        second_highest_emitting_stage_percentage="8",
        lowest_emitting_stage="Packaging",
        lowest_emitting_stage_percentage="5",
    )

    processing_replacements = ManufacturingReplacements(
        process_name="Manufacturing & Processing",
        processing_emissions_factor_kg_co2e="0.1",
        processing_emissions_factor_source="AISE",
    )

    transport_replacements = TransportReplacements(
        ingredient_transport_distance_km="1000",
        ingredient_transport_emissions_factor_kg_co2e="0.1",
        ingredient_transport_emissions_factor_source="UK GHG Conversion Factors",
        packaging_material_transport_distance_km="100",
        packaging_material_transport_emissions_factor_kg_co2e="0.01",
        packaging_material_transport_emissions_factor_source="UK GHG Conversion Factors",
        product_transport_distance_km="100",
        product_transport_emissions_factor_kg_co2e="0.01",
        product_transport_emissions_factor_source="UK GHG Conversion Factors",
    )

    raw_materials_replacements = RawMaterialsReplacements(
        ingredients=[
            ['Water', 'United Kingdom', '99', '0.1'],
            ['Sodium Laureth Sulfate', 'United Kingdom', '1', '0.1'],
        ],
    )

    packaging_material_replacements = PackagingMaterialReplacements(
        packaging_components=[
            ['PET', 'Bottle', 'Primary', '0.1', '0.01', 'UK GHG Conversion Factors'],
            ['PP', 'Cap', 'Primary', '0.01', '0.001', 'UK GHG Conversion Factors'],
            ['PP', 'Label', 'Primary', '0.01', '0.001', 'UK GHG Conversion Factors'],
        ],
    )

    use_phase_replacements = UsePhaseReplacements(
        consumer_uses=[
            ['Laundry cycle', '0.1', 'DEFRA'],
            ['Energy to heat water', '0.1', 'DEFRA'],
        ],
    )

    eol_replacements = EOLReplacements(
        materials=[
            ['PET', '0.1', 'DEFRA'],
            ['PP', '0.01', 'DEFRA'],
        ],
    )

    overview_of_results_replacements = OverviewOfResultsReplacements(
        raw_materials_gwp_impact="0.1",
        raw_materials_share_of_total="16.6",
        packaging_materials_gwp_impact="0.01",
        packaging_materials_share_of_total="16.6",
        manufacturing_gwp_impact="0.1",
        manufacturing_share_of_total="16.6",
        transport_gwp_impact="0.01",
        transport_share_of_total="16.6",
        use_phase_gwp_impact="0.1",
        use_phase_share_of_total="16.6",
        eol_gwp_impact="0.01",
        eol_share_of_total="16.6",
        total_gwp_impact="0.2",
        packaging_quartile="1",
        manufacturing_quartile="1",
        manufacturing_high_or_low="Low",
        use_phase_quartile="1",
    )

    lca_report_document = LCAReportDocument(
        report_meta_replacements=report_meta_replacements,
        company_info_replacements=company_info_replacements,
        product_info_replacements=product_info_replacements,
        lca_report_overview_replacements=lca_report_overview_replacements,
        manufacturing_replacements=processing_replacements,
        transport_replacements=transport_replacements,
        raw_materials_replacements=raw_materials_replacements,
        packaging_material_replacements=packaging_material_replacements,
        use_phase_replacements=use_phase_replacements,
        eol_replacements=eol_replacements,
        overview_of_results_replacements=overview_of_results_replacements,
    )

    lca_report_document.create_report()