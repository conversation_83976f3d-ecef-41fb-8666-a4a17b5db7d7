# pylint: disable=arguments-differ
import unittest
from typing import List
from docx import Document
from lca_report.replacements.template_value_replacements import TemplateValueReplacements


class TestReplacements(TemplateValueReplacements):
    name: str = "Test"
    description: str = "Test description"
    version: str = "1.0.0"
    data: List[str] = [
        ["Apple", "1"],
        ["Banana", "2"],
        ["Orange", "3"],
    ]

    def replacements(self):
        return {
            "[name]": self.name,
            "[description]": self.description,
            "[version]": self.version
        }

    def insert_values_into_table(self, doc):
        super().insert_values_into_table(
            doc,
            '[Test Table]',
            self.data,
        )

class TestReplacementsEmptyData(TemplateValueReplacements):
    name: str = "Test"
    description: str = "Test description"
    version: str = "1.0.0"
    data: List[str] = []

    def replacements(self):
        return {
            "[name]": self.name,
            "[description]": self.description,
            "[version]": self.version
        }

    def insert_values_into_table(self, doc):
        super().insert_values_into_table(
            doc,
            '[Test Table]',
            self.data,
        )

class TestTemplateReplacementValues(unittest.TestCase):

    def test_replace_values(self):
        test_replacements = TestReplacements()

        doc = Document()
        paragraph = doc.add_paragraph("[name] [description] [version]")
        test_replacements.replace_values(doc)

        self.assertEqual(paragraph.text, "Test Test description 1.0.0")

    def test_insert_table(self):
        test_replacements = TestReplacements()

        doc = Document()
        table = doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = "Fruit"
        table.cell(0, 1).text = "Number"
        table.cell(1, 0).text = "[Test Table]"

        test_replacements.insert_values_into_table(doc)

        self.assertEqual(len(doc.tables), 1)
        self.assertEqual(len(doc.tables[0].rows), 4)
        self.assertEqual(len(doc.tables[0].columns), 2)

        self.assertEqual(table.cell(1, 0).text, "Apple")
        self.assertEqual(table.cell(1, 1).text, "1")
        self.assertEqual(table.cell(2, 0).text, "Banana")
        self.assertEqual(table.cell(2, 1).text, "2")
        self.assertEqual(table.cell(3, 0).text, "Orange")
        self.assertEqual(table.cell(3, 1).text, "3")

    def test_insert_empty_list_into_table(self):
        test_replacements = TestReplacementsEmptyData()

        doc = Document()

        table = doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = "Fruit"
        table.cell(0, 1).text = "Number"
        table.cell(1, 0).text = "[Test Table]"

        test_replacements.insert_values_into_table(doc)

        self.assertEqual(len(doc.tables), 1)
        self.assertEqual(len(doc.tables[0].rows), 2)
        self.assertEqual(len(doc.tables[0].columns), 2)

        self.assertEqual(table.cell(1, 0).text, "N/A")

    def test_insert_chart(self):
        test_replacements = TestReplacements()

        doc = Document()
        paragraph = doc.add_paragraph("[chart]")
        test_replacements.insert_chart(
            doc,
            "[chart]",
            "Test Chart",
            ["Apple", "Banana", "Orange"],
            [1, 2, 3],
        )

        self.assertEqual(paragraph.text, "")
        self.assertEqual(len(doc.inline_shapes), 1)

    def tearDown(self):
        if not self._outcome.success:
            return

        test_method_name = self._testMethodName
        print(f"Test passed: {test_method_name}")
