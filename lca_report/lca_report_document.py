import os
import subprocess
from dataclasses import dataclass
from docx import Document
from lca_report.replacements import (
    ReportMetaReplacements,
    CompanyInfoReplacements,
    ProductInfoReplacements,
    LCAReportOverviewReplacements,
    ManufacturingReplacements,
    TransportReplacements,
    RawMaterialsReplacements,
    PackagingMaterialReplacements,
    UsePhaseReplacements,
    EOLReplacements,
    OverviewOfResultsReplacements,
)


@dataclass
class LCAReportDocument:
    report_meta_replacements: ReportMetaReplacements
    company_info_replacements: CompanyInfoReplacements
    product_info_replacements: ProductInfoReplacements
    lca_report_overview_replacements: LCAReportOverviewReplacements
    manufacturing_replacements: ManufacturingReplacements
    transport_replacements: TransportReplacements
    raw_materials_replacements: RawMaterialsReplacements
    packaging_material_replacements: PackagingMaterialReplacements
    use_phase_replacements: UsePhaseReplacements
    eol_replacements: EOLReplacements
    overview_of_results_replacements: OverviewOfResultsReplacements

    def create_report(self, report_name: str) -> str:
        dir_path = os.path.dirname(os.path.realpath(__file__))
        template_path = os.path.join(dir_path, 'templates', 'lca_report_template_v1_5.docx')
        template = Document(template_path)

        self.report_meta_replacements.replace_values(template)
        self.company_info_replacements.replace_values(template)
        self.product_info_replacements.replace_values(template)
        self.lca_report_overview_replacements.replace_values(template)

        self.manufacturing_replacements.insert_table(template)
        self.transport_replacements.insert_table(template)
        self.raw_materials_replacements.insert_table(template)
        self.packaging_material_replacements.insert_table(template)
        self.use_phase_replacements.insert_table(template)
        self.eol_replacements.insert_table(template)

        self.overview_of_results_replacements.replace_values(template)
        self.overview_of_results_replacements.insert_chart(template)

        output_path = os.path.join(dir_path, 'output', f'{report_name}.docx')
        pdf_output_path = output_path.replace('.docx', '.pdf')

        if os.path.exists(output_path):
            os.remove(output_path)

        if os.path.exists(pdf_output_path):
            os.remove(pdf_output_path)

        template.save(output_path)
        subprocess.call([
            'soffice',
            '--headless',
            '--convert-to',
            'pdf',
            output_path,
            '--outdir',
            os.path.join(dir_path, 'output'),
        ])

        pdf_output_path = output_path.replace('.docx', '.pdf')

        return pdf_output_path
