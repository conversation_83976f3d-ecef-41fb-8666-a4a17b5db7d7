from enum import Enum
from dataclasses import dataclass

class ErrorMessages(str, Enum):
    PRODUCT_NOT_FOUND_BY_NAME = "Product with product name '{product_name}' not found."
    PRODUCT_NOT_FOUND_BY_ID = "Product with ID '{product_id}' not found."
    PRODUCT_WITH_PRODUCT_ID_ALREADY_EXISTS = "Product with ID '{product_id}' already exists."
    PRODUCT_INGREDIENT_NOT_FOUND = "Product ingredient '{ingredient_name}' not found."
    COUNTRY_NOT_FOUND = "Country '{country_name}' not found."
    CANNOT_UPDATE_INGREDIENT_WEIGHTS_FOR_NON_CLONE = (
        "Cannot update ingredient weights for a product that is not a clone."
    )
    CLONE_FOR_PRODUCT_ALREADY_EXISTS = (
        "Clone for product already exists. Each product can be cloned a maximum 1 times."
    )
    CANNOT_CLONE_A_CLONE = "Cannot clone a clone of a product."
    SUPPLIER_NOT_FOUND_BY_ID = "Supplier with ID '{supplier_id}' not found."
    ERROR_PACKAGING_MATERIAL_ISNT_SUPPORTED = (
        "Error packaging material '{packaging_material}' is not yet supported."
    )
    SOURCE_COUNTRY_NOT_FOUND_FOR_INGREDIENT = "Source country for '{ingredient_name}' not found."
    INGREDIENT_NOT_FOUND = "Ingredient '{ingredient_name}' not found."
    EMISSIONS_FACTOR_NOT_FOUND = "Emissions factor for '{activity_name}' not found."
    PRODUCT_CATEGORY_PREDICTION_NOT_FOUND = "Product category prediction not found."
    PRODUCT_CATEGORY_NOT_FOUND_BY_NAME = "Product category '{product_category}' not found."
    PRODUCT_CATEGORY_NOT_FOUND_BY_ID = "Product category with ID '{category_id}' not found."
    PRODUCT_PACKAGING_NOT_FOUND_BY_ID = "Product packaging with ID '{packaging_id}' not found."
    PRODUCT_ATTRIBUTE_NOT_FOUND_BY_ID = "Product attribute with ID '{attribute_id}' not found."
    PRODUCT_ATTRIBUTE_EXISTS = "Product attribute with key '{attribute_key}' already exists."


@dataclass
class ResourceNotFoundException(Exception):
    resource_type: str
    resource_name: str
    def __post_init__(self):
        self.message = f"{self.resource_type} {self.resource_name} not found"

class DatabaseError(Exception):
    """Custom exception for database-related errors."""

    def __init__(self, message=None, original_exception=None):
        super().__init__(message)
        self.original_exception = original_exception
        self.message = message

    def __str__(self):
        if self.original_exception:
            return f"{self.message} (Caused by {self.original_exception})"
        return self.message

class DuplicateEntryError(DatabaseError):
    """DatabaseError - Duplicate Entry"""
