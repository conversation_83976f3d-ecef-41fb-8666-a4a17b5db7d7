from typing import List
from databases.tenant.models import TenantEmissionsFactorValueRead


class Aggregation:
    def __init__(self):
        self._payload = {"default": {}}

    def add_segment_node(
        self,
        pcr_name: str,
        segment_name: str,
        sequence_number: int,
        node_id: id,
        node_name: str,
        amount: float,
        unit: str,
        emissions_factor_values: List[TenantEmissionsFactorValueRead],
    ):
        pcr_payload = self._payload.setdefault(pcr_name, {})
        segment_payload = pcr_payload.setdefault(
            segment_name,
            {
                "sequence_number": sequence_number,
                "segment_nodes": [],
            }
        )
        segment_node = {
            "node_id": node_id,
            "node_name": node_name,
            "amount": amount,
            "unit": unit,
            "impacts": [],
        }

        for emissions_factor_value in emissions_factor_values:
            total_impact_amount = amount * emissions_factor_value.amount
            segment_node["impacts"].append(
                {
                    "emissions_factor_value": emissions_factor_value.dict(),
                    "impact_amount": total_impact_amount,
                }
            )

        segment_payload["segment_nodes"].append(segment_node)


    def to_dict(self) -> dict:
        return self._payload
