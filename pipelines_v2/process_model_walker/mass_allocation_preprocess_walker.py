from databases.shared.constants.node_type import NodeTypes
from databases.tenant.models import Node, Edge
from databases.tenant.repository import TenantEmissionsFactorRepo, ProductRepo
from services.emissions_factors_service import EmissionsFactorsService
from pipelines_v2.utils.unit import UnitType
from pipelines_v2.graph import Graph
from pipelines_v2.process_model_walker.walker import <PERSON>
from pipelines_v2.process_model_walker.preprocess_walker import get_eol_emissions_factors
from utils.async_call import safe_async
from clients.ml_models import MLModels

DEFAULT_EOL_ACTIVITY = {
    "activity_name": "treatment of municipal solid waste, sanitary landfill",
    "geography": "RoW",
    "reference_product_name": "municipal solid waste",
    "source": "Ecoinvent 3.11",
}


class MassAllocationPreprocessWalker(Walker):
    """
    handle mass allocation logic after the initial preprocessing
    claculating total product weight and handle mass allocation
    for materials
    """

    def __init__(
        self,
        tenant_id: str,
        product_id: str,
        nodes: list[Node],
        edges: list[Edge],
    ):
        self._product_repo = ProductRepo(tenant_id)
        self._product = self._product_repo.get_product(product_id)

        if not self._product:
            raise ValueError(f"Product not found: {product_id}")

        self._emissions_factors_service = EmissionsFactorsService()
        self._tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        self._ml_models = MLModels()
        super().__init__(tenant_id, product_id, nodes, edges)

    def _walk(self):
        output_graph = self._graph.clone()

        # Calculate total product weight excluding mass allocation nodes
        total_product_weight_kg = self._calculate_product_weight(output_graph)

        # Get a static list of nodes in topological order
        # This ensures that modifications to the graph during traversal won't affect node processing
        nodes_to_process = self._graph.get_nodes_topologically_sorted()

        # Handle mass allocation for materials
        for node in nodes_to_process:
            if (
                node.node_type in [NodeTypes.MATERIAL.value, NodeTypes.PACKAGING.value]
                and node.mass_allocation_per_kg
            ):
                self._visit_material(node, output_graph, total_product_weight_kg)

        return output_graph.get_nodes(), output_graph.get_edges()

    def _calculate_product_weight(self, graph: Graph) -> float:
        """Calculate total product weight excluding mass allocation nodes"""
        total_weight_kg = 0.0
        # Use get_nodes_topologically_sorted for consistent traversal pattern
        for node in graph.get_nodes_topologically_sorted():
            if (
                node.node_type in [NodeTypes.MATERIAL.value, NodeTypes.PACKAGING.value]
                and not node.mass_allocation_per_kg
                and node.amount
            ):
                total_weight_kg += UnitType.convert_from_abbrev(
                    node.amount, node.unit, UnitType.KILOGRAM.value.abbrev
                )
        return total_weight_kg

    def _visit_material(
        self, node: Node, output_graph: Graph, total_product_weight_kg: float
    ):
        """Handle mass allocation for material waste."""
        if not node.amount or node.amount <= 0 or total_product_weight_kg <= 0:
            return

        # Convert amounts to kg
        material_waste_produced_kg = UnitType.convert_from_abbrev(
            node.amount, node.unit, UnitType.KILOGRAM.value.abbrev
        )

        # Calculate allocated amount basis on total product weight
        allocated_amount_kg = material_waste_produced_kg / total_product_weight_kg

        # Convert allocated amount back to original unit
        node.amount = UnitType.convert_from_abbrev(
            allocated_amount_kg,
            UnitType.KILOGRAM.value.abbrev,
            node.unit
        )

        # Create EOL nodes basis specified rates
        eol_rates = {
            "recycling": node.eol_recycling_rate,
            "landfill": node.eol_landfill_rate,
            "incineration": node.eol_incineration_rate,
            "composting": node.eol_composting_rate,
        }

        # Get first manufacturing node from the list
        manufacturing_nodes = [
            n
            for n in output_graph.get_outgoing_nodes(node)
            if n.node_type == NodeTypes.PRODUCTION.value
        ]
        if not manufacturing_nodes:
            return

        manufacturing_node = manufacturing_nodes[0]

        # Get emissions factors for disposal methods in parallel
        disposal_methods = [
            (node.name, disposal_method)
            for disposal_method, rate in eol_rates.items()
            if rate > 0
        ]

        emissions_factors = safe_async(
            get_eol_emissions_factors,
            self._ml_models,
            disposal_methods,
        )

        for (_, disposal_method), emissions_factor in emissions_factors.items():
            if not emissions_factor:
                emissions_factor = DEFAULT_EOL_ACTIVITY

            tenant_emissions_factor = (
                self._tenant_emissions_factors.get_emissions_factor(
                    activity_name=emissions_factor["activity_name"],
                    geography=emissions_factor["geography"],
                    reference_product=emissions_factor["reference_product_name"],
                    source=emissions_factor["source"],
                )
            )

            if not tenant_emissions_factor:
                tenant_emissions_factor, _ = (
                    self._emissions_factors_service
                    .copy_emissions_factor_to_tenant_emissions_factors(
                        self._tenant_id,
                        emissions_factor["activity_name"],
                        emissions_factor["reference_product_name"],
                        emissions_factor["geography"],
                        emissions_factor["source"],
                    )
                )

            eol_node = Node(
                product_id=self._product.product_id,
                name=f"{node.name} factory waste disposal ({disposal_method})",
                node_type=NodeTypes.EOL.value,
                amount=node.amount * eol_rates[disposal_method],
                unit=node.unit,
                quantity=1,
                emissions_factor_id=tenant_emissions_factor.id,
                segment_type="auto",
            )
            eol_node = output_graph.insert_node(eol_node)
            edge = Edge(
                product_id=self._product.product_id,
                from_node_id=manufacturing_node.id,
                to_node_id=eol_node.id,
            )
            output_graph.insert_edges([edge])
