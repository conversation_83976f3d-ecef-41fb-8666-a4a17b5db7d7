#pylint: disable=unused-argument
from unittest import TestCase
from unittest.mock import patch
from databases.shared.constants.node_type import NodeTypes
from databases.shared.repository.waste_disposal_method_emission import (
    WasteDisposalMethodEmissionRepo,
)
from databases.tenant.models import Node, Edge, Product, Address, TenantEmissionsFactor
from pipelines_v2.process_model_walker import PreprocessWalker


class TestPreprocessWalker(TestCase):
    @patch("pipelines_v2.process_model_walker.preprocess_walker.TenantEmissionsFactorRepo")
    @patch(
        "pipelines_v2.process_model_walker.preprocess_walker.ProductRepo.get_product",
        return_value=Product(
            product_id="1",
            product_name="Product",
            brand="Brand",
            country_of_use="United States",
            uses_per_package=1,
            primary_category="Backpacks",
        )
    )
    @patch(
        "databases.tenant.repository.product.engine_pool.get_tenant_engine",
        return_value=None,
    )
    def test_auto_eol_nodes(
        self,
        mock_get_tenant_engine,
        mock_get_product,
        mock_tenant_emissions_factor_repo
    ):
        mock_tenant_emissions_factor_repo = mock_tenant_emissions_factor_repo.return_value
        mock_tenant_emissions_factor_repo\
            .copy_emissions_factor_to_tenant_emissions_factors\
            .return_value = TenantEmissionsFactor(
            id=1,
            activity_name="Product Use",
            geography="GBR",
            reference_product="Product",
            source="ML",
        )
        nodes = [
            Node(
                id=1,
                name="PET",
                node_type=NodeTypes.MATERIAL.value,
                quantity=1,
                amount=100,
                unit="g",
                location=Address(
                    city="San Francisco",
                    country="United States",
                ),
            ),
            Node(
                id=2,
                name="Product Use",
                node_type=NodeTypes.USE.value,
                segment_type="auto",
                quantity=1,
                amount=0,
                unit="l",
                location=Address(
                    city="San Francisco",
                    country="United States",
                ),
            ),
            Node(
                id=3,
                name="Product EOL",
                node_type=NodeTypes.EOL.value,
                segment_type="auto",
                quantity=1,
                location=Address(
                    city="San Francisco",
                    country="United States",
                ),
            ),
        ]
        edges = [
            Edge(
                from_node_id=1,
                to_node_id=2,
            ),
            Edge(
                from_node_id=2,
                to_node_id=3,
            ),
        ]
        walker = PreprocessWalker(
            tenant_id="test",
            product_id="test",
            nodes=nodes,
            edges=edges,
        )
        nodes, edges = walker.walk()
        eol_nodes = [node for node in nodes if node.node_type == NodeTypes.EOL.value]

        waste_disposal_method_emission_repo = WasteDisposalMethodEmissionRepo()
        waste_disposal_methods = waste_disposal_method_emission_repo.get_by_packaging_material(
            "Plastic",
            "USA",
        )

        self.assertEqual(len(eol_nodes), len(waste_disposal_methods))

        for disposal_method in waste_disposal_methods:
            self.assertTrue(
                any(
                    node.amount == (100 * disposal_method.rate)
                    for node in eol_nodes
                )
            )
