from typing import Optional, List, Dict

class NodeBuilder:
    """Helper class to build test nodes with sensible defaults."""

    def __init__(self):
        self._next_id = 1
        self._nodes: List[Dict] = []
        self.location = {
            "country": "India",
            "city": "Nashik",
            "address": None,
            "zip_code": None
        }

    def create_node(
        self,
        name: str,
        node_type: str,
        amount: Optional[float] = None,
        unit: str = "g",
        emissions_factor: Optional[dict] = None,
        scrap_rate: Optional[float] = None,
        scrap_fate: Optional[str] = None,
    ) -> dict:
        """Create a node with sensible defaults and add it to the internal list."""
        node_id = self._next_id
        self._next_id += 1

        node = {
            "id": node_id,
            "name": name,
            "amount": amount,
            "unit": unit,
            "quantity": 1,
            "node_type": node_type,
            "location": self.location,
            "emissions_factor": emissions_factor,
            "supplier_id": None,
            "recycled_content_rate": None,
            "disposed_weight_kg": None,
            "mass_allocation_per_kg": False,
            "eol_recycling_rate": 0.0,
            "eol_landfill_rate": 0.0,
            "eol_incineration_rate": 0.0,
            "eol_composting_rate": 0.0,
            "component_name": "1101 (Acme Corp Inc.)",
            "packaging_level": None,
            "packaging_type": None,
            "description": "Nut & Bolt, Unpainted",
            "segment_type": None,
            "transport_mode": None,
            "scrap_rate": scrap_rate,
            "scrap_fate": scrap_fate
        }
        self._nodes.append(node)
        return node

    def get_nodes(self) -> List[Dict]:
        """Return all created nodes."""
        return self._nodes

def create_edge(from_node: dict, to_node: dict) -> dict:
    """Create an edge between two nodes."""
    return {
        "from_node_id": from_node["id"],
        "to_node_id": to_node["id"]
    }
