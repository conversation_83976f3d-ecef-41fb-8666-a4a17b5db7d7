import unittest
from unittest.mock import MagicMock, patch

# Assuming these are the paths to your actual classes
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.models import Product as TenantProduct # Renamed to avoid clash
from pipelines_v2.process_model_walker.preprocess_walker import Preprocess<PERSON>alker
from pipelines_v2.process_model_walker.tests.lib import (
    create_mock_node,
    create_mock_edge
)


# --- Test Case ---
# This test ensures that both EOL nodes are processed correctly and aren't skipped
# during the graph traversal. It verifies that each origin material gets exactly
# one set of disposal nodes, preventing duplicate EOL processing.
class TestPreprocessWalkerSkipNodeIssue(unittest.TestCase):
    # Uses the builder pattern for mock setup to make the test more maintainable
    # Each dependency is mocked and configured in setUp, then injected via patch decorators

    def setUp(self):
        # Mock dependencies of PreprocessWalker
        self.mock_product_repo = MagicMock()
        self.mock_product = MagicMock(spec=TenantProduct)
        self.mock_product.product_id = "test_product_id"
        self.mock_product.primary_category = "TestCategory"
        self.mock_product_repo.get_product.return_value = self.mock_product

        self.mock_emissions_factors_service = MagicMock()
        self.mock_tenant_emissions_factors_repo = MagicMock()
        # Mock get_emissions_factor to return something or None
        self.mock_tenant_emissions_factors_repo.get_emissions_factor.return_value = MagicMock(
            id="ef1"
        )
        copy_result = (MagicMock(id="ef_copied"), [])
        # Get the copy method
        service = self.mock_emissions_factors_service
        copy_method = service.copy_emissions_factor_to_tenant_emissions_factors
        copy_method.return_value = copy_result


        self.mock_ml_models = MagicMock()
        # Assume EOL includes raw materials
        self.mock_ml_models.predict_has_eol.return_value = True
        # Return material name as its EOL category
        self.mock_ml_models.predict_eol_category.side_effect = lambda material_name: material_name

        # Mock for get_eol_emissions_factors (the async helper in preprocess_walker)
        # This mock simulates the behavior of the `safe_async` call
        # It returns a dict with (material_name, disposal_method) keys
        # and emissions factor dicts as values
        # pylint: disable=unused-argument
        def mock_get_eol_emissions_factors_sync(func, ml_models, materials_list, **kwargs):
            # This is a synchronous mock for the async helper
            results = {}
            for mat_name, disp_method in materials_list:
                results[(mat_name, disp_method)] = {
                    "activity_name": f"{mat_name} {disp_method}",
                    "geography": "GLO",
                    "reference_product_name": mat_name,
                    "source": "MockSource"
                }
            return results

        self.mock_safe_async_get_eol_factors = MagicMock(
            side_effect=mock_get_eol_emissions_factors_sync
        )


        self.mock_waste_disposal_repo = MagicMock()

        # Mock for get_country_info
        self.mock_get_country_info = MagicMock()
        self.mock_get_country_info.return_value = MagicMock(alpha_3_code="TCY")


    @patch('pipelines_v2.process_model_walker.preprocess_walker.ProductRepo')
    @patch('pipelines_v2.process_model_walker.preprocess_walker.EmissionsFactorsService')
    @patch('pipelines_v2.process_model_walker.preprocess_walker.TenantEmissionsFactorRepo')
    @patch('pipelines_v2.process_model_walker.preprocess_walker.MLModels')
    @patch('pipelines_v2.process_model_walker.preprocess_walker.WasteDisposalMethodEmissionRepo')
    @patch('pipelines_v2.process_model_walker.preprocess_walker.get_country_info')
    @patch('pipelines_v2.process_model_walker.preprocess_walker.safe_async') # Patch safe_async
    def test_potential_skip_node_issue(self,
                                       mock_safe_async, # Corresponds to safe_async
                                       mock_get_country_info_func,
                                       mock_waste_repo_constructor,
                                       mock_ml_constructor,
                                       mock_tenant_ef_repo_constructor,
                                       mock_ef_service_constructor,
                                       mock_prod_repo_constructor):

        # Assign mocks to instances created within PreprocessWalker
        mock_prod_repo_constructor.return_value = self.mock_product_repo
        mock_ef_service_constructor.return_value = self.mock_emissions_factors_service
        mock_tenant_ef_repo_constructor.return_value = self.mock_tenant_emissions_factors_repo
        mock_ml_constructor.return_value = self.mock_ml_models
        mock_waste_repo_constructor.return_value = self.mock_waste_disposal_repo
        mock_get_country_info_func.return_value = self.mock_get_country_info.return_value

        # Configure mock_safe_async to handle the call to get_eol_emissions_factors
        # The first argument to safe_async is the function, the rest are its args.
        # We want to intercept calls where the first arg is `get_eol_emissions_factors`
        # (which is imported in preprocess_walker module).
        # For simplicity, we'll assume any safe_async call is for this,
        # or you can make the side_effect more sophisticated.
        # Define a side effect function to handle different calls to safe_async
        def safe_async_side_effect(func, *args, **kwargs):
            if "get_eol_emissions_factors" in str(func):
                return self.mock_safe_async_get_eol_factors(func, *args, **kwargs)
            if "predict_has_eol" in str(func):
                return self.mock_ml_models.predict_has_eol(*args, **kwargs)
            return self.mock_ml_models.predict_eol_category(*args, **kwargs)

        mock_safe_async.side_effect = safe_async_side_effect


        # --- Define the Graph Structure ---
        # Origin node (e.g., a material or product assembly point)
        # Two "auto" EOL nodes as children of the origin node.
        # The PreprocessWalker should process the first auto EOL,
        # which then tries to skip the second auto EOL.
        # We also add a "dependent" node after the second auto EOL to see if it gets processed.
        # (Although PreprocessWalker typically replaces EOL, so a dependent might not be natural,
        # let's use a simpler check: does the second EOL node get *fully processed* by _visit_eol?)

        # Create nodes using the helper function
        origin_node = create_mock_node(
            node_id=1,
            product_id="p1",
            name="Origin Product",
            node_type=NodeTypes.MATERIAL.value,
            amount=100,
            unit="kg",
            location_city="Testville",
            location_country="Testland"
        )

        # Auto EOL node 1 (This one will be processed by _visit_eol first)
        auto_eol_node_1 = create_mock_node(
            node_id=2,
            product_id="p1",
            name="Auto EOL 1",
            node_type=NodeTypes.EOL.value,
            location_city="Testville",
            location_country="Testland"
        )
        # Set segment_type which is not a parameter in create_mock_node
        auto_eol_node_1.segment_type = "auto"

        # Auto EOL node 2 (This one _visit_eol for node 1 will try to skip)
        auto_eol_node_2 = create_mock_node(
            node_id=3,
            product_id="p1",
            name="Auto EOL 2",
            node_type=NodeTypes.EOL.value,
            location_city="Testville",
            location_country="Testland"
        )
        auto_eol_node_2.segment_type = "auto"

        # A marker node to see if iteration continues correctly after potential skip
        # If EOL nodes are terminal, this might not be reached
        # But we can check if auto_eol_node_2 gets processed
        final_marker_node = create_mock_node(
            node_id=4,
            product_id="p1",
            name="Final Marker",
            node_type=NodeTypes.PRODUCTION.value,
            location_city="Testville",
            location_country="Testland"
        )

        nodes = [origin_node, auto_eol_node_1, auto_eol_node_2, final_marker_node]
        edges = [
            create_mock_edge(1, 2, "p1"), # Origin -> Auto EOL 1
            create_mock_edge(1, 3, "p1"), # Origin -> Auto EOL 2
            # To ensure topological order processes EOL 1 before EOL 2 if IDs matter
            # Let's make EOL2 depend on EOL1 to force an order
            # For _visit_eol, it looks at outgoing nodes of origin_node
            # So order in `nodes` list might matter for BFS/topo
            # Let's make EOL2 also an output of origin to test if EOL1 processing skips EOL2
            # Auto EOL 2 -> Final Marker (to ensure EOL2 isn't terminal for sort)
            create_mock_edge(3, 4, "p1")
        ]

        # Mock the waste disposal methods from the repo
        # When _visit_eol processes auto_eol_node_1, it will create these
        mock_disposal_method_1 = MagicMock()
        mock_disposal_method_1.waste_disposal_method = "Landfill"
        mock_disposal_method_1.rate = 0.6 # 60%

        mock_disposal_method_2 = MagicMock()
        mock_disposal_method_2.waste_disposal_method = "Recycling"
        mock_disposal_method_2.rate = 0.4 # 40%

        self.mock_waste_disposal_repo.get_by_packaging_material.return_value = [
            mock_disposal_method_1,
            mock_disposal_method_2,
        ]

        # --- Instantiate and Run the Walker ---
        # We will spy on the _visit_eol method to see which nodes it's called for.
        walker = PreprocessWalker("test_tenant", "test_product_id", nodes, edges)

        # Spy on _visit_eol - using protected access is necessary for testing
        # pylint: disable=protected-access
        original_visit_eol = walker._visit_eol
        visited_eol_nodes_in_main_loop = []

        def spy_visit_eol(node, output_graph_arg):
            if node.segment_type == "auto": # Only log for the original auto EOL nodes
                visited_eol_nodes_in_main_loop.append(node.id)
            return original_visit_eol(node, output_graph_arg)

        walker._visit_eol = spy_visit_eol
        # pylint: enable=protected-access

        # After our fix, we expect both EOL nodes to be processed without errors
        processed_nodes, _ = walker.walk()

        # --- Assertions ---
        print(
            f"Nodes for which _visit_eol was called from main loop: "
            f"{visited_eol_nodes_in_main_loop}"
        )

        # With the fix, both auto EOL nodes should be processed
        self.assertIn(
            2,
            visited_eol_nodes_in_main_loop,
            "Auto EOL Node 1 (id 2) should have been visited."
        )
        self.assertIn(
            3,
            visited_eol_nodes_in_main_loop,
            "Auto EOL Node 2 (id 3) should have been visited."
        )

        # Check that both EOL nodes were properly transformed
        output_node_names = [n.name for n in processed_nodes]
        print(
            f"Output node names: {output_node_names}"
        )

        # The EOL nodes are named after the origin node's material name, not the EOL nodes
        # So we expect to see "Origin Product disposal (Landfill)" etc.
        self.assertTrue(
            any("Origin Product disposal (Landfill)" in name for name in output_node_names),
            "Origin Product should have been transformed to include Landfill disposal"
        )
        self.assertTrue(
            any("Origin Product disposal (Recycling)" in name for name in output_node_names),
            "Origin Product should have been transformed to include Recycling disposal"
        )

        # Expect only ONE set of disposal nodes for the origin material
        landfill_count = sum(
            1 for name in output_node_names if "Origin Product disposal (Landfill)" in name
        )
        recycling_count = sum(
            1 for name in output_node_names if "Origin Product disposal (Recycling)" in name
        )
        self.assertEqual(
            1, landfill_count,
            "Should have exactly one Landfill disposal node for the origin material"
        )
        self.assertEqual(
            1, recycling_count,
            "Should have exactly one Recycling disposal node for the origin material"
        )

        # Ensure original "auto" EOL placeholder nodes are removed from the output
        self.assertNotIn(
            auto_eol_node_1.name,  # Check by name of the original placeholder
            output_node_names,
            f"{auto_eol_node_1.name} should have been removed and replaced."
        )
        self.assertNotIn(
            auto_eol_node_2.name,  # Check by name of the original placeholder
            output_node_names,
            f"{auto_eol_node_2.name} should have been removed and replaced."
        )


if __name__ == '__main__':
    unittest.main()
