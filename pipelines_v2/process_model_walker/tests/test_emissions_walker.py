# pylint: disable=protected-access,line-too-long
from unittest import TestCase
from unittest.mock import patch
from databases.shared.constants.node_type import NodeTypes
from databases.shared.repository import PCRRepository
from databases.tenant.models import Product
from pipelines_v2.process_model_walker.emissions_walker import EmissionsWalker
from pipelines_v2.process_model_walker.tests.lib import (
  create_mock_process_model,
  create_mock_tenant_emissions_factor_value,
)


class TestEmissionsWalker(TestCase):
    def _get_total_impact(self, aggregation, segment_name, lcia_method, impact_category, impact_unit):
        total_impact = 0
        for segment in aggregation.to_dict()["default"][segment_name]["segment_nodes"]:
            for impact in segment["impacts"]:
                if (
                    impact["emissions_factor_value"]["impact_indicator"]["lcia_method"] == lcia_method
                    and impact["emissions_factor_value"]["impact_indicator"]["category"] == impact_category
                    and impact["emissions_factor_value"]["impact_indicator"]["unit"] == impact_unit
                ):
                    total_impact += impact["impact_amount"]

        return total_impact

    @patch(
        "pipelines_v2.process_model_walker.emissions_walker.EmissionsWalker._get_emissions_values"
    )
    @patch("pipelines_v2.process_model_walker.emissions_walker.ProductRepo")
    def test_different_units(self, mock_product_repo, mock_get_emissions_factor_values):
        """Test different units for material nodes (kilograms and grams)."""
        mock_product_repo = mock_product_repo.return_value
        mock_product_repo.get_product.return_value = Product(
            product_id="1",
            product_name="Product",
            brand="Brand",
            country_of_use="United States",
            uses_per_package=1,
        )
        mock_get_emissions_factor_values.return_value = [
            create_mock_tenant_emissions_factor_value(
                1,
                "Citric Acid",
                "Product",
            )
        ]
        process_model = create_mock_process_model(
            [
                {
                    "node_id": 1,
                    "product_id": "1",
                    "name": "Citric Acid",
                    "node_type": NodeTypes.MATERIAL.value,
                    "amount": 0.1,
                    "unit": "kg",
                    "location_id": 1,
                    "location_city": "Chicago",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 2,
                    "product_id": "1",
                    "name": "Citric Acid Transport",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 1000,
                    "unit": "km",
                    "location_id": 2,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 2,
                    "emissions_factor_unit": "metric ton*km",
                },
                {
                    "node_id": 3,
                    "product_id": "1",
                    "name": "Product Manufacturing",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 3,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                },
            ],
            [
                {
                    "from_node_id": 1,
                    "to_node_id": 2,
                    "product_id": "1",
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3,
                    "product_id": "1",
                },
            ],
        )
        walker = EmissionsWalker(
            "demo1",
            "1",
            process_model[0],
            process_model[1],
            impact_factors=[
                {
                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                    "impact_categories": ["climate change"],
                }
            ],
        )
        agg_with_kg_unit = walker.walk()

        process_model = create_mock_process_model(
            [
                {
                    "node_id": 1,
                    "product_id": "1",
                    "name": "Citric Acid",
                    "node_type": NodeTypes.MATERIAL.value,
                    "amount": 100,
                    "unit": "g",
                    "location_id": 1,
                    "location_city": "Chicago",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 2,
                    "product_id": "1",
                    "name": "Citric Acid Transport",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 1000,
                    "unit": "km",
                    "location_id": 2,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 2,
                    "emissions_factor_unit": "metric ton*km",
                },
                {
                    "node_id": 3,
                    "product_id": "1",
                    "name": "Product Manufacturing",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 3,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                }
            ],
            [
                {
                    "from_node_id": 1,
                    "to_node_id": 2,
                    "product_id": "1",
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3,
                    "product_id": "1",
                },
            ],
        )

        walker2 = EmissionsWalker(
            "demo1",
            "1",
            process_model[0],
            process_model[1],
            impact_factors=[
                {
                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                    "impact_categories": ["climate change"],
                }
            ],
        )
        agg_with_g_unit = walker2.walk()
        self.assertEqual(
            self._get_total_impact(
                agg_with_kg_unit,
                "materials",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            ),
            self._get_total_impact(
                agg_with_g_unit,
                "materials",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            )
        )

        self.assertAlmostEqual(
            self._get_total_impact(
                agg_with_kg_unit,
                "materials",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            ),
            self._get_total_impact(
                agg_with_g_unit,
                "materials",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            ),
            places=4,
        )

        self.assertEqual(
            self._get_total_impact(
                agg_with_kg_unit,
                "materials",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            ),
            self._get_total_impact(
                agg_with_g_unit,
                "materials",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            ),
        )

    @patch(
        "pipelines_v2.process_model_walker.emissions_walker.EmissionsWalker._get_emissions_values"
    )
    @patch("pipelines_v2.process_model_walker.emissions_walker.ProductRepo")
    def test_uses_per_package(self, mock_product_repo, mock_get_emissions_factor_values):
        mock_product_repo = mock_product_repo.return_value
        mock_product_repo.get_product.return_value = Product(
            product_id="1",
            product_name="Product",
            brand="Brand",
            country_of_use="United States",
            uses_per_package=2,
        )

        consumer_use_emissions_factor = 3.4

        mock_get_emissions_factor_values.return_value = [
            create_mock_tenant_emissions_factor_value(
                1,
                "Consumer Use",
                "Product",
                impact_amount=consumer_use_emissions_factor,
            )
        ]

        process_model = create_mock_process_model(
            [
                {
                    "node_id": 1,
                    "product_id": "1",
                    "name": "Citric Acid",
                    "node_type": NodeTypes.MATERIAL.value,
                    "quantity": 1,
                    "amount": 100,
                    "unit": "g",
                    "location_id": 1,
                    "location_city": "Chicago",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 2,
                    "product_id": "1",
                    "name": "Citric Acid Transport",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 1000,
                    "unit": "km",
                    "location_id": 2,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 2,
                    "emissions_factor_unit": "metric ton*km",
                },
                {
                    "node_id": 3,
                    "product_id": "1",
                    "name": "Product Manufacturing",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 3,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 4,
                    "product_id": "1",
                    "name": "Product Use",
                    "node_type": NodeTypes.USE.value,
                    "amount": 1,
                    "unit": "kg",
                    "emissions_factor_id": 1,
                }
            ],
            [
                {
                    "from_node_id": 1,
                    "to_node_id": 2,
                    "product_id": "1",
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3,
                    "product_id": "1",
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 4,
                    "product_id": "1",
                },
            ],
        )

        walker = EmissionsWalker(
            "demo1",
            "1",
            process_model[0],
            process_model[1],
            impact_factors=[
                {
                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                    "impact_categories": ["climate change"],
                }
            ],
        )
        agg = walker.walk()

        number_of_uses_by_quantity = 2
        expected_emissions = consumer_use_emissions_factor * number_of_uses_by_quantity

        self.assertEqual(
            self._get_total_impact(
                agg,
                "use",
                "ReCiPe 2016 v1.03, midpoint (H)",
                "climate change",
                "kg CO2e",
            ),
            expected_emissions,
        )

    @patch(
        "pipelines_v2.process_model_walker.emissions_walker.EmissionsWalker._get_emissions_values"
    )
    @patch("pipelines_v2.process_model_walker.emissions_walker.ProductRepo")
    def test_multiple_transport_nodes(self, mock_product_repo, mock_get_emissions_factor_values):
        mock_product_repo = mock_product_repo.return_value
        mock_get_emissions_factor_values.return_value = [
            create_mock_tenant_emissions_factor_value(
                1,
                "Citric Acid Transport",
                "Product",
                impact_amount=0.0001,
            )
        ]
        mock_product_repo.get_product.return_value = Product(
            product_id="1",
            product_name="Product",
        )
        process_model = create_mock_process_model(
            [
                {
                    "node_id": 1,
                    "product_id": "1",
                    "name": "Citric Acid",
                    "node_type": NodeTypes.MATERIAL.value,
                    "quantity": 1,
                    "amount": 100,
                    "unit": "g",
                    "location_id": 1,
                    "location_city": "Chicago",
                    "location_country": "United States",
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 2,
                    "product_id": "1",
                    "name": "Citric Acid Transport",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 1000,
                    "unit": "km",
                    "location_id": 2,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                    "emissions_factor_id": 2,
                    "emissions_factor_unit": "metric ton*km",
                },
                {
                    "node_id": 3,
                    "product_id": "1",
                    "name": "Citric Acid Transport 2",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 1000,
                    "unit": "km",
                    "location_id": 3,
                    "location_city": "New York",
                    "location_country": "United States",
                    "emissions_factor_id": 2,
                    "emissions_factor_unit": "metric ton*km",
                }
            ],
            [
                {
                    "from_node_id": 1,
                    "to_node_id": 2,
                    "product_id": "1",
                },
                {
                    "from_node_id": 1,
                    "to_node_id": 3,
                    "product_id": "1",
                },
            ],
        )
        walker = EmissionsWalker(
            "demo1",
            "1",
            process_model[0],
            process_model[1],
            impact_factors=[
                {
                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                    "impact_categories": ["climate change"],
                }
            ],
        )
        aggregation = walker.walk()

        self.assertEqual(
            len(aggregation.to_dict()["default"]["transportation"]["segment_nodes"]),
            2
        )
        for segment in aggregation.to_dict()["default"]["transportation"]["segment_nodes"]:
            self.assertEqual(
                segment["amount"],
                0.1,
            )

    @patch(
        "pipelines_v2.process_model_walker.emissions_walker.EmissionsWalker._get_emissions_values"
    )
    @patch("pipelines_v2.process_model_walker.emissions_walker.ProductRepo")
    def test_multiple_production_nodes(self, mock_product_repo, mock_get_emissions_factor_values):
        mock_product_repo = mock_product_repo.return_value
        mock_get_emissions_factor_values.return_value = [
            create_mock_tenant_emissions_factor_value(
                1,
                "Citric Acid",
                "Product",
                impact_amount=1,
            )
        ]
        mock_product_repo.get_product.return_value = Product(
            product_id="1",
            product_name="Product",
        )
        process_model = create_mock_process_model(
            [
                {
                    "node_id": 1,
                    "product_id": "1",
                    "name": "Citric Acid",
                    "node_type": NodeTypes.MATERIAL.value,
                    "quantity": 1,
                    "amount": 100,
                    "unit": "g",
                    "location_id": 1,
                    "location_city": "Chicago",
                    "location_country": "United States",
                },
                {
                    "node_id": 2,
                    "product_id": "1",
                    "name": "Product Manufacturing",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 2,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                },
                {
                    "node_id": 3,
                    "product_id": "1",
                    "name": "Product Manufacturing 2",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 3,
                    "location_city": "Los Angeles",
                    "location_country": "United States",
                }
            ],
            [
                {
                    "from_node_id": 1,
                    "to_node_id": 2,
                    "product_id": "1",
                },
                {
                    "from_node_id": 1,
                    "to_node_id": 3,
                    "product_id": "1",
                },
            ],
        )
        walker = EmissionsWalker(
            "demo1",
            "1",
            process_model[0],
            process_model[1],
            impact_factors=[
                {
                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                    "impact_categories": ["climate change"],
                }
            ],
        )
        aggregation = walker.walk()

        self.assertEqual(len(aggregation.to_dict()["default"]["production"]["segment_nodes"]), 2)
        for segment in aggregation.to_dict()["default"]["production"]["segment_nodes"]:
            self.assertEqual(
                next(
                    impact for impact in segment["impacts"]
                    if impact["emissions_factor_value"]["impact_indicator"]["lcia_method"] == "ReCiPe 2016 v1.03, midpoint (H)"
                    and impact["emissions_factor_value"]["impact_indicator"]["category"] == "climate change"
                    and impact["emissions_factor_value"]["impact_indicator"]["unit"] == "kg CO2e"
                )["impact_amount"],
                0.1,
            )

    @patch(
        "pipelines_v2.process_model_walker.emissions_walker.EmissionsWalker._get_emissions_values"
    )
    @patch("pipelines_v2.process_model_walker.emissions_walker.ProductRepo")
    @patch("pipelines_v2.process_model_walker.emissions_walker.get_org_metadata_field")
    def test_all_nodes_captured_in_aggregation(
        self,
        mock_get_org_metadata_field,
        mock_product_repo,
        mock_get_emissions_factor_values
    ):
        """Test that all nodes in the graph are captured in both default and PCR-specific segments."""
        # Get actual PCRs from the repository
        pcr_repo = PCRRepository()
        pcrs = pcr_repo.get_all_pcrs()

        # Verify BIFMA PCR exists
        bifma_pcr = next((pcr for pcr in pcrs if pcr.name == "BIFMA"), None)
        self.assertIsNotNone(bifma_pcr, "BIFMA PCR must be defined in the repository")

        # Create PCR category mappings for all PCRs
        pcr_mappings = {
            f"Category {pcr.name}": pcr.name for pcr in pcrs
        }
        mock_get_org_metadata_field.return_value = pcr_mappings

        # Create a process model with different types of nodes
        process_model = create_mock_process_model(
            [
                {
                    "node_id": 1,
                    "product_id": "1",
                    "name": "Raw Material",
                    "node_type": NodeTypes.MATERIAL.value,
                    "amount": 1.0,
                    "unit": "kg",
                    "location_id": 1,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 2,
                    "product_id": "1",
                    "name": "Material Transport",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 100,
                    "unit": "km",
                    "location_id": 2,
                    "emissions_factor_id": 1,
                    "emissions_factor_unit": "metric ton*km",
                },
                {
                    "node_id": 3,
                    "product_id": "1",
                    "name": "Initial Manufacturing",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 3,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 4,
                    "product_id": "1",
                    "name": "Packaging",
                    "node_type": NodeTypes.PACKAGING.value,
                    "amount": 0.1,
                    "unit": "kg",
                    "location_id": 4,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 5,
                    "product_id": "1",
                    "name": "Product Bundle",
                    "node_type": NodeTypes.BUNDLE.value,
                    "location_id": 5,
                },
                {
                    "node_id": 6,
                    "product_id": "1",
                    "name": "Additional Material",
                    "node_type": NodeTypes.MATERIAL.value,
                    "amount": 0.5,
                    "unit": "kg",
                    "location_id": 6,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 7,
                    "product_id": "1",
                    "name": "Final Manufacturing",
                    "node_type": NodeTypes.PRODUCTION.value,
                    "location_id": 7,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 8,
                    "product_id": "1",
                    "name": "Final Transport",
                    "node_type": NodeTypes.TRANSPORTATION.value,
                    "amount": 50,
                    "unit": "km",
                    "location_id": 8,
                    "emissions_factor_id": 1,
                    "emissions_factor_unit": "metric ton*km",
                },
                {
                    "node_id": 9,
                    "product_id": "1",
                    "name": "Product Use",
                    "node_type": NodeTypes.USE.value,
                    "amount": 1.0,
                    "unit": "kg",
                    "location_id": 9,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 10,
                    "product_id": "1",
                    "name": "Product EOL",
                    "node_type": NodeTypes.EOL.value,
                    "amount": 1.0,
                    "unit": "kg",
                    "location_id": 10,
                    "emissions_factor_id": 1,
                },
                {
                    "node_id": 11,
                    "product_id": "1",
                    "name": "Additional Material EOL",
                    "node_type": NodeTypes.EOL.value,
                    "amount": 0.5,
                    "unit": "kg",
                    "location_id": 11,
                    "emissions_factor_id": 1,
                }
            ],
            [
                {"from_node_id": 1, "to_node_id": 2, "product_id": "1"},      # Raw Material -> Transport
                {"from_node_id": 2, "to_node_id": 3, "product_id": "1"},      # Transport -> Initial Manufacturing
                {"from_node_id": 3, "to_node_id": 5, "product_id": "1"},      # Initial Manufacturing -> Bundle
                {"from_node_id": 4, "to_node_id": 5, "product_id": "1"},      # Packaging -> Bundle
                {"from_node_id": 5, "to_node_id": 7, "product_id": "1"},      # Bundle -> Final Manufacturing
                {"from_node_id": 6, "to_node_id": 7, "product_id": "1"},      # Additional Material -> Final Manufacturing
                {"from_node_id": 7, "to_node_id": 8, "product_id": "1"},      # Final Manufacturing -> Final Transport
                {"from_node_id": 7, "to_node_id": 11, "product_id": "1"},     # Final Manufacturing -> Additional Material EOL
                {"from_node_id": 8, "to_node_id": 9, "product_id": "1"},      # Final Transport -> Use
                {"from_node_id": 9, "to_node_id": 10, "product_id": "1"},     # Use -> Product EOL
            ],
        )

        mock_get_emissions_factor_values.return_value = [
            create_mock_tenant_emissions_factor_value(
                1,
                "Test Activity",
                "Product",
            )
        ]

        # Test each PCR category
        for pcr in pcrs:
            # Mock product with current PCR category
            category = f"Category {pcr.name}"
            mock_product_repo.return_value.get_product.return_value = Product(
                product_id="1",
                product_name="Product",
                brand="Brand",
                country_of_use="United States",
                uses_per_package=1,
                primary_category=category
            )

            walker = EmissionsWalker(
                "demo1",
                "1",
                process_model[0],
                process_model[1],
                impact_factors=[
                    {
                        "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                        "impact_categories": ["climate change"],
                    }
                ],
            )
            aggregation = walker.walk()
            agg_dict = aggregation.to_dict()

            # Get all node IDs from the aggregation object for default segments
            captured_node_ids = set()
            for segment_type in agg_dict["default"].values():
                for node in segment_type["segment_nodes"]:
                    captured_node_ids.add(node["node_id"])

            # Get all node IDs from the original process model, excluding BUNDLE nodes
            expected_node_ids = {node.id for node in process_model[0] if node.node_type != NodeTypes.BUNDLE.value}

            # Verify that all non-BUNDLE nodes are captured in default segments
            self.assertEqual(
                captured_node_ids,
                expected_node_ids,
                f"Not all nodes were captured in default aggregation for {pcr.name}. Missing nodes: {expected_node_ids - captured_node_ids}"
            )

            # Verify PCR-specific segments exist and contain the same nodes
            self.assertIn(
                pcr.name,
                agg_dict,
                f"{pcr.name} segments not found in aggregation"
            )

            # Get all node IDs from PCR-specific segments
            pcr_captured_node_ids = set()
            for segment_type in agg_dict[pcr.name].values():
                for node in segment_type["segment_nodes"]:
                    pcr_captured_node_ids.add(node["node_id"])

            # Verify that all non-BUNDLE nodes are captured in PCR-specific segments
            self.assertEqual(
                pcr_captured_node_ids,
                expected_node_ids,
                f"Not all nodes were captured in {pcr.name} aggregation. Missing nodes: {expected_node_ids - pcr_captured_node_ids}"
            )
