from typing import List, Any
from databases.tenant.models import (
    Node,
    Edge,
    TenantEmissionsFactor,
    Address,
    TenantEmissionsFactorValueRead,
    TenantImpactIndicator,
)


def create_mock_tenant_emissions_factor_value(
    emissions_factor_id: str,
    activity_name: str,
    reference_product: str,
    geography: str = "GLO",
    source: str = "Custom",
    lcia_method: str = "ReCiPe 2016 v1.03, midpoint (H)",
    impact_category: str = "climate change",
    impact_indicator: str = "GWP100",
    impact_amount: float = 0.1,
    impact_unit: str = "kg CO2e",
) -> TenantEmissionsFactorValueRead:
    return TenantEmissionsFactorValueRead(
        emissions_factor=TenantEmissionsFactor(
            id=emissions_factor_id,
            activity_name=activity_name,
            reference_product=reference_product,
            activity_description=f"{activity_name} Production Activity",
            reference_product_amount=1,
            unit=impact_unit,
            geography=geography,
            source=source,
        ),
        impact_indicator=TenantImpactIndicator(
            lcia_method=lcia_method,
            category=impact_category,
            indicator=impact_indicator,
            unit=impact_unit,
            description="",
        ),
        amount=impact_amount,
    )

def create_mock_node(
    node_id,
    product_id,
    name,
    node_type,
    quantity=1,
    amount=None,
    unit="kg",
    location_id=None,
    location_city=None,
    location_country=None,
    emissions_factor_id=None,
    emissions_factor_unit="kg",
):
    return Node(
        id=node_id,
        product_id=product_id,
        name=name,
        node_type=node_type,
        quantity=quantity,
        amount=amount,
        unit=unit,
        location_id=location_id,
        location=Address(
            id=location_id,
            city=location_city,
            country=location_country,
        ),
        emissions_factor_id=emissions_factor_id,
        emissions_factor=TenantEmissionsFactor(
            activity_name=f"{name} Activity",
            reference_product=name,
            activity_description=f"{name} Production Activity",
            reference_product_amount=1,
            unit=emissions_factor_unit,
            geography="US",
            source="Custom",
        )
    )

def create_mock_edge(from_node_id, to_node_id, product_id):
    return Edge(
        from_node_id=from_node_id,
        to_node_id=to_node_id,
        product_id=product_id,
    )

def create_mock_process_model(nodes: List[Any], edges: List[Any]):
    return [
        [create_mock_node(**node) for node in nodes],
        [create_mock_edge(**edge) for edge in edges],
    ]
