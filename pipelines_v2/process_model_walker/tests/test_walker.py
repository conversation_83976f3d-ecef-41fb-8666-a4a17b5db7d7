# pylint: disable=protected-access
from typing import List
from unittest import TestCase
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.models import (
    Node,
    Edge,
)
from pipelines_v2.process_model_walker.walker import <PERSON>
from pipelines_v2.process_model_walker.tests.lib import (
  create_mock_node,
  create_mock_edge
)


class DummyWalker(<PERSON>):
    def __init__(self, tenant_id: str, product_id: str, nodes: List[Node], edges: List[Edge]):
        super().__init__(tenant_id, product_id, nodes, edges)

    def _walk(self):
        for _ in self._graph.get_nodes_topologically_sorted():
            pass

class TestWalker(TestCase):
    nodes = [
        create_mock_node(1, "1", "Citric Acid", NodeTypes.MATERIAL.value,
                         amount=100, unit="g", quantity=2),
        create_mock_node(2, "1", "PET", NodeTypes.PACKAGING.value,
                         amount=25, unit="g", quantity=1),
        create_mock_node(3, "1", "Citric Acid Transport", NodeTypes.TRANSPORTATION.value),
        create_mock_node(4, "1", "PET Transport", NodeTypes.TRANSPORTATION.value),
        create_mock_node(5, "1", "Bundle", NodeTypes.BUNDLE.value),
        create_mock_node(6, "1", "Bundle Processing", NodeTypes.PRODUCTION.value, amount=0),
    ]

    edges = [
        create_mock_edge(1, 3, "1"),
        create_mock_edge(2, 4, "1"),
        create_mock_edge(3, 5, "1"),
        create_mock_edge(4, 5, "1"),
        create_mock_edge(5, 6, "1"),
    ]

    def test_walk(self):
        """Test that the walk method resets the graph after iterating all nodes."""
        walker = DummyWalker("1", "1", self.nodes, self.edges)
        walker.walk()
        self.assertGreater(len(walker._graph.get_nodes_topologically_sorted()), 0)

    def test_get_node_outputs(self):
        """Test that the get_node_outputs method returns the correct outputs for a node."""
        walker = DummyWalker("1", "1", self.nodes, self.edges)
        outputs = walker._get_node_outputs(self.nodes[-1])
        self.assertEqual(
            outputs["total_weight"]["amount"],
            0.225,
        )

        self.assertEqual(
            outputs["total_weight"]["unit"],
            "kg",
        )
