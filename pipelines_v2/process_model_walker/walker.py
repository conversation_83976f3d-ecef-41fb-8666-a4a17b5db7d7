from typing import Any
from abc import ABC, abstractmethod
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.models import Node, Edge
from pipelines_v2.graph import Graph
from pipelines_v2.utils.unit import UnitType


class NodeCache:
    def __init__(self):
        self._cache = {}

    def get(self, node_id: int) -> dict | None:
        return self._cache.get(node_id)

    def set(self, node_id: int, value: dict):
        self._cache[node_id] = value

class Walker(ABC):
    def __init__(
        self,
        tenant_id: str,
        product_id: str,
        nodes: list[Node],
        edges: list[Edge],
    ):
        self._tenant_id = tenant_id
        self._product_id = product_id
        self._nodes = nodes
        self._edges = edges
        self._graph = Graph(self._nodes, self._edges)
        self._node_cache = NodeCache()

    def walk(self) -> Any:
        outputs = self._walk()
        self._graph = Graph(self._nodes, self._edges)

        return outputs

    @abstractmethod
    def _walk(self):
        """This method should be implemented by the subclass."""

    def _get_node_outputs(self, node: Node) -> dict:
        cached_outputs = self._node_cache.get(node.id)
        if cached_outputs:
            return cached_outputs

        outputs = {
            "total_weight": {
                "amount": 0,
                "unit": "kg",
            },
            "total_material_weight": {
                "amount": 0,
                "unit": "kg",
            },
            "total_volume": {
                "amount": 0,
                "unit": "l",
            },
            "total_energy": {
                "amount": 0,
                "unit": "kWh",
            },
            NodeTypes.MATERIAL.value: [],
            NodeTypes.PACKAGING.value: [],
        }
        stack = [node]
        visited_nodes = set()

        while stack:
            current_node = stack.pop()

            if current_node.id in visited_nodes:
                continue

            visited_nodes.add(current_node.id)

            if current_node.node_type in [NodeTypes.MATERIAL, NodeTypes.PACKAGING]:
                unit = UnitType.get_by_abbrev(current_node.unit).value
                if unit.category == "volume":
                    base_unit = "l"
                    agg_key = "total_volume"
                elif unit.category == "weight":
                    base_unit = "kg"
                    agg_key = "total_weight"
                elif unit.category == "energy":
                    base_unit = "kWh"
                    agg_key = "total_energy"
                else:
                    raise ValueError(f"Unsupported unit category: {unit.category}")

                amount = UnitType.convert_from_abbrev(
                    current_node.amount,
                    current_node.unit,
                    base_unit,
                )
                outputs[agg_key]["amount"] += amount * current_node.quantity

                if current_node.node_type == NodeTypes.MATERIAL and unit.category == "weight":
                    outputs["total_material_weight"]["amount"] += amount * current_node.quantity

                outputs[current_node.node_type].append(current_node)
            else:
                incoming_nodes = self._graph.get_incoming_nodes(current_node)
                for incoming_node in incoming_nodes:
                    if incoming_node.id not in visited_nodes:
                        stack.append(incoming_node)

        self._node_cache.set(node.id, outputs)
        return outputs
