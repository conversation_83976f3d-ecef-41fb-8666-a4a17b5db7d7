from typing import List, <PERSON><PERSON>
import asyncio
from databases.shared.constants.node_type import NodeTypes
from databases.shared.repository.waste_disposal_method_emission import (
    WasteDisposalMethodEmissionRepo,
)
from databases.tenant.models import Node, Edge
from databases.tenant.repository import TenantEmissionsFactorRepo, ProductRepo
from services.emissions_factors_service import EmissionsFactorsService
from pipelines_v2.graph import Graph
from pipelines_v2.process_model_walker.walker import <PERSON>
from pipelines_v2.transport.route import Route
from pipelines_v2.transport.constants import DefaultTransportEmissionsFactors, TransportMode
from clients.ml_models import MLModels
from utils.async_call import safe_async
from utils.geocoding import get_country_info

DEFAULT_EOL_ACTIVITY = {
    "activity_name": "treatment of municipal solid waste, sanitary landfill",
    "geography": "RoW",
    "reference_product_name": "municipal solid waste",
    "source": "Ecoinvent 3.11",
}

async def get_eol_emissions_factors(
    ml_models: MLModels,
    materials: List[Tuple[str, str]],
    chunk_size: int=20,
) -> dict[Tuple[str, str], dict]:
    materials_to_emissions_factors = {}
    semaphore = asyncio.Semaphore(chunk_size)

    async def get_eol_emissions_factor(material: str, disposal_method: str):
        async with semaphore:
            return await ml_models.predict_eol_activity(
                material,
                disposal_method,
            )

    tasks = [
        get_eol_emissions_factor(material, disposal_method)
        for material, disposal_method in materials
    ]
    results = await asyncio.gather(*tasks)

    for (material, disposal_method), emissions_factor in zip(materials, results):
        if emissions_factor:
            materials_to_emissions_factors[(material, disposal_method)] = emissions_factor

    return materials_to_emissions_factors

class PreprocessWalker(Walker):
    """
    This walker is used to preprocess the process model before being created.
    """
    def __init__(
        self,
        tenant_id: str,
        product_id: str,
        nodes: list[Node],
        edges: list[Edge],
    ):
        self._product_repo = ProductRepo(tenant_id)
        self._product = self._product_repo.get_product(product_id)

        if not self._product:
            raise ValueError(f"Product not found: {product_id}")

        self._emissions_factors_service = EmissionsFactorsService()
        self._tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        self._ml_models = MLModels()
        super().__init__(tenant_id, product_id, nodes, edges)

    def _walk(self):
        output_graph = self._graph.clone()

        # Get a static list of nodes in topological order
        # This ensures that skip_node operations won't affect which nodes get processed
        nodes_to_process = self._graph.get_nodes_topologically_sorted()

        for node in nodes_to_process:
            if node.node_type == NodeTypes.PRODUCTION.value:
                self._visit_production(node, output_graph)
            elif (
                node.node_type == NodeTypes.TRANSPORTATION and
                node.segment_type == "auto"
            ):
                self._visit_auto_transportation(node, output_graph)
            elif (
                node.node_type == NodeTypes.EOL.value and
                node.segment_type == "auto"
            ):
                self._visit_eol(node, output_graph)

        return output_graph.get_nodes(), output_graph.get_edges()

    def _visit_production(self, node: Node, output_graph: Graph):
        """Handle production/manufacturing with scrap.
        """

        if not node.scrap_rate:
            return

        incoming_production_nodes = self._graph.get_incoming_nodes_by_type(
            node,
            [NodeTypes.PRODUCTION.value]
        )
        material_nodes = []
        if not incoming_production_nodes:
            incoming_material_nodes = self._graph.get_incoming_nodes_by_type(
                node,
                [NodeTypes.MATERIAL.value]
            )
            material_nodes.extend(incoming_material_nodes)

        for _material_node in material_nodes:
            material_node = output_graph.get_node(_material_node.id)
            if not material_node.amount:
                continue

            scrap_amount = material_node.amount * (node.scrap_rate / 100)
            scrap_fate = node.scrap_fate or "recycling"
            scrap_disposal_name = f"{material_node.name} scrap disposal ({scrap_fate})"

            # store scrap info to material node
            material_node.scrap_rate = node.scrap_rate
            material_node.scrap_fate = scrap_fate

            # check if scrap disposal node is already added
            # components may already have scrap disposal nodes, so when importing parts list,
            # we need to skip adding the same node again
            if output_graph.get_node_by_name_and_type(scrap_disposal_name, NodeTypes.EOL.value):
                continue

            try:
                _emissions_factor = safe_async(
                    get_eol_emissions_factors,
                    self._ml_models,
                    [
                        (material_node.name, scrap_fate)
                    ],
                )
                emissions_factor = _emissions_factor.get((material_node.name, scrap_fate))

                if emissions_factor:
                    tenant_emissions_factor = self._tenant_emissions_factors.get_emissions_factor(
                        activity_name=emissions_factor["activity_name"],
                        geography=emissions_factor["geography"],
                        reference_product=emissions_factor["reference_product_name"],
                        source=emissions_factor["source"],
                    )

                    if not tenant_emissions_factor:
                        tenant_emissions_factor, _ = (
                            self._emissions_factors_service
                            .copy_emissions_factor_to_tenant_emissions_factors(
                                self._tenant_id,
                                emissions_factor["activity_name"],
                                emissions_factor["reference_product_name"],
                                emissions_factor["geography"],
                                emissions_factor["source"],
                            )
                        )
            except Exception:
                tenant_emissions_factor = self._tenant_emissions_factors.get_emissions_factor(
                    activity_name=DEFAULT_EOL_ACTIVITY["activity_name"],
                    geography=DEFAULT_EOL_ACTIVITY["geography"],
                    reference_product=DEFAULT_EOL_ACTIVITY["reference_product_name"],
                    source=DEFAULT_EOL_ACTIVITY["source"],
                )

                if not tenant_emissions_factor:
                    tenant_emissions_factor, _ = (
                        self._emissions_factors_service
                        .copy_emissions_factor_to_tenant_emissions_factors(
                            self._tenant_id,
                            DEFAULT_EOL_ACTIVITY["activity_name"],
                            DEFAULT_EOL_ACTIVITY["reference_product_name"],
                            DEFAULT_EOL_ACTIVITY["geography"],
                            DEFAULT_EOL_ACTIVITY["source"],
                        )
                    )

            eol_node = Node(
                product_id=self._product.product_id,
                name=scrap_disposal_name,
                component_name=material_node.component_name,
                node_type=NodeTypes.EOL.value,
                amount=scrap_amount,
                unit=material_node.unit,
                quantity=1,
                emissions_factor_id=tenant_emissions_factor.id,
                segment_type="auto",
            )
            eol_node = output_graph.insert_node(eol_node)

            edge = Edge(
                product_id=self._product.product_id,
                from_node_id=node.id,
                to_node_id=eol_node.id,
            )
            output_graph.insert_edges([edge])

    def _visit_eol(self, node: Node, output_graph: Graph):
        # Guard condition: Check if the node still exists in output_graph
        try:
            # Check if the node (the "auto" EOL placeholder from the static iteration list)
            # still exists in the output_graph.
            output_graph.get_node(node.id)
        except KeyError:
            # Node was already removed from output_graph
            # (likely by processing a sibling "auto" EOL node).
            # Nothing more to do for this placeholder.
            return

        # If the origin node is a production node, skip creating eol nodes
        origin_node = output_graph.get_incoming_nodes(node)[0]
        if origin_node.node_type == NodeTypes.PRODUCTION:
            return

        node_outputs = self._get_node_outputs(node)
        include_raw_materials = safe_async(
            self._ml_models.predict_has_eol,
            self._product.primary_category,
        )
        materials = node_outputs[NodeTypes.PACKAGING.value]
        if include_raw_materials:
            materials += node_outputs[NodeTypes.MATERIAL.value]

        # Find all "auto" EOL children of this origin_node that are currently in output_graph
        all_auto_eol_placeholders_for_origin = []
        for outgoing_edge in output_graph.get_outgoing_edges(origin_node):
            try:
                child_node = output_graph.get_node(outgoing_edge.to_node_id)
                if (child_node.node_type == NodeTypes.EOL.value and
                    child_node.segment_type == "auto"):
                    all_auto_eol_placeholders_for_origin.append(child_node)
            except KeyError:
                # Child node might have been removed in a previous iteration; ignore.
                pass

        # Remove all these "auto" EOL placeholders related to this origin_node from output_graph
        if all_auto_eol_placeholders_for_origin:
            output_graph.remove_nodes(all_auto_eol_placeholders_for_origin)

        waste_disposal_method_emission_repo = WasteDisposalMethodEmissionRepo()

        edges = []

        materials_with_disposal_methods = []

        origin_country_info = get_country_info(origin_node.location.country)

        for material in materials:
            material_name = safe_async(
                self._ml_models.predict_eol_category,
                material.name,
            )
            if not material_name:
                continue

            waste_disposal_methods = waste_disposal_method_emission_repo.get_by_packaging_material(
                material_name,
                origin_country_info.alpha_3_code,
            )
            if not waste_disposal_methods:
                waste_disposal_methods = (
                    waste_disposal_method_emission_repo.get_by_packaging_material(
                        material_name,
                        "GBR",
                    )
                )
            materials_with_disposal_methods.extend(
                {
                    "name": material.name,
                    "disposal_method": disposal_method.waste_disposal_method,
                    "rate": disposal_method.rate,
                    "amount": material.amount,
                    "unit": material.unit,
                    "component_name": material.component_name,
                    "description": material.description,
                }
                for disposal_method in waste_disposal_methods
            )

        materials_to_emissions_factors = safe_async(
            get_eol_emissions_factors,
            self._ml_models,
            [
                (material["name"], material["disposal_method"])
                for material in materials_with_disposal_methods
            ],
        )

        for material in materials_with_disposal_methods:
            name = f"{material['name']} disposal ({material['disposal_method']})"
            try:
                emissions_factor = materials_to_emissions_factors.get(
                    (material["name"], material["disposal_method"])
                )
                tenant_emissions_factor = self._tenant_emissions_factors.get_emissions_factor(
                    activity_name=emissions_factor["activity_name"],
                    geography=emissions_factor["geography"],
                    reference_product=emissions_factor["reference_product_name"],
                    source=emissions_factor["source"],
                )

                if not tenant_emissions_factor:
                    tenant_emissions_factor, _ = (
                        self._emissions_factors_service
                        .copy_emissions_factor_to_tenant_emissions_factors(
                            self._tenant_id,
                            emissions_factor["activity_name"],
                            emissions_factor["reference_product_name"],
                            emissions_factor["geography"],
                            emissions_factor["source"],
                        )
                    )
            except Exception:
                tenant_emissions_factor = self._tenant_emissions_factors.get_emissions_factor(
                    activity_name=DEFAULT_EOL_ACTIVITY["activity_name"],
                    geography=DEFAULT_EOL_ACTIVITY["geography"],
                    reference_product=DEFAULT_EOL_ACTIVITY["reference_product_name"],
                    source=DEFAULT_EOL_ACTIVITY["source"],
                )

                if not tenant_emissions_factor:
                    tenant_emissions_factor, _ = (
                        self._emissions_factors_service
                        .copy_emissions_factor_to_tenant_emissions_factors(
                            self._tenant_id,
                            DEFAULT_EOL_ACTIVITY["activity_name"],
                            DEFAULT_EOL_ACTIVITY["reference_product_name"],
                            DEFAULT_EOL_ACTIVITY["geography"],
                            DEFAULT_EOL_ACTIVITY["source"],
                        )
                    )

            node = Node(
                product_id=self._product.product_id,
                name=name,
                component_name=material["component_name"],
                description=material["description"],
                node_type=NodeTypes.EOL.value,
                amount=material["amount"] * material["rate"],
                unit=material["unit"],
                quantity=1,
                emissions_factor_id=tenant_emissions_factor.id,
                segment_type="auto",
            )
            node = output_graph.insert_node(node)
            edges.append(
                Edge(
                    product_id=self._product.product_id,
                    from_node_id=origin_node.id,
                    to_node_id=node.id,
                )
            )

        output_graph.insert_edges(edges)

    def _visit_auto_transportation(self, node: Node, output_graph: Graph):
        """
        Remove the auto transportation node and replace it with a series
        of transportation nodes and edges.

        Example (where `{AUTO_TRANSPORTATION_NODE}` is an auto transportation node):
        - Input:
        MATERIAL_NODE -> {AUTO_TRANSPORTATION_NODE} -> MANUFACTURING_NODE

        - Output:
        MATERIAL_NODE -> {TRANSPORT_NODE_1 -> TRANSPORT_NODE_2 -> ... -> TRANSPORT_NODE_N}
        -> MANUFACTURING_NODE

        `AUTO_TRANSPORTATION_NODE` is replaced by a series of transportation nodes and edges.
        """
        # check if node has already been deleted from output_graph
        if node not in output_graph.get_nodes():
            return

        incoming_nodes = output_graph.get_incoming_nodes(node)
        if len(incoming_nodes) != 1:
            raise ValueError(f"Expected 1 incoming node, got {len(incoming_nodes)}")

        origin_node = incoming_nodes[0]
        nodes_to_remove = set()
        current_node = node

        while (
            current_node.node_type == NodeTypes.TRANSPORTATION and
            current_node.segment_type == "auto"
        ):
            nodes_to_remove.add(current_node)
            outgoing_nodes = output_graph.get_outgoing_nodes(current_node)
            if len(outgoing_nodes) != 1:
                raise ValueError(f"Expected 1 outgoing node, got {len(outgoing_nodes)}")

            current_node = outgoing_nodes[0]

        destination_node = current_node
        output_graph.remove_nodes(list(nodes_to_remove))

        route = Route(origin_node.location, destination_node.location)
        distance = route.calculate_total_distance()

        nodes = []
        for i, segment in enumerate(distance.segments):
            if segment.mode == TransportMode.HGV.value:
                emissions_factor = DefaultTransportEmissionsFactors.hgv
            elif segment.mode == TransportMode.CONTAINER_SHIP.value:
                emissions_factor = DefaultTransportEmissionsFactors.container_ship
            else:
                raise ValueError(f"Unknown transport mode: {segment.mode}")

            tenant_emissions_factor = self._tenant_emissions_factors.get_emissions_factor(
                activity_name=emissions_factor.activity_name,
                geography=emissions_factor.geography,
                reference_product=emissions_factor.reference_product_name,
                source=emissions_factor.source,
            )
            if not tenant_emissions_factor:
                tenant_emissions_factor, _ = (
                    self._emissions_factors_service
                    .copy_emissions_factor_to_tenant_emissions_factors(
                        self._tenant_id,
                        emissions_factor.activity_name,
                        emissions_factor.reference_product_name,
                        emissions_factor.geography,
                        emissions_factor.source,
                    )
                )

            _node = Node(
                product_id=self._product.product_id,
                name=f"{origin_node.name} Transportation Segment {i+1}",
                component_name=node.component_name,
                description=node.description,
                node_type=NodeTypes.TRANSPORTATION.value,
                amount=segment.distance,
                unit=segment.unit,
                quantity=1,
                transport_mode=segment.mode,
                emissions_factor_id=tenant_emissions_factor.id,
                segment_type="auto",
            )
            nodes.append(_node)

        edges = []
        for node_ in [(nodes[i], nodes[i + 1]) for i in range(len(nodes) - 1)]:
            edges.append(
                Edge(
                    product_id=self._product.product_id,
                    from_node_id=node_[0].id,
                    to_node_id=node_[1].id,
                )
            )

        current_edge = Edge(
            product_id=self._product.product_id,
            from_node_id=origin_node.id,
            to_node_id=destination_node.id,
        )

        output_graph.insert_edge(current_edge)

        for node_ in nodes:
            output_graph.insert_between_nodes(current_edge, node_)
            current_edge = Edge(
                product_id=self._product.product_id,
                from_node_id=node_.id,
                to_node_id=destination_node.id,
            )
