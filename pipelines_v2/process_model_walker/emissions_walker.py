from typing import List
from databases.shared.constants.node_type import NodeTypes
from databases.shared.models import PCRRule
from databases.tenant.models import Node, Edge, TenantEmissionsFactorValueRead
from databases.shared.repository import PCRRepository
from databases.tenant.repository import (
  ProductRepo,
  TenantEmissionsFactorRepo,
  TenantEmissionsFactorValueRepo,
  TenantImpactIndicatorRepo,
)
from services.process_model_service import ProcessModelService
from pipelines_v2.pcr_rule_runner import RuleRunner
from pipelines_v2.process_model_walker.walker import <PERSON>
from pipelines_v2.aggregator import Aggregation
from pipelines_v2.utils.unit import UnitType
from pipelines_v2.graph import CyclicDependencyError
from clients.propelauth import get_org_metadata_field
from utils.logger import logger

class EmissionsWalkerError(Exception):
    pass

class EmissionsWalker(Walker):
    def __init__(
        self,
        tenant_id: str,
        product_id: str,
        nodes: list[Node],
        edges: list[Edge],
        impact_factors: dict[str, list[dict[str, str]]],
    ):
        self._service = ProcessModelService(tenant_id)
        self._tenant_emissions_factor_repo = TenantEmissionsFactorRepo(tenant_id)
        self._tenant_emissions_factor_value_repo = TenantEmissionsFactorValueRepo(tenant_id)
        self._tenant_impact_indicator_repo = TenantImpactIndicatorRepo(tenant_id)
        self._product_repo = ProductRepo(tenant_id)
        self._product = self._product_repo.get_product(product_id)
        self._impact_factors = impact_factors
        self._impact_indicator_ids_cache = {}

        self._pcr_category = (
            (get_org_metadata_field(tenant_id, "pcr_categories") or {})
            .get(self._product.primary_category)
        )
        self._pcr_rules = (
            PCRRepository().get_pcr_rules_by_pcr_name(self._pcr_category)
            if self._pcr_category
            else None
        )

        if not self._product:
            raise ValueError(f"Product not found: {product_id}")

        super().__init__(tenant_id, product_id, nodes, edges)

        if self._pcr_rules:
            self._rule_runner = RuleRunner(self._pcr_rules, self._graph)
        else:
            self._rule_runner = None

    def _visit(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        try:

            if node.node_type == NodeTypes.TRANSPORTATION:
                self._visit_transportation(node, aggregation, rule)
            elif node.node_type == NodeTypes.PRODUCTION:
                self._visit_production(node, aggregation, rule)
            elif node.node_type == NodeTypes.MATERIAL:
                self._visit_material(node, aggregation, rule)
            elif node.node_type == NodeTypes.PACKAGING:
                self._visit_packaging(node, aggregation, rule)
            elif node.node_type == NodeTypes.BUNDLE:
                pass
            elif node.node_type == NodeTypes.USE:
                self._visit_use(node, aggregation, rule)
            elif node.node_type == NodeTypes.EOL:
                self._visit_eol(node, aggregation, rule)
            else:
                logger.info(f"Unknown node type: {node.node_type}")
        except Exception as error:
            raise EmissionsWalkerError(f"Error visiting node {node.name}: {error}") from error

    def _visit_transportation(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        total_weight = self._get_node_outputs(node)["total_weight"]["amount"]
        unit = self._get_node_outputs(node)["total_weight"]["unit"]

        #incorporate scrap amount in total weight
        incoming_production_nodes = self._graph.get_incoming_nodes_by_type(
            node,
            [NodeTypes.PRODUCTION.value],
        )
        incoming_assembly_node = [
            node for node in self._graph.get_incoming_nodes_by_type(
                    node,
                    [NodeTypes.BUNDLE.value],
                ) if node.name == "Product Assembly"
        ]
        if not incoming_production_nodes and not incoming_assembly_node:
            incoming_material_nodes = self._graph.get_incoming_nodes_by_type(
                node,
                [NodeTypes.MATERIAL.value],
            )
            for incoming_material_node in incoming_material_nodes:
                if incoming_material_node.scrap_rate and incoming_material_node.scrap_rate > 0:
                    if incoming_material_node.scrap_fate != "Fed back into system":
                        total_weight = total_weight * (1 + incoming_material_node.scrap_rate / 100)

        amount = UnitType.convert_from_abbrev(
            total_weight * node.amount,
            f"{unit}*km",
            node.emissions_factor.unit,
        )
        emissions_factor_values = self._get_emissions_values(
            node.emissions_factor.activity_name,
            node.emissions_factor.reference_product,
            node.emissions_factor.geography,
            node.emissions_factor.source,
        )

        aggregation.add_segment_node(
            pcr_name="default",
            segment_name="transportation",
            sequence_number=3,
            amount=amount * node.quantity,
            node_id=node.id,
            node_name=node.name,
            unit=node.emissions_factor.unit,
            emissions_factor_values=emissions_factor_values,
        )

        if rule:
            aggregation.add_segment_node(
                pcr_name=self._pcr_category,
                segment_name=rule.segment,
                sequence_number=rule.sequence_number,
                amount=amount * node.quantity,
                node_id=node.id,
                node_name=node.name,
                unit=node.emissions_factor.unit,
                emissions_factor_values=emissions_factor_values,
            )

    def _visit_production(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        total_weight = self._get_node_outputs(node)["total_material_weight"]["amount"]
        unit = self._get_node_outputs(node)["total_material_weight"]["unit"]
        amount = UnitType.convert_from_abbrev(
            total_weight,
            unit,
            node.emissions_factor.unit,
        )

        if node.scrap_rate and node.scrap_rate > 0:
            if node.scrap_fate == "Fed back into system":
                amount = amount * (1 + node.scrap_rate / 100)

        emissions_factor_values = self._get_emissions_values(
            node.emissions_factor.activity_name,
            node.emissions_factor.reference_product,
            node.emissions_factor.geography,
            node.emissions_factor.source,
        )

        aggregation.add_segment_node(
            pcr_name="default",
            segment_name="production",
            sequence_number=4,
            amount=amount * node.quantity,
            node_id=node.id,
            node_name=node.name,
            unit=node.emissions_factor.unit,
            emissions_factor_values=emissions_factor_values,
        )

        if rule:
            aggregation.add_segment_node(
                pcr_name=self._pcr_category,
                segment_name=rule.segment,
                sequence_number=rule.sequence_number,
                amount=amount * node.quantity,
                node_id=node.id,
                node_name=node.name,
                unit=node.emissions_factor.unit,
                emissions_factor_values=emissions_factor_values,
            )

    def _visit_material(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        amount = UnitType.convert_from_abbrev(
            node.amount,
            node.unit,
            node.emissions_factor.unit,
        )

        if node.scrap_rate and node.scrap_rate > 0:
            if node.scrap_fate != "Fed back into system":
                amount = amount * (1 + node.scrap_rate / 100)

        emissions_factor_values = self._get_emissions_values(
            node.emissions_factor.activity_name,
            node.emissions_factor.reference_product,
            node.emissions_factor.geography,
            node.emissions_factor.source,
        )

        aggregation.add_segment_node(
            pcr_name="default",
            segment_name="materials",
            sequence_number=1,
            amount=amount * node.quantity,
            node_id=node.id,
            node_name=node.name,
            unit=node.emissions_factor.unit,
            emissions_factor_values=emissions_factor_values,
        )

        if rule:
            aggregation.add_segment_node(
                pcr_name=self._pcr_category,
                segment_name=rule.segment,
                sequence_number=rule.sequence_number,
                amount=amount * node.quantity,
                node_id=node.id,
                node_name=node.name,
                unit=node.emissions_factor.unit,
                emissions_factor_values=emissions_factor_values,
            )

    def _visit_packaging(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        amount = UnitType.convert_from_abbrev(
            node.amount,
            node.unit,
            node.emissions_factor.unit,
        )

        emissions_factor_values = self._get_emissions_values(
            node.emissions_factor.activity_name,
            node.emissions_factor.reference_product,
            node.emissions_factor.geography,
            node.emissions_factor.source,
        )

        aggregation.add_segment_node(
            pcr_name="default",
            segment_name="packaging",
            sequence_number=2,
            amount=amount * node.quantity,
            node_id=node.id,
            node_name=node.name,
            unit=node.emissions_factor.unit,
            emissions_factor_values=emissions_factor_values,
        )

        if rule:
            aggregation.add_segment_node(
                pcr_name=self._pcr_category,
                segment_name=rule.segment,
                sequence_number=rule.sequence_number,
                amount=amount * node.quantity,
                node_id=node.id,
                node_name=node.name,
                unit=node.emissions_factor.unit,
                emissions_factor_values=emissions_factor_values,
            )

    def _visit_use(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        amount = UnitType.convert_from_abbrev(
            node.amount,
            node.unit,
            node.emissions_factor.unit,
        )
        emissions_factor_values = self._get_emissions_values(
            node.emissions_factor.activity_name,
            node.emissions_factor.reference_product,
            node.emissions_factor.geography,
            node.emissions_factor.source,
        )
        number_of_uses = self._product.uses_per_package or 1

        aggregation.add_segment_node(
            pcr_name="default",
            segment_name="use",
            sequence_number=5,
            amount=amount * number_of_uses * node.quantity,
            node_id=node.id,
            node_name=node.name,
            unit=node.emissions_factor.unit,
            emissions_factor_values=emissions_factor_values,
        )

        if rule:
            aggregation.add_segment_node(
                pcr_name=self._pcr_category,
                segment_name=rule.segment,
                sequence_number=rule.sequence_number,
                amount=amount * number_of_uses * node.quantity,
                node_id=node.id,
                node_name=node.name,
                unit=node.emissions_factor.unit,
                emissions_factor_values=emissions_factor_values,
            )

    def _visit_eol(
        self,
        node: Node,
        aggregation: Aggregation,
        rule: PCRRule | None = None,
    ):
        amount = UnitType.convert_from_abbrev(
            node.amount * node.quantity,
            node.unit,
            node.emissions_factor.unit,
        )
        emissions_factor_values = self._get_emissions_values(
            node.emissions_factor.activity_name,
            node.emissions_factor.reference_product,
            node.emissions_factor.geography,
            node.emissions_factor.source,
        )

        aggregation.add_segment_node(
            pcr_name="default",
            segment_name="eol",
            sequence_number=6,
            amount=amount,
            node_id=node.id,
            node_name=node.name,
            unit=node.emissions_factor.unit,
            emissions_factor_values=emissions_factor_values,
        )

        if rule:
            aggregation.add_segment_node(
                pcr_name=self._pcr_category,
                segment_name=rule.segment,
                sequence_number=rule.sequence_number,
                amount=amount,
                node_id=node.id,
                node_name=node.name,
                unit=node.emissions_factor.unit,
                emissions_factor_values=emissions_factor_values,
            )

    def _verify_node_capture(self, aggregation: Aggregation):
        """Verify that all nodes in the graph are captured in appropriate segments."""
        # Get all non-BUNDLE nodes from the graph
        all_nodes = self._graph.get_nodes()
        logger.info(f"Total nodes in graph: {len(all_nodes)}")

        expected_nodes = {
            node.id for node in all_nodes if node.node_type != NodeTypes.BUNDLE.value
        }

        agg_dict = aggregation.to_dict()

        # Verify nodes for each category (default and PCR if present)
        for category in agg_dict.keys():
            self._verify_category_nodes(
                category=category,
                category_dict=agg_dict[category],
                expected_nodes=expected_nodes,
                all_nodes=all_nodes
            )

    def _verify_category_nodes(
        self,
        category: str,
        category_dict: dict,
        expected_nodes: set,
        all_nodes: list[Node]
    ):
        """Helper method to verify nodes for a given category (default or PCR).

        Args:
            category: The category name (default or PCR category)
            category_dict: Dictionary containing segment information for the category
            expected_nodes: Set of node IDs that should be captured
            all_nodes: List of all nodes in the graph
        """
        captured_nodes = set()
        segment_map = {}  # node_id -> list of segments it appears in

        # Collect nodes and their segments
        for segment_name, segment_type in category_dict.items():
            for node in segment_type["segment_nodes"]:
                node_id = node["node_id"]
                captured_nodes.add(node_id)
                if node_id not in segment_map:
                    segment_map[node_id] = []
                segment_map[node_id].append(segment_name)

        # Check for nodes in multiple segments
        for node_id, segments in segment_map.items():
            if len(segments) > 1:
                node = next(n for n in all_nodes if n.id == node_id)
                msg = f"Node appears in multiple {category} segments: "
                msg += f"Node(id={node.id}, name='{node.name}', type='{node.node_type}') "
                msg += f"appears in segments: {', '.join(segments)}"
                raise EmissionsWalkerError(msg)

        # Verify all nodes are captured
        missing_nodes = expected_nodes - captured_nodes
        if missing_nodes:
            self._raise_missing_nodes_error(category, missing_nodes, all_nodes)

    def _raise_missing_nodes_error(
        self,
        segment_type: str,
        missing_node_ids: set,
        all_nodes: list[Node]
    ):
        """Helper method to generate and raise error for missing nodes.

        Args:
            segment_type: The type of segment (default or PCR category)
            missing_node_ids: Set of node IDs that are missing
            all_nodes: List of all nodes in the graph
        """
        missing_nodes_info = []
        for node_id in missing_node_ids:
            node = next(n for n in all_nodes if n.id == node_id)
            missing_nodes_info.append(
                f"Node(id={node.id}, name='{node.name}', type='{node.node_type}')"
            )
        msg = f"Not all nodes were captured in {segment_type} segments.\n"
        msg += f"Missing nodes: {', '.join(missing_nodes_info)}"
        raise EmissionsWalkerError(msg)

    def _walk(self):
        aggregation = Aggregation()
        try:
            # Use topological sort instead of BFS traversal
            # This ensures nodes are processed in dependency order
            for node in self._graph.get_nodes_topologically_sorted():
                if self._rule_runner:
                    rule = self._rule_runner.get_rule_match(node)
                else:
                    rule = None
                self._visit(node, aggregation, rule)
        except CyclicDependencyError as error:
            # Convert cycle detection error into an EmissionsWalkerError with more context
            raise EmissionsWalkerError(
                "Invalid process model: Graph contains a cycle. "
                "LCA calculations require a directed acyclic graph (DAG). "
                "Please check your process model for circular dependencies."
            ) from error

        # Verify all nodes are captured in appropriate segments
        self._verify_node_capture(aggregation)

        return aggregation

    def _get_emissions_values(
        self,
        activity_name: str,
        reference_product: str,
        geography: str,
        source: str,
    ) -> List[TenantEmissionsFactorValueRead]:
        emissions_factor = self._tenant_emissions_factor_repo.get_emissions_factor(
            activity_name=activity_name,
            reference_product=reference_product,
            geography=geography,
            source=source,
        )
        if not emissions_factor:
            raise ValueError(
                f"Emissions factor not found for {activity_name} "
                f"{reference_product} {geography} {source}"
            )

        emissions_factor_values = []

        for impact_factor in self._impact_factors:
            impact_indicator_ids = self._get_impact_indicator_ids(impact_factor)

            emissions_factor_values.extend(
                self._tenant_emissions_factor_value_repo
                .get_emissions_factor_values(
                    emissions_factor.id,
                    impact_indicator_ids,
                )
            )

        if not emissions_factor_values:
            raise ValueError(
                f"Emissions factor values not found for {activity_name} "
                f"{reference_product} {geography} {source}"
            )

        return emissions_factor_values

    def _get_impact_indicator_ids(self, impact_factor):
        cache_key = (
            impact_factor["lcia_method"],
            tuple(sorted(impact_factor["impact_categories"]))
        )

        if cache_key in self._impact_indicator_ids_cache:
            return self._impact_indicator_ids_cache[cache_key]

        impact_indicator_ids = [
            self._tenant_impact_indicator_repo.get_impact_indicator(
                lcia_method=impact_factor["lcia_method"],
                category=impact_indicator,
            ).id
            for impact_indicator in impact_factor["impact_categories"]
        ]

        self._impact_indicator_ids_cache[cache_key] = impact_indicator_ids
        return impact_indicator_ids
