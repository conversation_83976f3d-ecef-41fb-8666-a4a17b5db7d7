# pylint: disable=line-too-long,cyclic-import
from typing import List
from pydantic import BaseModel
from databases.shared.models.wits_trade_data import TradeFlow
from databases.shared.repository import (
    fetch_trade_data,
    fetch_country_by_name,
    fetch_domestic_production_data,
    fetch_hscode_product_category,
)
from clients.ml_models import MLModels, MLModelsRequestException
from utils.async_call import safe_async
from utils.geocoding import get_country_info
from utils.logger import logger_instance
from pipelines_v2.ingredient_analysis.helpers import (
    sanitize_ingredient_name,
    format_exporter_data,
)


logger = logger_instance.get_logger()
ml_models = MLModels()


class IngredientSourceInput(BaseModel):
    ingredient_name: str
    country: str
    hscode_level: int | None = 6


class KeyExporters(BaseModel):
    country_code: str
    country_name: str
    trade_value: float | None


class PredictedIngredientSource(BaseModel):
    ingredient_name: str
    hscodes: str | None = None
    synonyms: str | None = None
    hscode: int
    product_category: str
    manufacturing_country: str
    import_value: int | None = 0
    export_value: int | None = 0
    domestic_production_value: str | None = 0
    domestic_production_rate: str | None = 0
    likely_source_of_procurement: str | None = ""
    key_exporters: List[KeyExporters] | None = []


def predict_hscode(ingredient_name: str | None = None) -> str | None:
    try:
        hscode = safe_async(
            ml_models.predict_chemical_hscode,
            ingredient_name,
        )
        return hscode
    except MLModelsRequestException as error:
        logger.warning(f"Error predicting HSCode: skipping, error: {error}")
        return None


def predict_likely_source_of_procurement(
    ingredient_source_input: IngredientSourceInput
) -> PredictedIngredientSource:
    country_info = get_country_info(ingredient_source_input.country)
    if not country_info:
        logger.error(f"Country {ingredient_source_input.country} not found")
        return None

    wits_country_info = fetch_country_by_name(country_info.alpha_3_code)
    if not wits_country_info:
        logger.error(
            f"Country {ingredient_source_input.country} not found in country table"
        )
        return None

    ingredient_name = sanitize_ingredient_name(ingredient_source_input.ingredient_name)
    hscodes = predict_hscode(ingredient_name)

    if not hscodes:
        logger.error(f"HS Code not available for ingredient {ingredient_name}")
        return None

    logger.info(f"HS Codes for ingredient {ingredient_name} : {hscodes}")

    for hscode in hscodes.split(","):
        # TODO: Cleanup noise in ML Models
        if "." in hscode or len(hscode) > 8:
            continue

        hscode = str(hscode)[: ingredient_source_input.hscode_level]

        import_data = fetch_trade_data(hscode, TradeFlow.IMPORT, wits_country_info.id)
        export_data = fetch_trade_data(hscode, TradeFlow.EXPORT, wits_country_info.id)

        if not import_data and not export_data:
            continue

        product_category = fetch_hscode_product_category(hscode)

        domestic_production_data = fetch_domestic_production_data(
            hscode,
            ingredient_source_input.country,
        )

        predicted_ingredient_source = PredictedIngredientSource(
            ingredient_name=ingredient_name,
            hscodes=hscodes,
            synonyms=None,
            hscode=hscode,
            product_category=product_category,
            manufacturing_country=ingredient_source_input.country,
        )

        if import_data:
            predicted_ingredient_source.import_value = float(
                import_data[0]["trade_value"]
            )
        if export_data:
            predicted_ingredient_source.export_value = float(
                export_data[0]["trade_value"]
            )

        if domestic_production_data:
            predicted_ingredient_source.domestic_production_value = (
                domestic_production_data[0]["production_value_usd"]
            )

            if (
                predicted_ingredient_source.domestic_production_value
                > predicted_ingredient_source.import_value
            ):
                predicted_ingredient_source.key_exporters = format_exporter_data(
                    export_data
                )
                predicted_ingredient_source.likely_source_of_procurement = (
                    ingredient_source_input.country
                )
                return predicted_ingredient_source

        if (
            predicted_ingredient_source.export_value
            > predicted_ingredient_source.import_value
        ):
            predicted_ingredient_source.domestic_production_value = float(
                predicted_ingredient_source.export_value
                - predicted_ingredient_source.import_value
            )
            predicted_ingredient_source.key_exporters = format_exporter_data(
                export_data
            )
            predicted_ingredient_source.likely_source_of_procurement = (
                ingredient_source_input.country
            )
        else:
            predicted_ingredient_source.key_exporters = format_exporter_data(
                import_data
            )
            predicted_ingredient_source.likely_source_of_procurement = (
                predicted_ingredient_source.key_exporters[1]["country_code"]
            )

        predicted_ingredient_source.key_exporters.pop(
            0
        )  # remove World[WLD] from exporters

        return predicted_ingredient_source
