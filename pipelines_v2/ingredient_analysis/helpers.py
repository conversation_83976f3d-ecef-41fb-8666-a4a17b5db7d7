import unicodedata


def sanitize_ingredient_name(ingredient_name):
    return (
        unicodedata.normalize("NFKD", ingredient_name.strip().title())
        .encode("ASCII", "ignore")
        .decode("utf-8")
    )

def format_exporter_data(trade_data):
    """Fmt trade data"""
    key_exporters = []
    if not trade_data:
        return key_exporters

    for exporter in trade_data:
        key_exporters.append(
            {
                "country_code": exporter["country_code"],
                "country_name": exporter["country_name"],
                "trade_value": exporter["trade_value"],
            }
        )
    return key_exporters
