from enum import Enum
from dataclasses import dataclass


class TransportMode(Enum):
    HGV = "hgv"
    CONTAINER_SHIP = "container ship"

@dataclass(frozen=True)
class EmissionsFactor:
    activity_name: str
    reference_product_name: str
    geography: str
    source: str

@dataclass(frozen=True)
class DefaultTransportEmissionsFactors:
    """
    Default emissions factors for transport.
    """
    hgv: EmissionsFactor = EmissionsFactor(
        activity_name="transport, freight, lorry, >32 metric ton, diesel, EURO 6",
        reference_product_name="transport, freight, lorry, >32 metric ton, diesel, EURO 6",
        geography="RoW",
        source="Ecoinvent 3.11",
    )
    container_ship: EmissionsFactor = EmissionsFactor(
        activity_name="transport, freight, sea, container ship, heavy fuel oil",
        reference_product_name="transport, freight, sea, container ship, heavy fuel oil",
        geography="GLO",
        source="Ecoinvent 3.11",
    )
