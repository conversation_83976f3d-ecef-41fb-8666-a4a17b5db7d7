"""Module to calculate the distance between two points on the earth's surface"""
import math
from enum import Enum
from typing import Optional
from pydantic import BaseModel
from utils.geocoding import Coordinates


RADIUS_OF_EARTH_KM = 6371


class DistanceCorrectionFactor(Enum):
    """Enum defining the correction factors for the haversine formula"""
    NONE = 1
    SEA = 1.45
    LAND = 1.35

class Distances(BaseModel):
    """A Model to represent both land and sea distances between two points"""
    land_distance : Optional[float] = None
    sea_distance : Optional[float] = None


def haversine(
    origin: Coordinates,
    destination: Coordinates,
    correction_factor=DistanceCorrectionFactor.NONE,
):
    """
    Calculate the great circle distance in kilometers between two
    points on the earth (specified in decimal degrees)
    """
    # convert decimal degrees to radians
    lon1, lat1, lon2, lat2 = map(
        math.radians,
        [
            origin.longitude,
            origin.latitude,
            destination.longitude,
            destination.latitude,
        ],
    )

    # haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1

    arc = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
    circumfrence = 2 * math.asin(math.sqrt(arc))

    return circumfrence * RADIUS_OF_EARTH_KM * correction_factor.value

def calculate_freight_distance(area_sq_km) -> float:
    """
    Calculate the estimated average freight transport distance for a country

    >>> route = Route(None, None)
    >>> route.calculate_freight_distance(5833517)  # Close to United States area
    308.09
    >>> route.calculate_freight_distance(142900)  # Close to United Kingdom area
    81.92
    """
    reference_avg_distance = [
        {"name": "United States", "area": 9833517, "distance": 400},
        {"name": "United Kingdom", "area": 242900, "distance": 106.8},
    ]

    closest_reference = min(
        reference_avg_distance, key=lambda x: abs(x["area"] - area_sq_km)
    )

    reference_country_area = closest_reference["area"]
    reference_distance = closest_reference["distance"]

    scaling_factor = reference_distance / math.sqrt(reference_country_area)

    estimated_distance = math.sqrt(area_sq_km) * scaling_factor

    return round(estimated_distance, 2)
