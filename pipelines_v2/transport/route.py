import json
import time
from typing import List
from pydantic import BaseModel
from databases.engine import engine_pool
from databases.shared.repository import fetch_closest_sea_port
from databases.tenant.models import Address
from utils import cache
from utils.geocoding import (
  fetch_coordinates,
  get_country_info,
  Coordinates,
  generate_land_route,
  RouteNotConstructableError,
  RateLimitExceededError,
)
from utils.logger import logger
from pipelines_v2.transport.distance import (
  haversine,
  calculate_freight_distance,
  DistanceCorrectionFactor,
)
from pipelines_v2.transport.constants import TransportMode


class RouteSegment(BaseModel):
    distance: float
    unit: str
    mode: str
    origin: Address
    destination: Address

class TotalRouteDistance(BaseModel):
    total_distance: float
    unit: str
    segments: List[RouteSegment]
    origin: Address
    destination: Address


class Route:
    def __init__(
        self,
        origin: Address,
        destination: Address,
        origin_coordinates: Coordinates | None = None,
        destination_coordinates: Coordinates | None = None,
        vehicle_profile: str = TransportMode.HGV.value,
    ) -> None:
        self.origin = origin
        self.destination = destination
        self.origin_coordinates = origin_coordinates
        self.destination_coordinates = destination_coordinates
        self.vehicle_profile = vehicle_profile
        self.engine = engine_pool.get_root_engine()

        if not self._is_domestic():
            if self.origin.city is None:
                country_info = get_country_info(self.origin.country)
                self.origin.city = country_info.capital

            if self.destination.city is None:
                country_info = get_country_info(self.destination.country)
                self.destination.city = country_info.capital

        if not self.origin_coordinates:
            self.origin_coordinates = fetch_coordinates(
                f"{self.origin.city}, {self.origin.country}"
                if self.origin.city
                else self.origin.country,
            )

        if not self.destination_coordinates:
            self.destination_coordinates = fetch_coordinates(
                f"{self.destination.city}, {self.destination.country}"
                if self.destination.city
                else self.destination.country,
            )

    def calculate_total_distance(self) -> TotalRouteDistance:
        if self._is_domestic() and (self.origin.city is None or self.destination.city is None):
            country_info = get_country_info(self.origin.country)
            avg_transport_distance = calculate_freight_distance(
                area_sq_km=country_info.area_sq_km
            )

            return TotalRouteDistance(
                total_distance=avg_transport_distance,
                unit="kilometers",
                segments=[
                    RouteSegment(
                        distance=avg_transport_distance,
                        unit="kilometers",
                        mode=self.vehicle_profile,
                        origin=self.origin,
                        destination=self.destination,
                    ),
                ],
                origin=self.origin,
                destination=self.destination,
            )

        if self._is_identical():
            return TotalRouteDistance(
                total_distance=0,
                unit="kilometers",
                segments=[],
                origin=self.origin,
                destination=self.destination,
            )

        generated_route = self._route_segments()
        total_distance = sum(segment.distance for segment in generated_route)

        return TotalRouteDistance(
            total_distance=total_distance,
            unit="kilometers",
            segments=generated_route,
            origin=self.origin,
            destination=self.destination,
        )

    def _route_segments(self) -> List[RouteSegment]:
        if not self._is_sea_route():
            return self._land_route_segments()
        return self._sea_route_segments()

    def _land_route_segments(self) -> List[RouteSegment]:
        try:
            land_distance = self._generate_land_route(
                self.origin_coordinates,
                self.destination_coordinates,
            )

            return [
                RouteSegment(
                    distance=land_distance,
                    unit="kilometers",
                    mode=TransportMode.HGV.value,
                    origin=self.origin,
                    destination=self.destination,
                ),
            ]

        except RouteNotConstructableError:
            logger.warning("UNABLE TO CREATE LAND ROUTE")
            # Falls back to a sea route
            return self._sea_route_segments()

    def _generate_land_route(self, origin: Coordinates, destination: Coordinates):
        try:
            origin_json = json.dumps(origin.dict())
            destination_json = json.dumps(destination.dict())
            cached_route = cache.read_cache(
                f"LAND_ROUTE_DISTANCE_{origin_json}{destination_json}",
            )
            if cached_route:
                return cached_route

            land_route = generate_land_route(
                origin,
                destination,
            )

            route_distance = land_route["routes"][0]["distance"] / 1000
            cache.write_cache(
                f"LAND_ROUTE_DISTANCE_{origin_json}{destination_json}",
                route_distance,
            )

            return route_distance
        except RateLimitExceededError:
            logger.warning(
                """
                Rate limit exceeded for Mapbox,
                falling back onto point to point calculation.
                Sleep for 60 seconds and try again.
                """,
            )
            time.sleep(60)
            return self._generate_land_route(origin, destination)
        except RouteNotConstructableError:
            logger.warning(
                """
                Unable to generate land route,
                falling back onto point to point calculation.
                """,
            )

            return haversine(
                origin,
                destination,
                correction_factor=DistanceCorrectionFactor.LAND,
            )

    def _sea_route_segments(self) -> List[RouteSegment]:
        origin_port = fetch_closest_sea_port(
            self.origin_coordinates.latitude,
            self.origin_coordinates.longitude,
        )

        destination_port = fetch_closest_sea_port(
            self.destination_coordinates.latitude,
            self.destination_coordinates.longitude,
        )

        origin_port_coordinates = Coordinates(
            latitude=origin_port.latitude,
            longitude=origin_port.longitude,
        )

        destination_port_coordinates = Coordinates(
            latitude=destination_port.latitude,
            longitude=destination_port.longitude,
        )

        origin_port_distance = self._generate_land_route(
            self.origin_coordinates,
            origin_port_coordinates,
        )

        destination_port_distance = self._generate_land_route(
            self.destination_coordinates,
            destination_port_coordinates,
        )

        sea_distance = haversine(
            origin_port_coordinates,
            destination_port_coordinates,
            correction_factor=DistanceCorrectionFactor.SEA,
        )

        segments = [
            RouteSegment(
                distance=origin_port_distance,
                unit="kilometers",
                mode=TransportMode.HGV.value,
                origin=self.origin,
                destination=Address(
                    city=origin_port.city,
                    country=origin_port.country,
                ),
            ),
            RouteSegment(
                distance=sea_distance,
                unit="kilometers",
                mode=TransportMode.CONTAINER_SHIP.value,
                origin=Address(
                    city=origin_port.city,
                    country=origin_port.country,
                ),
                destination=Address(
                    city=destination_port.city,
                    country=destination_port.country,
                ),
            ),
            RouteSegment(
                distance=destination_port_distance,
                unit="kilometers",
                mode=TransportMode.HGV.value,
                origin=Address(
                    city=destination_port.city,
                    country=destination_port.country,
                ),
                destination=self.destination,
            ),
        ]

        return segments

    def _is_sea_route(self) -> bool:
        domestic = self._is_domestic()
        if domestic and domestic is not None:
            return False

        distance = haversine(
            self.origin_coordinates,
            self.destination_coordinates,
        )

        if distance > 2000:
            return True
        return False

    def _is_identical(self):
        """
        Determine if the origin and destination locations are identical.

        This method checks if both the city and country of the origin 
        and destination locations are the same. Note that coordinates 
        are not considered in this check, as they can be generated for 
        just the country level as well.
        """
        return (
            (
                self.origin.country == self.destination.country
                and self.origin.city == self.destination.city
                and self.origin.city is not None
                and self.destination.city is not None
            )
        )

    def _is_domestic(self):
        return self.origin.country == self.destination.country
