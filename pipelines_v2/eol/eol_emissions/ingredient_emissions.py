import logging
from pydantic import BaseModel
from databases.shared.repository import WasteDisposalMethodEmissionRepo
from utils.geocoding import get_country_info
from .weight import Weight, WeightUnits


logging.basicConfig(level=logging.INFO)


class EmissionFactor(BaseModel):
    country: str
    product_category: str
    waste_disposal_method: str
    co2e_ef: float
    rate: float


class TotalEOLEmission(BaseModel):
    """
    EOL emissions for ingredients by product category
    """

    total_disposed_weight_grams: float
    unit: str = "kgCo2e"
    country: str
    product_category: str
    total_emission: float
    recycling: float = None
    incineration: float = None
    landfilling: float = None
    composting: float = None
    anaerobic_digestion: float = None
    source: str = "DEFRA"
    number_of_reuses: float


class ProductIngredientEolInput(BaseModel):
    product_category: str
    country: str
    total_weight: Weight
    number_of_reuses: float = 0


def calculate_emission(
    emission_factor_kg_co2_e: float, method_percent_rate: float, total_weight: Weight
):
    return (
        emission_factor_kg_co2_e
        * (method_percent_rate / 100)
        * total_weight.get_weight(WeightUnits.KILOGRAM)
    )


def fetch_eol_emissions(
    product_ingredient: ProductIngredientEolInput,
) -> TotalEOLEmission:
    """
    Takes ProductIngredientEolInput as input and returns the emissions for each disposal method
    """
    eol_emissions = fetch_product_category_emissions(product_ingredient)
    return eol_emissions

def calculate_eol_emissions(weight_kg: float, country: str) -> float:
    waste_disposal_method_emission_repo = WasteDisposalMethodEmissionRepo()
    country_info = get_country_info(country)
    country_iso_code = country_info.alpha_3_code

    landfill_emissions_ef = waste_disposal_method_emission_repo.get_landfill_by_country(
        country_iso_code
    )

    if landfill_emissions_ef:
        landfill_emissions = landfill_emissions_ef.co2e_ef * weight_kg
        return landfill_emissions

    return 0


def fetch_product_category_emissions(
    product_ingredient: ProductIngredientEolInput,
) -> TotalEOLEmission:
    """
    Takes a ProductIngredientEolInput and returns the emissions for the product category
    """
    waste_disposal_method_emission_repo = WasteDisposalMethodEmissionRepo()
    product_category_emissions = waste_disposal_method_emission_repo.get_by_product_category(
        product_ingredient.product_category, product_ingredient.country
    )

    disposal_emissions = {}
    factor_of_non_reuse = 1 / (product_ingredient.number_of_reuses + 1)

    if product_category_emissions:
        for emission in product_category_emissions:
            disposal_emissions[emission.waste_disposal_method.lower()] = (
                calculate_emission(
                    emission.co2e_ef,
                    emission.rate,
                    product_ingredient.total_weight
                )
                * factor_of_non_reuse
            )

    total_emissions = sum(
        value
        for value in disposal_emissions.values()
        if isinstance(value, float)
    )

    return TotalEOLEmission(
        total_disposed_weight_grams=product_ingredient.total_weight.get_weight(
            WeightUnits.GRAM
        ),
        country=product_ingredient.country,
        product_category=product_ingredient.product_category,
        total_emission=total_emissions,
        number_of_reuses=product_ingredient.number_of_reuses,
        **disposal_emissions,
    )
