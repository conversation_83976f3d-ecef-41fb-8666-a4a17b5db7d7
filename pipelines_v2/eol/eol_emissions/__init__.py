"""packaging Material EOL EF"""
from .packaging_emissions import (
    fetch_packaging_material_emissions,
    fetch_eol_emissions as fetch_packaging_material_eol_emissions,
    PackagingMaterial,
    TotalEOLEmission,
)
from .ingredient_emissions import (
    fetch_eol_emissions as fetch_product_ingredient_eol_emissions,
    TotalEOLEmission as ProductIngredientTotalEOLEmission,
    ProductIngredientEolInput,
)
from .material import eol_material_mapping

__all__ = [
    "fetch_packaging_material_emissions",
    "fetch_packaging_material_eol_emissions",
    "fetch_product_ingredient_eol_emissions",
    "eol_material_mapping"
]
