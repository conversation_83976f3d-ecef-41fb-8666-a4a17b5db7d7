from enum import Enum

from pydantic import BaseModel


class WeightUnits(Enum):
    GRAM = 1
    KILOGRAM = 1000
    TONNE = 1000000


class Weight(BaseModel):
    """
    A class to represent and manipulate weights in different units.
    The default unit is grams
    >>> w = Weight(1234)
    >>> w.amount
    1234.0
    >>> w.get_weight(WeightUnits.KILOGRAM)
    1.234
    >>> w = Weight(1, WeightUnits.KIL<PERSON>GRAM)
    >>> w.amount
    1000.0
    >>> (2 * w).get_weight(WeightUnits.KILOGRAM)
    2.0
    >>> w.get_weight(WeightUnits.TONNE)
    0.001
    >>> w.get_weight(WeightUnits.GRAM)
    1000.0
    """
    amount: float
    unit: WeightUnits = WeightUnits.GRAM

    # Pydantic doesn't support positional arguments,
    # Creating this __init__ method to support positional arguments for ease of use
    def __init__(self, amount: float, unit: WeightUnits = WeightUnits.GRAM) -> None:
        super().__init__(amount=amount, unit=unit)
        #Convert amount to grams
        self.amount = float(self.amount) * self.unit.value

    def __mul__(self, other: float) -> "Weight":
        if not isinstance(other, (int, float)):
            raise ValueError("Scaling factor can only be an int or float")
        return Weight(amount=self.amount * other)

    def __rmul__(self, other: float) -> "Weight":
        return self.__mul__(other)

    def __truediv__(self, other: float) -> "Weight":
        if not isinstance(other, (int, float)):
            raise ValueError("Scaling factor can only be an int or float")
        return Weight(amount=self.amount / other)

    def get_weight(self, unit: WeightUnits) -> float:
        return self.amount / unit.value


if __name__ == "__main__":
    import doctest
    doctest.testmod()
