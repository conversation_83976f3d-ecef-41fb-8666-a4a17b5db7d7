import logging
from typing import List
from enum import Enum
from pydantic import BaseModel
from databases.shared.repository import WasteDisposalMethodEmissionRepo
from utils.geocoding import get_country_info
from utils.async_call import safe_async
from clients.ml_models import MLModels
from .material import eol_material_mapping
from .weight import Weight, WeightUnits


logging.basicConfig(level=logging.INFO)

ml_models = MLModels()


class PackagingLevel(Enum):
    PRIMARY = "Primary"
    SECONDARY = "Secondary"
    TERTIARY = "Tertiary"

class PackagingSupplier(BaseModel):
    id: int | None
    name: str
    origin_country: str | None
    origin_city: str | None
    supplier_level: int

class PackageComponent(BaseModel):
    id: int | None = None
    component: str
    material: str
    weight: Weight | None = None
    reusable_packaging: bool = False
    number_of_reuses: float = 0
    number_of_packages: int = 1
    recycled_material: bool = False
    recycled_content_rate: float = 0
    packaging_level: str = PackagingLevel.PRIMARY.value
    supplier: PackagingSupplier | None = None


class EmissionFactor(BaseModel):
    country: str
    packaging_material: str
    waste_disposal_method: str
    co2e_ef: float
    rate: float


class TotalEOLEmission(BaseModel):
    """
    EOL emissions for a packaging material
    """

    unit: str = "kgCo2e"
    country: str
    packaging_material: str
    weight: Weight
    number_of_packages: int
    total_emission: float
    recycling: float = None
    incineration: float = None
    landfilling: float = None
    composting: float = None
    source: str = "DEFRA"


def calculate_emission(
    emission_factor_kg_co2_e: float, method_percent_rate: float, total_weight: float
):
    return (
        emission_factor_kg_co2_e
        * method_percent_rate
        * total_weight
    )


class PackagingMaterial(BaseModel):
    material: str
    total_weight: Weight
    number_of_packages: int = 1
    pack_size: float = None
    num_uses_reusable: int = None
    country: str


def fetch_eol_emissions(
    country_of_use_iso3: str,
    packaging_materials: List[PackageComponent],
) -> List[TotalEOLEmission]:
    """
    Takes a list of PackagingMaterial objects and returns the emissions for each
    """
    eol_emissions = [
        fetch_packaging_material_emissions(
            country_of_use_iso3,
            packaging_material
        )
        for packaging_material in packaging_materials
    ]
    return eol_emissions

def calculate_disposal_emissions(
    country: str,
    packaging_material: str,
    weight: float,
) -> TotalEOLEmission:
    """To Be Deprecated"""
    eol_category = safe_async(
        ml_models.predict_eol_category,
        packaging_material,
    )
    packaging_component = PackageComponent(
        material=eol_category or packaging_material,
        component="",
        weight=Weight(weight, WeightUnits.KILOGRAM),
        number_of_packages=1,
    )

    country_info = get_country_info(country)
    country_of_use_iso3 = country_info.alpha_3_code

    return fetch_packaging_material_emissions(
        country_of_use_iso3,
        packaging_component,
    )

def fetch_packaging_material_emissions(
    country_of_use_iso3: str,
    packaging_component: PackageComponent,
) -> TotalEOLEmission:
    """
    Calculate EOL emissions for a packaging component.
    Also accounting for reuse if applicable.
    """
    waste_disposal_method_emission_repo = WasteDisposalMethodEmissionRepo()
    packaging_emissions = waste_disposal_method_emission_repo.get_by_packaging_material(
        eol_material_mapping.get(packaging_component.material, packaging_component.material),
        country_of_use_iso3,
    )

    disposal_emissions = {}

    factor_of_non_reuse = 1
    weight = (
        packaging_component.weight.get_weight(WeightUnits.KILOGRAM)
        / packaging_component.number_of_packages
    )

    if packaging_component.reusable_packaging and packaging_component.number_of_reuses:
        factor_of_non_reuse = 1 / (packaging_component.number_of_reuses + 1)

    if packaging_emissions:
        for emission in packaging_emissions:
            disposal_emissions[emission.waste_disposal_method.lower()] = (
                calculate_emission(
                    emission.co2e_ef,
                    emission.rate,
                    weight
                )
                * factor_of_non_reuse
            )

    total_emission = sum(
        value
        for value in disposal_emissions.values()
        if isinstance(value, float)
    )

    return TotalEOLEmission(
        country=country_of_use_iso3,
        packaging_material=packaging_component.material,
        weight=packaging_component.weight,
        number_of_packages=packaging_component.number_of_packages,
        total_emission=total_emission,
        **disposal_emissions,
    )
