from requests.structures import CaseInsensitiveDict


eol_material_mapping = CaseInsensitiveDict(
    data={
        "PP": "Plastic",
        "PE": "Plastic",
        "PVC": "Plastic",
        "PS": "Plastic",
        "PET": "Plastic",
        "HDPE": "Plastic",
        "LLDPE": "Plastic",
        "Paper Self Adhesive Label": "Paper",
        "Corrugate": "Cardboard",
        "Corrugated Fiberboard": "Cardboard",
        "Aluminium": "Aluminium",
        "Steel": "Steel",
        "Tin": "Aluminium",
        "Box": "Cardboard",
        "PMMA": "Plastic",
        "ABS": "Plastic",
        "Paper and board: paper": "Paper",
        "Paper and Board: Mixed": "Cardboard",
        "Material.CARDBOARD.value": "Cardboard",
        "Silica gel": "Silica Gel",
        "Glass": "Glass",
    }
)
