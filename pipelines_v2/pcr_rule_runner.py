from typing import Any, List, TypedDict, Union
from databases.shared.models.pcr_rule import PCRRule
from databases.tenant.models import Node
from pipelines_v2.graph import Graph


class AST(TypedDict):
    type: str
    left: Union['AST', str]
    right: Union['AST', str]

class Token:
    def __init__(self, token_type: str, value: str):
        self.token_type = token_type
        self.value = value

    def __str__(self):
        return f"Token(type={self.token_type}, value={self.value})"

GROUPING_TOKENS = {
    '(': 'LPAREN',
    ')': 'RPAREN',
}

SYMBOL_TOKENS = {
    'node': 'NODE',
    'assembly': 'ASSEMBLY',
}

OPERATOR_TOKENS = {
    'and': 'AND',
    'or': 'OR',
    'is': 'IS',
    'before': 'BEFORE',
    'after': 'AFTER',
}

TOKENS = {
  **GROUPING_TOKENS,
  **OPERATOR_TOKENS,
  **SYMBOL_TOKENS,
}

class RuleConstructor:
    def __init__(self, rule_str: str):
        self.rule_str = rule_str

    def _get_tokens(self):
        tokens = []
        rule_str = self.rule_str.replace("(", " ( ").replace(")", " ) ")
        for token in rule_str.split():
            if token in TOKENS:
                tokens.append(Token(TOKENS[token], token))
            else:
                if token.isalpha():
                    tokens.append(Token('IDENTIFIER', token))
                else:
                    raise ValueError(f"Invalid token: {token}")
        return tokens

    def _get_postfix_tokens(self, tokens: List[Token]) -> List[Token]:
        """Parse tokens into postfix notation."""
        stack = []
        postfix_tokens = []
        for token in tokens:
            if (
                token.token_type == 'IDENTIFIER' or
                token.token_type in SYMBOL_TOKENS.values()
            ):
                postfix_tokens.append(token)
            elif token.token_type == 'LPAREN':
                stack.append(token)
            elif token.token_type == 'RPAREN':
                while stack and stack[-1].token_type != 'LPAREN':
                    postfix_tokens.append(stack.pop())
                if stack:
                    stack.pop()
            else:
                while (stack and stack[-1].token_type != 'LPAREN' and
                      stack[-1].token_type in OPERATOR_TOKENS.values()):
                    postfix_tokens.append(stack.pop())
                stack.append(token)
        while stack:
            postfix_tokens.append(stack.pop())
        return postfix_tokens

    def parse(self):
        tokens = self._get_tokens()
        return self._get_postfix_tokens(tokens)

    def evaluate(self) -> AST:
        postfix_tokens = self.parse()
        stack = []
        for token in postfix_tokens:
            if (
                token.token_type == 'IDENTIFIER' or
                token.token_type in SYMBOL_TOKENS.values()
            ):
                stack.append(token.value)
            elif token.token_type in OPERATOR_TOKENS.values():
                if len(stack) < 2:
                    raise ValueError(f"Invalid expression: {token.value} requires two operands")
                right = stack.pop()
                left = stack.pop()
                stack.append(
                    AST(
                        type=token.token_type,
                        left=left,
                        right=right
                    )
                )
        return stack[0]

class RuleRunner:
    def __init__(self, rules: list[PCRRule], graph: Graph):
        self.rules = rules
        self.graph = graph

    def _execute(self, rule: dict[str, Any], node: Node) -> bool:
        rule_type = rule["type"]
        left = rule["left"]
        right = rule["right"]

        execution_map = {
            "AND": self._and,
            "OR": self._or,
            "IS": self._is,
            "BEFORE": self._before,
            "AFTER": self._after,
        }

        if not rule_type in execution_map:
            raise ValueError(f"Unknown operator: {rule_type}")

        if rule_type in ["BEFORE", "AFTER"]:
            if left != "node":
                raise ValueError(f"Invalid left operand for {rule_type} operator: {left}")
            return execution_map[rule_type](node, right)
        return execution_map[rule_type](node, left, right)


    def _and(self, node: Node, left: dict[str, Any], right: dict[str, Any]) -> bool:
        return (self._execute(left, node)) and (self._execute(right, node))

    def _or(self, node: Node, left: dict[str, Any], right: dict[str, Any]) -> bool:
        return (self._execute(left, node)) or (self._execute(right, node))

    def _is(self, node: Node, left: dict[str, Any], right: str) -> bool:
        if left != "node":
            raise ValueError(f"Invalid left operand for IS operator: {left}")

        if right == "ASSEMBLY":
            assembly_node = self._get_assembly_node()
            return node.id == assembly_node.id

        return node.node_type == right.lower()

    def _before(self, node: Node, right: str) -> bool:
        if right == "ASSEMBLY":
            assembly_node = self._get_assembly_node()
            return self.graph.precedes_node(node, assembly_node)

        return self.graph.precedes_type(node, right)

    def _after(self, node: Node, right: str) -> bool:
        if right == "ASSEMBLY":
            assembly_node = self._get_assembly_node()
            return self.graph.succeeds_node(node, assembly_node)

        return self.graph.succeeds_type(node, right)

    def _get_assembly_node(self) -> Node | None:
        """Find the assembly node using breadth-first traversal.

        Returns:
            The last node in breadth-first order that is either:
            - a production node, or
            - a bundle node with 'Assembly' in its name

        This ensures we get the assembly node closest to the end of the process,
        which is typically what we want in a process model.

        Note:
            Uses a cloned graph for traversal to avoid interfering with any
            ongoing traversal of the original graph.
        """
        # Clone the graph to avoid interfering with any ongoing traversal
        assembly_node = None

        for node in self.graph.get_nodes_topologically_sorted():
            if (
                node.node_type == "production"
            ) or (
                node.node_type == "bundle" and "Assembly" in node.name
            ):
                # Get the corresponding node from the original graph
                assembly_node = self.graph.get_node(node.id)
        return assembly_node

    def get_rule_match(self, node: Node) -> PCRRule | None:
        for rule in self.rules:
            rule_constructor = RuleConstructor(rule.condition)
            tree = rule_constructor.evaluate()
            if self._execute(tree, node):
                return rule
        return None
