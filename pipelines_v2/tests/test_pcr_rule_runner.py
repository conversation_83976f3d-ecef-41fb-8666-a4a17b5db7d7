import unittest
from databases.shared.models.pcr_rule import PCRRule
from databases.tenant.models import Node, Edge
from pipelines_v2.graph import Graph
from pipelines_v2.pcr_rule_runner import RuleRunner

class TestPCRRuleRunner(unittest.TestCase):
    def test_get_rule_match(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="material", product_id=""),
            Node(id=2, name="Node 2", node_type="material", product_id=""),
            Node(id=3, name="Node 3", node_type="transportation", product_id=""),
            Node(id=4, name="Node 3", node_type="transportation", product_id=""),
            Node(id=5, name="Node 4", node_type="production", product_id=""),
            Node(id=6, name="Node 5", node_type="use", product_id=""),
            Node(id=7, name="Node 6", node_type="eol", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=3),
            Edge(from_node_id=2, to_node_id=4),
            <PERSON>(from_node_id=3, to_node_id=5),
            <PERSON>(from_node_id=4, to_node_id=5),
            Edge(from_node_id=5, to_node_id=6),
            Edge(from_node_id=6, to_node_id=7),
        ]
        pcr_rules = [
            PCRRule(
                id=1,
                condition="node is MATERIAL",
                segment="Material Extraction",
                sequence_number=1,
            ),
            PCRRule(
                id=2,
                condition="(node is TRANSPORTATION) and (node before ASSEMBLY)",
                segment="Material Transport",
                sequence_number=2,
            ),
            PCRRule(id=3, condition="node is ASSEMBLY", segment="Assembly", sequence_number=3),
            PCRRule(
                id=4,
                condition="(node is TRANSPORTATION) and (node after ASSEMBLY)",
                segment="Transport to Consumer",
                sequence_number=4,
            ),
            PCRRule(id=5, condition="node is USE", segment="Use", sequence_number=5),
            PCRRule(id=6, condition="node is EOL", segment="Waste Disposal", sequence_number=6),
        ]

        graph = Graph(nodes, edges)
        rule_runner = RuleRunner(pcr_rules, graph)

        self.assertEqual(rule_runner.get_rule_match(nodes[0]).segment, "Material Extraction")
        self.assertEqual(rule_runner.get_rule_match(nodes[1]).segment, "Material Extraction")
        self.assertEqual(rule_runner.get_rule_match(nodes[2]).segment, "Material Transport")
        self.assertEqual(rule_runner.get_rule_match(nodes[3]).segment, "Material Transport")
        self.assertEqual(rule_runner.get_rule_match(nodes[4]).segment, "Assembly")
        self.assertEqual(rule_runner.get_rule_match(nodes[5]).segment, "Use")
        self.assertEqual(rule_runner.get_rule_match(nodes[6]).segment, "Waste Disposal")

    def test_get_rule_match_with_complex_process_model(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="material", product_id=""),
            Node(id=2, name="Node 2", node_type="material", product_id=""),
            Node(id=3, name="Node 3", node_type="production", product_id=""),
            Node(id=4, name="Node 4", node_type="production", product_id=""),
            Node(id=5, name="Node 5", node_type="production", product_id=""),
            Node(id=6, name="Node 6", node_type="transportation", product_id=""),
            Node(id=7, name="Node 7", node_type="transportation", product_id=""),
            Node(id=8, name="Node 8", node_type="production", product_id=""),
            Node(id=9, name="Node 9", node_type="use", product_id=""),
            Node(id=10, name="Node 10", node_type="eol", product_id=""),
        ]

        edges = [
            Edge(from_node_id=1, to_node_id=3),
            Edge(from_node_id=2, to_node_id=4),
            Edge(from_node_id=4, to_node_id=5),
            Edge(from_node_id=5, to_node_id=6),
            Edge(from_node_id=6, to_node_id=7),
            Edge(from_node_id=7, to_node_id=8),
            Edge(from_node_id=8, to_node_id=9),
            Edge(from_node_id=9, to_node_id=10),
        ]

        pcr_rules = [
            PCRRule(
                id=1,
                condition="node is MATERIAL",
                segment="Material Extraction",
                sequence_number=1,
            ),
            PCRRule(
                id=2,
                condition="(node is TRANSPORTATION) and (node before ASSEMBLY)",
                segment="Material Transport",
                sequence_number=2,
            ),
            PCRRule(
                id=3,
                condition="(node is PRODUCTION) and (node before ASSEMBLY)",
                segment="Material Processing",
                sequence_number=3,
            ),
            PCRRule(id=4, condition="node is ASSEMBLY", segment="Assembly", sequence_number=4),
            PCRRule(
                id=5,
                condition="(node is TRANSPORTATION) and (node after ASSEMBLY)",
                segment="Transport to Consumer",
                sequence_number=5,
            ),
            PCRRule(id=6, condition="node is USE", segment="Use", sequence_number=6),
            PCRRule(id=7, condition="node is EOL", segment="Waste Disposal", sequence_number=7),
        ]

        graph = Graph(nodes, edges)
        rule_runner = RuleRunner(pcr_rules, graph)

        self.assertEqual(rule_runner.get_rule_match(nodes[0]).segment, "Material Extraction")
        self.assertEqual(rule_runner.get_rule_match(nodes[1]).segment, "Material Extraction")
        self.assertEqual(rule_runner.get_rule_match(nodes[2]).segment, "Material Processing")
        self.assertEqual(rule_runner.get_rule_match(nodes[3]).segment, "Material Processing")
        self.assertEqual(rule_runner.get_rule_match(nodes[4]).segment, "Material Processing")
        self.assertEqual(rule_runner.get_rule_match(nodes[5]).segment, "Material Transport")
        self.assertEqual(rule_runner.get_rule_match(nodes[6]).segment, "Material Transport")
        self.assertEqual(rule_runner.get_rule_match(nodes[7]).segment, "Assembly")
        self.assertEqual(rule_runner.get_rule_match(nodes[8]).segment, "Use")
        self.assertEqual(rule_runner.get_rule_match(nodes[9]).segment, "Waste Disposal")
