import unittest
from databases.tenant.models import Node, Edge
from pipelines_v2.graph import Graph, CyclicDependencyError

class TestGraph(unittest.TestCase):
    nodes = [
        Node(id=1, name="Node 1", node_type="", product_id=""),
        Node(id=2, name="Node 2", node_type="", product_id=""),
        Node(id=3, name="Node 3", node_type="", product_id=""),
        Node(id=4, name="Node 4", node_type="", product_id=""),
    ]

    edges = [
        Edge(from_node_id=1, to_node_id=2),
        Edge(from_node_id=2, to_node_id=3),
        Edge(from_node_id=3, to_node_id=4),
        Edge(from_node_id=1, to_node_id=4),
    ]

    def test_empty_graph(self):
        graph = Graph([], [])
        self.assertEqual(graph.get_nodes_topologically_sorted(), [])
        self.assertEqual(graph.get_source_nodes(), [])

    def test_single_node_graph(self):
        node = Node(id=1, name="Node 1")
        graph = Graph([node], [])
        self.assertEqual(graph.get_nodes_topologically_sorted(), [node])
        self.assertEqual(graph.get_source_nodes(), [node])

    def test_disconnected_graph(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id="", quantity=1),
            Node(id=2, name="Node 2", node_type="", product_id="", quantity=1),
        ]
        graph = Graph(nodes, [])
        nodes_list = graph.get_nodes_topologically_sorted()
        self.assertEqual(len(nodes_list), 2)
        self.assertIn(nodes[0], nodes_list)
        self.assertIn(nodes[1], nodes_list)
        self.assertEqual(len(graph.get_source_nodes()), 2)

    def test_cyclic_graph(self):
        """Test that a graph with a cycle raises CyclicDependencyError when traversed."""
        nodes = [
            Node(id=1, name="Node 1"),
            Node(id=2, name="Node 2"),
            Node(id=3, name="Node 3"),
            Node(id=4, name="Node 4"),
            Node(id=5, name="Node 5"),
        ]

        edges = [
            Edge(from_node_id=5, to_node_id=4),
            Edge(from_node_id=4, to_node_id=1),
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=3),
            Edge(from_node_id=3, to_node_id=4),  # Creates a cycle: 4 -> 1 -> 2 -> 3 -> 4
        ]

        graph = Graph(nodes, edges)

        # Verify source nodes can still be retrieved
        expected_source_nodes = [nodes[-1]]
        self.assertEqual(graph.get_source_nodes(), expected_source_nodes)

        # Verify that traversing the graph raises CyclicDependencyError
        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()  # Force graph traversal
        self.assertIn("cycle", str(context.exception))

    def test_multiple_source_nodes(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id="", quantity=1),
            Node(id=2, name="Node 2", node_type="", product_id="", quantity=1),
            Node(id=3, name="Node 3", node_type="", product_id="", quantity=1),
            Node(id=4, name="Node 4", node_type="", product_id="", quantity=1),
        ]

        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=3, to_node_id=4),
        ]

        graph = Graph(nodes, edges)

        source_nodes = graph.get_source_nodes()
        self.assertEqual(len(source_nodes), 2)
        self.assertIn(self.nodes[0], source_nodes)
        self.assertIn(self.nodes[2], source_nodes)

    def test_get_nonexistent_node(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id="", quantity=1),
            Node(id=2, name="Node 2", node_type="", product_id="", quantity=1),
        ]

        graph = Graph(nodes, [])
        with self.assertRaises(KeyError):
            graph.get_node(999)

    def test_incoming_outgoing_edges(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id="", quantity=1),
            Node(id=2, name="Node 2", node_type="", product_id="", quantity=1),
            Node(id=3, name="Node 3", node_type="", product_id="", quantity=1),
            Node(id=4, name="Node 4", node_type="", product_id="", quantity=1),
        ]

        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=3),
            Edge(from_node_id=3, to_node_id=4),
            Edge(from_node_id=1, to_node_id=4),
        ]

        graph = Graph(nodes, edges)

        self.assertEqual(len(graph.get_incoming_edges(nodes[3])), 2)
        self.assertEqual(len(graph.get_outgoing_edges(nodes[0])), 2)
        self.assertEqual(len(graph.get_incoming_edges(nodes[0])), 0)
        self.assertEqual(len(graph.get_outgoing_edges(nodes[3])), 0)

    def test_incoming_outgoing_nodes(self):
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id="", quantity=1),
            Node(id=2, name="Node 2", node_type="", product_id="", quantity=1),
            Node(id=3, name="Node 3", node_type="", product_id="", quantity=1),
            Node(id=4, name="Node 4", node_type="", product_id="", quantity=1),
        ]

        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=3),
            Edge(from_node_id=3, to_node_id=4),
            Edge(from_node_id=1, to_node_id=4),
        ]

        graph = Graph(nodes, edges)

        incoming_nodes = graph.get_incoming_nodes(self.nodes[3])
        self.assertEqual(len(incoming_nodes), 2)
        self.assertIn(self.nodes[0], incoming_nodes)
        self.assertIn(self.nodes[2], incoming_nodes)

        outgoing_nodes = graph.get_outgoing_nodes(self.nodes[0])
        self.assertEqual(len(outgoing_nodes), 2)
        self.assertIn(self.nodes[1], outgoing_nodes)
        self.assertIn(self.nodes[3], outgoing_nodes)

    def test_topological_sort_empty_graph(self):
        """Test topological sort on empty graph."""
        graph = Graph([], [])
        self.assertEqual(graph.get_nodes_topologically_sorted(), [])

    def test_topological_sort_single_node(self):
        """Test topological sort on single node graph."""
        node = Node(id=1, name="Node 1", node_type="", product_id="")
        graph = Graph([node], [])
        self.assertEqual(graph.get_nodes_topologically_sorted(), [node])

    def test_topological_sort_linear_graph(self):
        """Test topological sort on linear graph (chain of nodes)."""
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id=""),
            Node(id=2, name="Node 2", node_type="", product_id=""),
            Node(id=3, name="Node 3", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=3),
        ]
        graph = Graph(nodes, edges)
        sorted_nodes = graph.get_nodes_topologically_sorted()

        # Verify order: Node1 -> Node2 -> Node3
        self.assertEqual(len(sorted_nodes), 3)
        self.assertEqual(sorted_nodes[0].id, 1)
        self.assertEqual(sorted_nodes[1].id, 2)
        self.assertEqual(sorted_nodes[2].id, 3)

    def test_topological_sort_tree_graph(self):
        """Test topological sort on tree-like graph."""
        nodes = [
            Node(id=1, name="Root", node_type="", product_id=""),
            Node(id=2, name="Left", node_type="", product_id=""),
            Node(id=3, name="Right", node_type="", product_id=""),
            Node(id=4, name="Left Child", node_type="", product_id=""),
            Node(id=5, name="Right Child", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),  # Root -> Left
            Edge(from_node_id=1, to_node_id=3),  # Root -> Right
            Edge(from_node_id=2, to_node_id=4),  # Left -> Left Child
            Edge(from_node_id=3, to_node_id=5),  # Right -> Right Child
        ]
        graph = Graph(nodes, edges)
        sorted_nodes = graph.get_nodes_topologically_sorted()

        # Verify root comes first and children come after their parents
        self.assertEqual(len(sorted_nodes), 5)
        self.assertEqual(sorted_nodes[0].id, 1)  # Root must be first
        # Left and Right can be in any order, but must be before their children
        left_idx = next(i for i, n in enumerate(sorted_nodes) if n.id == 2)
        right_idx = next(i for i, n in enumerate(sorted_nodes) if n.id == 3)
        left_child_idx = next(i for i, n in enumerate(sorted_nodes) if n.id == 4)
        right_child_idx = next(i for i, n in enumerate(sorted_nodes) if n.id == 5)

        self.assertGreater(left_child_idx, left_idx)  # Left Child after Left
        self.assertGreater(right_child_idx, right_idx)  # Right Child after Right

    def test_topological_sort_multiple_paths(self):
        """Test topological sort on graph with multiple paths to same node."""
        nodes = [
            Node(id=1, name="Start", node_type="", product_id=""),
            Node(id=2, name="Middle1", node_type="", product_id=""),
            Node(id=3, name="Middle2", node_type="", product_id=""),
            Node(id=4, name="End", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=1, to_node_id=3),
            Edge(from_node_id=2, to_node_id=4),
            Edge(from_node_id=3, to_node_id=4),
        ]
        graph = Graph(nodes, edges)
        sorted_nodes = graph.get_nodes_topologically_sorted()

        # Verify start is first, end is last, and middle nodes are in between
        self.assertEqual(len(sorted_nodes), 4)
        self.assertEqual(sorted_nodes[0].id, 1)  # Start must be first
        self.assertEqual(sorted_nodes[-1].id, 4)  # End must be last
        middle_ids = {sorted_nodes[1].id, sorted_nodes[2].id}
        self.assertEqual(middle_ids, {2, 3})  # Middle nodes can be in any order

    def test_topological_sort_cyclic_graph(self):
        """Test topological sort on graph with cycle (should raise error)."""
        nodes = [
            Node(id=1, name="Node 1", node_type="", product_id=""),
            Node(id=2, name="Node 2", node_type="", product_id=""),
            Node(id=3, name="Node 3", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=3),
            Edge(from_node_id=3, to_node_id=1),  # Creates a cycle
        ]
        graph = Graph(nodes, edges)
        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()
        self.assertIn("cycle", str(context.exception))

    def test_simple_cycle(self):
        """Test detection of simple two-node cycle."""
        nodes = [
            Node(id=1, name="Node A", node_type="", product_id=""),
            Node(id=2, name="Node B", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=1),  # Creates cycle A -> B -> A
        ]
        graph = Graph(nodes, edges)

        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()  # Force graph traversal
        self.assertIn("cycle", str(context.exception))

    def test_self_referential_cycle(self):
        """Test detection of self-referential node (node pointing to itself)."""
        nodes = [Node(id=1, name="Self Node", node_type="", product_id="")]
        edges = [Edge(from_node_id=1, to_node_id=1)]  # Node points to itself
        graph = Graph(nodes, edges)

        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()  # Force graph traversal
        self.assertIn("cycle", str(context.exception))

    def test_complex_cycle(self):
        """Test detection of complex multi-node cycle."""
        nodes = [
            Node(id=1, name="Node A", node_type="", product_id=""),
            Node(id=2, name="Node B", node_type="", product_id=""),
            Node(id=3, name="Node C", node_type="", product_id=""),
            Node(id=4, name="Node D", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=3),
            Edge(from_node_id=3, to_node_id=4),
            Edge(from_node_id=4, to_node_id=2),  # Creates cycle B -> C -> D -> B
        ]
        graph = Graph(nodes, edges)

        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()  # Force graph traversal
        self.assertIn("cycle", str(context.exception))

    def test_multiple_cycles(self):
        """Test detection of multiple cycles in the same graph."""
        nodes = [
            Node(id=1, name="Node A", node_type="", product_id=""),
            Node(id=2, name="Node B", node_type="", product_id=""),
            Node(id=3, name="Node C", node_type="", product_id=""),
            Node(id=4, name="Node D", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=2, to_node_id=1),  # First cycle: A -> B -> A
            Edge(from_node_id=3, to_node_id=4),
            Edge(from_node_id=4, to_node_id=3),  # Second cycle: C -> D -> C
        ]
        graph = Graph(nodes, edges)

        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()  # Force graph traversal
        self.assertIn("cycle", str(context.exception))

    def test_cycle_with_branching(self):
        """Test cycle detection in a graph with multiple paths and branches.
        This test verifies that cycles are properly detected even in complex graphs
        with multiple paths."""
        nodes = [
            Node(id=1, name="Start", node_type="", product_id=""),
            Node(id=2, name="Branch A", node_type="", product_id=""),
            Node(id=3, name="Branch B", node_type="", product_id=""),
            Node(id=4, name="Cycle Node", node_type="", product_id=""),
            Node(id=5, name="End", node_type="", product_id=""),
        ]
        edges = [
            Edge(from_node_id=1, to_node_id=2),
            Edge(from_node_id=1, to_node_id=3),
            Edge(from_node_id=2, to_node_id=4),
            Edge(from_node_id=3, to_node_id=4),
            Edge(from_node_id=4, to_node_id=2),  # Creates cycle with Branch A
            Edge(from_node_id=4, to_node_id=5),
        ]
        graph = Graph(nodes, edges)

        with self.assertRaises(CyclicDependencyError) as context:
            graph.get_nodes_topologically_sorted()  # Force graph traversal
        self.assertIn("cycle", str(context.exception))
