from enum import Enum
from pydantic import BaseModel
from databases.shared.repository import PackagingPredictionRepo
from pipelines_v2.eol.eol_emissions.weight import Weight


class PackagingLevel(Enum):
    PRIMARY = "Primary"
    SECONDARY = "Secondary"
    TERTIARY = "Tertiary"

class PackagingSupplier(BaseModel):
    id: int | None
    name: str
    origin_country: str | None
    origin_city: str | None
    supplier_level: int

class PackageComponent(BaseModel):
    id: int | None = None
    component: str
    material: str
    weight: Weight | None = None
    reusable_packaging: bool = False
    number_of_reuses: float = 0
    number_of_packages: int = 1
    recycled_material: bool = False
    recycled_content_rate: float = 0
    packaging_level: str = PackagingLevel.PRIMARY.value
    supplier: PackagingSupplier | None = None


class PackagingPredictor:
    def __init__(
        self,
        product_category: str,
        content_weight: Weight,
    ) -> None:
        self.predicted_base_material = None
        self.content_weight = content_weight
        self.product_category = product_category
        self.packaging_prediction_repo = PackagingPredictionRepo()

    def _estimate_material_weight_from_content_weight(
        self, weight_ratio_percentage: float
    ) -> Weight:
        return Weight((weight_ratio_percentage / 100) * self.content_weight)

    def _get_primary_components(self) -> list[PackageComponent]:
        predicted_primary_components = (
            self.packaging_prediction_repo.get_packaging_by_category(
                product_category=self.product_category,
                packaging_level=PackagingLevel.PRIMARY.value,
            )
        )

        return [
            PackageComponent(
                component=predicted_primary_component.packaging_item,
                material=predicted_primary_component.packaging_material,
                weight=self._estimate_material_weight_from_content_weight(
                    predicted_primary_component.weight_ratio_percentage
                ),
                number_of_packages=predicted_primary_component.number_of_packages,
                recycled_material=predicted_primary_component.recyclable,
                packaging_level=predicted_primary_component.packaging_level,
            )
            for predicted_primary_component in predicted_primary_components
        ]

    def _get_secondary_components(self) -> list[PackageComponent]:
        predicted_secondary_components = (
            self.packaging_prediction_repo.get_packaging_by_category(
                product_category=self.product_category,
                packaging_level=PackagingLevel.SECONDARY.value,
            )
        )

        return [
            PackageComponent(
                component=predicted_secondary_component.packaging_item,
                material=predicted_secondary_component.packaging_material,
                weight=self._estimate_material_weight_from_content_weight(
                    predicted_secondary_component.weight_ratio_percentage
                ),
                number_of_packages=predicted_secondary_component.number_of_packages,
                recycled_material=predicted_secondary_component.recyclable,
                packaging_level=predicted_secondary_component.packaging_level,
            )
            for predicted_secondary_component in predicted_secondary_components
        ]

    def predict_primary_package_components(
        self,
    ) -> list[PackageComponent]:
        components = self._get_primary_components()
        return components

    def predict_secondary_package_components(
        self,
    ) -> list[PackageComponent]:
        components = self._get_secondary_components()
        return components
