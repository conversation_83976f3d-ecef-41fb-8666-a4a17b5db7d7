from typing import List, Callable, Any
from collections import deque
from databases.tenant.models import Node, Edge


class CyclicDependencyError(ValueError):
    """Raised when a cycle is detected in the graph during traversal.

    This error indicates that the graph contains a circular dependency,
    which is not allowed in certain graph operations like topological sort.
    """


class Graph:
    """
    A graph of nodes and edges with methods for accessing incoming and outgoing nodes given a node.

    The graph can be iterated over to traverse the nodes in topological order.
    This ensures that nodes are processed in dependency order (a node is only processed
    after all its dependencies have been processed).

    Example:
        >>> graph = Graph(nodes, edges)
        >>> for node in graph:
        ...     print(node)
    """

    def __init__(self, nodes: List[Node], edges: List[Edge]):
        self._nodes = nodes
        self._edges = edges
        self._graph = {}

        for node in self._nodes:
            incoming_edges = [
                edge for edge in self._edges if edge.to_node_id == node.id
            ]
            outgoing_edges = [
                edge for edge in self._edges if edge.from_node_id == node.id
            ]

            self._graph[node.id] = {
                "node": node,
                "incoming_edges": incoming_edges,
                "outgoing_edges": outgoing_edges,
            }



    def get_nodes(self) -> List[Node]:
        return self._nodes

    def get_edges(self) -> List[Edge]:
        return self._edges

    def clone(self) -> "Graph":
        nodes = [node.clone() for node in self._nodes]
        edges = [edge.clone() for edge in self._edges]
        return Graph(nodes, edges)

    def get_node(self, node_id: int) -> Node:
        return self._graph[node_id]["node"]

    def get_node_by_name_and_type(self, name: str, node_type: str) -> Node | None:
        return next(
            (
                node
                for node in self._nodes
                if node.name == name and node.node_type == node_type
            ),
            None,
        )

    def get_source_nodes(self) -> List[Node]:
        """Get all source nodes (nodes with no incoming edges)."""
        return [
            node["node"]
            for _, node in self._graph.items()
            if not node["incoming_edges"]
        ]

    def get_incoming_nodes_by_type(
        self,
        node: Node,
        node_types: List[str],
    ) -> List[Node]:
        """
        Find all incoming nodes of the specified node types,
        traversing all possible paths up the tree.

        Args:
            node: The starting node to find incoming nodes from
            node_types: List of node types to match against

        Returns:
            Returns a list of all incoming nodes of the specified types from all paths.
        """
        result = []
        incoming_nodes = self.get_incoming_nodes(node)

        for incoming_node in incoming_nodes:
            # Check if the incoming node matches any of the specified types
            if incoming_node.node_type in node_types:
                result.append(incoming_node)

            # Recursively check all incoming of this node
            result.extend(self.get_incoming_nodes_by_type(incoming_node, node_types))

        return result

    def get_incoming_edges(self, node: Node) -> List[Edge]:
        return self._graph[node.id]["incoming_edges"]

    def get_incoming_nodes(self, node: Node) -> List[Node]:
        return [
            self.get_node(edge.from_node_id) for edge in self.get_incoming_edges(node)
        ]

    def get_outgoing_edges(self, node: Node) -> List[Edge]:
        return self._graph[node.id]["outgoing_edges"]

    def get_outgoing_nodes(self, node: Node) -> List[Node]:
        return [
            self.get_node(edge.to_node_id) for edge in self.get_outgoing_edges(node)
        ]

    def insert_edge(self, edge: Edge):
        self._edges.append(edge)
        self._graph[edge.from_node_id]["outgoing_edges"].append(edge)
        self._graph[edge.to_node_id]["incoming_edges"].append(edge)

    def insert_between_nodes(self, edge: Edge, new_node: Node):
        """Insert a new node between two nodes."""
        self._graph[edge.from_node_id]["outgoing_edges"].remove(edge)
        self._graph[edge.to_node_id]["incoming_edges"].remove(edge)
        self._edges.remove(edge)

        if not isinstance(new_node.id, int):
            new_node.id = self._generate_node_id()

        edge_a = Edge(
            product_id=edge.product_id,
            from_node_id=edge.from_node_id,
            to_node_id=new_node.id,
        )

        edge_b = Edge(
            product_id=edge.product_id,
            from_node_id=new_node.id,
            to_node_id=edge.to_node_id,
        )

        self._edges.extend([edge_a, edge_b])
        self._nodes.append(new_node)

        self._graph[new_node.id] = {
            "node": new_node,
            "incoming_edges": [edge_a],
            "outgoing_edges": [edge_b],
        }

        self._graph[edge.from_node_id]["outgoing_edges"].append(edge_a)
        self._graph[edge.to_node_id]["incoming_edges"].append(edge_b)

    def insert_edges(self, edges: List[Edge]):
        for edge in edges:
            self.insert_edge(edge)

    def remove_edges(self, edges: List[Edge]):
        for edge in edges:
            self._graph[edge.from_node_id]["outgoing_edges"].remove(edge)
            self._graph[edge.to_node_id]["incoming_edges"].remove(edge)
            self._edges.remove(edge)

    def insert_node(self, node: Node):
        if not isinstance(node.id, int):
            node.id = self._generate_node_id()

        self._nodes.append(node)
        self._graph[node.id] = {
            "node": node,
            "incoming_edges": [],
            "outgoing_edges": [],
        }

        return node

    def insert_nodes(self, nodes: List[Node]):
        for node in nodes:
            self.insert_node(node)

    def remove_nodes(self, nodes: List[Node]):
        for node in nodes:
            outgoing_edges = self._graph[node.id]["outgoing_edges"]
            incoming_edges = self._graph[node.id]["incoming_edges"]

            self.remove_edges(outgoing_edges + incoming_edges)

            if node.id in self._graph:
                del self._graph[node.id]

            self._nodes.remove(node)

    def _succeeds(
        self,
        node: Node,
        condition_args: List[Any],
        condition: Callable[[Node, Node], bool],
    ) -> bool:
        if condition(node, *condition_args):
            return False

        visited_node_ids = set()
        outgoing_nodes = [node]
        while outgoing_nodes:
            outgoing_node = outgoing_nodes.pop()
            if outgoing_node.id in visited_node_ids:
                continue
            visited_node_ids.add(outgoing_node.id)
            if condition(outgoing_node, *condition_args):
                return False
            outgoing_nodes.extend(self.get_outgoing_nodes(outgoing_node))
        return True

    def _precedes(
        self,
        node: Node,
        condition_args: List[Any],
        condition: Callable[[Node, Node], bool],
    ) -> bool:
        if condition(node, *condition_args):
            return False

        visited_node_ids = set()
        incoming_nodes = [node]
        while incoming_nodes:
            incoming_node = incoming_nodes.pop()
            if incoming_node.id in visited_node_ids:
                continue
            visited_node_ids.add(incoming_node.id)
            if condition(incoming_node, *condition_args):
                return False
            incoming_nodes.extend(self.get_incoming_nodes(incoming_node))
        return True

    def precedes_type(self, node: Node, node_type: str) -> bool:
        return self._precedes(node, [node_type], lambda n, t: n.node_type == t)

    def precedes_node(self, node: Node, other_node: Node) -> bool:
        return self._precedes(node, [other_node], lambda n, o: n.id == o.id)

    def succeeds_type(self, node: Node, node_type: str) -> bool:
        return self._succeeds(node, [node_type], lambda n, t: n.node_type == t)

    def succeeds_node(self, node: Node, other_node: Node) -> bool:
        return self._succeeds(node, [other_node], lambda n, o: n.id == o.id)

    def _generate_node_id(self) -> int:
        return max(self._graph.keys()) + 1

    def get_nodes_topologically_sorted(self) -> List[Node]:
        """Get nodes in topological sort order using Kahn's algorithm.

        Returns:
            List of nodes in topological order (from source to sink nodes).
            For a process model, this means from raw materials to final products.

        Raises:
            CyclicDependencyError: If the graph contains a cycle.
        """
        # Create a fresh topological sort state
        in_degree = {node.id: 0 for node in self._nodes}
        for edge in self._edges:
            in_degree[edge.to_node_id] += 1

        # Initialize queue with nodes that have no incoming edges (in-degree = 0)
        queue = deque([
            self.get_node(node_id)
            for node_id, degree in in_degree.items()
            if degree == 0
        ])

        # Track visited nodes count for cycle detection
        visited_count = 0
        result = []

        while queue:
            # Get next node with in-degree 0
            next_node = queue.popleft()
            result.append(next_node)
            visited_count += 1

            # For each outgoing edge, reduce in-degree of target node
            # If in-degree becomes 0, add to queue
            for edge in self.get_outgoing_edges(next_node):
                to_node_id = edge.to_node_id
                in_degree[to_node_id] -= 1
                if in_degree[to_node_id] == 0:
                    queue.append(self.get_node(to_node_id))

        # If we haven't visited all nodes but queue is empty, there's a cycle
        if visited_count < len(self._nodes):
            raise CyclicDependencyError(
                "Graph contains a cycle - this indicates a circular dependency between nodes"
            )

        return result
