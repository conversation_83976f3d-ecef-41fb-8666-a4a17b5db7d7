from typing import List, Dict, Any
from enum import Enum

class Unit:
    """A unit of measurement with conversion capabilities.

    Args:
        category (str): The type of measurement (length, weight, energy, volume)
        factor (float): Conversion factor relative to the base unit
        abbrev (str): Standard abbreviation for the unit
        label (str): The display label for the unit (optional, defaults to abbrev)
        full_name (str): The full name for the unit

    Examples:
        >>> meter = Unit('length', 1, 'm')
        >>> kilometer = Unit('length', 1000, 'km')
        >>> meter.convert(5, kilometer)
        0.005
        >>> kilometer.convert(0.005, meter)
        5.0

        Units of different categories cannot be converted:
        >>> gram = Unit('weight', 1, 'g')
        >>> meter.convert(100, gram)
        Traceback (most recent call last):
            ...
        ValueError: Cannot convert from m to g
    """
    def __init__(self, category, factor, abbrev, label=None, full_name=None):
        self.category = category
        self.factor = factor
        self.abbrev = abbrev
        self.label = label or abbrev
        self.full_name = full_name

    def convert(self, value: float, to_unit: 'Unit') -> float:
        if self.category != to_unit.category:
            raise ValueError(f"Cannot convert from {self.abbrev} to {to_unit.abbrev}")

        value_in_base = value * self.factor
        return value_in_base / to_unit.factor

    def dict(self) -> Dict[str, Any]:
        return {
            "abbrev": self.abbrev,
            "category": self.category,
            "label": self.label,
            "full_name": self.full_name,
        }

class UnitType(Enum):
    """A collection of unit types and conversion utilities for physical measurements.

    This enum provides unit definitions for length, weight, energy, and volume measurements,
    along with methods for converting between units of the same category.

    Each unit has:
        - category: The type of measurement (length, weight, energy, volume)
        - factor: Conversion factor relative to the base unit
        - abbrev: Standard abbreviation (e.g., 'km' for kilometer)

    Base units for each category:
        - length: meter (m)
        - weight: gram (g)
        - energy: joule (J)
        - volume: cubic meter (m³)

    Examples:
        Length conversion:
        >>> UnitType.convert_from_abbrev(5, 'km', 'm')
        5000.0

        Weight conversion:
        >>> UnitType.convert_from_abbrev(2.5, 'kg', 'g')
        2500.0

        Weight conversion with uppercase abbreviation:
        >>> UnitType.convert_from_abbrev(0.5, 'Kg', 'g')
        500.0

        Volume conversion:
        >>> UnitType.convert_from_abbrev(1, 'l', 'ml')
        1000.0000000000001

        Using abbreviations:
        >>> km_unit = UnitType.get_by_abbrev('km').value
        >>> m_unit = UnitType.get_by_abbrev('m').value
        >>> UnitType.convert_from_abbrev(5, km_unit.abbrev, m_unit.abbrev)
        5000.0
        >>> g_unit = UnitType.get_by_abbrev('g').value
        >>> gram_unit = UnitType.get_by_abbrev('gram').value
        >>> grams_unit = UnitType.get_by_abbrev('grams').value
        >>> g_unit.category == gram_unit.category == grams_unit.category
        True
        >>> g_unit.factor == gram_unit.factor == grams_unit.factor
        True
        >>> g_unit.label == gram_unit.label == grams_unit.label
        True


        Invalid conversions raise ValueError:
        >>> UnitType.convert_from_abbrev(
        ...     100,
        ...     UnitType.METER.value.abbrev,
        ...     UnitType.GRAM.value.abbrev,
        ... )
        Traceback (most recent call last):
            ...
        ValueError: Cannot convert from m to g

        Invalid abbreviations raise ValueError:
        >>> UnitType.get_by_abbrev('invalid')
        Traceback (most recent call last):
            ...
        ValueError: No unit found with abbreviation invalid
    """
    UNIT = Unit('unit', 1, 'unit', 'Unit')
    GUEST_NIGHT = Unit('unit', 1, 'guest night', 'Guest Night', 'guest night')

    MILLIMETER = Unit('length', 0.001, 'mm', 'Millimeter (mm)', 'millimeter')
    METER = Unit('length', 1, 'm', 'Meter (m)', 'meter')
    KILOMETER = Unit('length', 1000, 'km', 'Kilometer (km)', 'kilometer')

    SQUARE_METER = Unit('area', 1, 'm2', 'Square Meter (m²)', 'square meter')
    HECTARE = Unit('area', 10000, 'ha', 'Hectare (ha)', 'hectare')

    MILLIGRAM = Unit('weight', 0.001, 'mg', 'Milligram (mg)', 'milligram')
    GRAM = Unit('weight', 1, 'g', 'Gram (g)', 'gram')
    KILOGRAM = Unit('weight', 1000, 'kg', 'Kilogram (kg)', 'kilogram')
    TONNE = Unit('weight', 1000000, 't', 'Metric Tonne (t)', 'metric tonne')

    JOULE = Unit('energy', 1, 'J', 'Joule (J)', 'joule')
    CALORIE = Unit('energy', 4.184, 'cal', 'Calorie (cal)', 'calorie')
    KILOWATT_HOUR = Unit('energy', 3600000, 'kWh', 'Kilowatt-Hour (kWh)', 'kilowatt hour')

    MILLILITER = Unit('volume', 0.000001, 'ml', 'Milliliter (ml)', 'milliliter')
    LITER = Unit('volume', 0.001, 'l', 'Liter (l)', 'liter')
    CUBIC_METER = Unit('volume', 1, 'm3', 'Cubic Meter (m³)', 'cubic meter')

    SECOND = Unit('time', 1, 'second', 'Second (s)', 'second')
    MINUTE = Unit('time', 60, 'minute', 'Minute (min)', 'minute')
    HOUR = Unit('time', 3600, 'hour', 'Hour (hr)', 'hour')
    DAY = Unit('time', 86400, 'day', 'Day (d)', 'day')
    YEAR = Unit('time', 31536000, 'year', 'Year (yr)', 'year')

    METER_YEAR = Unit(
        'distance-time',
        1,
        'm*year',
        'Meter-Year (m*yr)',
        'meter year'
    )
    KILOMETER_YEAR = Unit(
        'distance-time',
        1000,
        'km*year',
        'Kilometer-Year (km*yr)',
        'kilometer year'
    )

    SQUARE_METER_YEAR = Unit(
        'area-time',
        1,
        'm2*year',
        'Square Meter-Year (m²*yr)',
        'square meter year'
    )

    KILOGRAMS_DAY = Unit(
        'mass-time',
        1,
        'kg*day',
        'Kilogram-Day (kg*d)',
        'kilogram day'
    )

    PERSON_KILOMETER = Unit(
        'person-distance',
        1,
        'person*km',
        'Person-Kilometer (person*km)',
        'person kilometer'
    )

    GRAM_KILOMETER = Unit(
        'mass-distance',
        0.000001,
        'g*km',
        'Gram-Kilometer (g*km)',
        'gram kilometer'
    )
    KILOGRAM_KILOMETER = Unit(
        'mass-distance',
        0.001,
        'kg*km',
        'Kilogram-Kilometer (kg*km)',
        'kilogram kilometer'
    )
    METRIC_TON_KILOMETER = Unit(
        'mass-distance',
        1,
        'metric ton*km',
        'Metric Ton-Kilometer (metric ton*km)',
        'metric ton kilometer'
    )

    @classmethod
    def get_by_abbrev(cls, abbrev: str) -> Unit:
        abbrev_lower = abbrev.lower()
        for unit in cls:
            if unit.value.abbrev.lower() == abbrev_lower:
                return unit
            if (unit.value.full_name and
                abbrev_lower in (unit.value.full_name.lower(), unit.value.full_name.lower() + 's')):
                return unit
        raise ValueError(f"No unit found with abbreviation {abbrev}")

    @classmethod
    def convert_from_abbrev(
        cls,
        value: float,
        from_unit_abbrev: str,
        to_unit_abbrev: str,
    ) -> float:
        """Convert a value from one unit to another using the abbreviations."""
        from_unit = cls.get_by_abbrev(from_unit_abbrev).value
        to_unit = cls.get_by_abbrev(to_unit_abbrev).value
        return from_unit.convert(value, to_unit)

    @classmethod
    def get_all(cls) -> List[Dict[str, Any]]:
        return [
            unit.value.dict()
            for unit in cls
        ]

def get_valid_conversions(unit_abbrev: str) -> List[str]:
    unit = UnitType.get_by_abbrev(unit_abbrev)
    return [
        unit_.value.abbrev for unit_ in UnitType
        if unit.value.category == unit_.value.category
    ]
