from unittest import TestCase
from pipelines_v2.utils.unit import UnitType

class TestUnit(TestCase):
    def test_convert_to_other_unit(self):
        # Millimeters to Meters - Meters to Millimeters
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.MILLIMETER.value.abbrev,
                UnitType.METER.value.abbrev,
            ),
            0.005,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.METER.value.abbrev,
                UnitType.MILLIMETER.value.abbrev,
            ),
            5000,
        )

        # Millimeters to Kilometers - Kilometers to Millimeters
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.MILLIMETER.value.abbrev,
                UnitType.KILOMETER.value.abbrev,
            ),
            0.000005,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.KILOMETER.value.abbrev,
                UnitType.MILLIMETER.value.abbrev,
            ),
            5000000,
        )

        # Meters to Kilometers - Kilometers to Meters
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.METER.value.abbrev,
                UnitType.KILOMETER.value.abbrev,
            ),
            0.005,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                2.5,
                UnitType.KILOMETER.value.abbrev,
                UnitType.METER.value.abbrev,
            ),
            2500,
        )

        # Milligrams to Grams - Grams to Milligrams
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.MILLIGRAM.value.abbrev,
                UnitType.GRAM.value.abbrev,
            ),
            0.005,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.GRAM.value.abbrev,
                UnitType.MILLIGRAM.value.abbrev,
            ),
            5000,
        )

        # Milligrams to Kilograms - Kilograms to Milligrams
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.MILLIGRAM.value.abbrev,
                UnitType.KILOGRAM.value.abbrev,
            ),
            0.000005,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.KILOGRAM.value.abbrev,
                UnitType.MILLIGRAM.value.abbrev,
            ),
            5000000,
        )

        # Milligrams to Tonnes - Tonnes to Milligrams
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.MILLIGRAM.value.abbrev,
                UnitType.TONNE.value.abbrev,
            ),
            0.000000005,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.TONNE.value.abbrev,
                UnitType.MILLIGRAM.value.abbrev,
            ),
            5000000000,
        )

        # Grams to Kilograms - Kilograms to Grams
        self.assertEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.GRAM.value.abbrev,
                UnitType.KILOGRAM.value.abbrev,
            ),
            0.003,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                9.9,
                UnitType.KILOGRAM.value.abbrev,
                UnitType.GRAM.value.abbrev,
            ),
            9900,
        )

        # Grams to Tonnes - Tonnes to Grams
        self.assertEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.GRAM.value.abbrev,
                UnitType.TONNE.value.abbrev,
            ),
            0.000003,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                9.9,
                UnitType.TONNE.value.abbrev,
                UnitType.GRAM.value.abbrev,
            ),
            9900000,
        )

        # Kilograms to Tonnes - Tonnes to Kilograms
        self.assertEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.KILOGRAM.value.abbrev,
                UnitType.TONNE.value.abbrev,
            ),
            0.003,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                9.9,
                UnitType.TONNE.value.abbrev,
                UnitType.KILOGRAM.value.abbrev,
            ),
            9900,
        )

        # Milliliters to Liters - Liters to Milliliters
        self.assertEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.MILLILITER.value.abbrev,
                UnitType.LITER.value.abbrev,
            ),
            0.003,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                9.9,
                UnitType.LITER.value.abbrev,
                UnitType.MILLILITER.value.abbrev,
            ),
            9900,
            places=6,
        )

        # Milliliters to Cubic Meters - Cubic Meters to Milliliters
        self.assertEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.MILLILITER.value.abbrev,
                UnitType.CUBIC_METER.value.abbrev,
            ),
            0.000003,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                9.9,
                UnitType.CUBIC_METER.value.abbrev,
                UnitType.MILLILITER.value.abbrev,
            ),
            9900000,
        )

        # Liters to Cubic Meters - Cubic Meters to Liters
        self.assertEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.LITER.value.abbrev,
                UnitType.CUBIC_METER.value.abbrev,
            ),
            0.003,
        )
        self.assertEqual(
            UnitType.convert_from_abbrev(
                9.9,
                UnitType.CUBIC_METER.value.abbrev,
                UnitType.LITER.value.abbrev,
            ),
            9900,
        )

        # Joules to Calories - Calories to Joules
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.JOULE.value.abbrev,
                UnitType.CALORIE.value.abbrev,
            ),
            1.195,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                1.195,
                UnitType.CALORIE.value.abbrev,
                UnitType.JOULE.value.abbrev,
            ),
            5,
            places=2,
        )

        # Joules to Kilowatt Hours - Kilowatt Hours to Joules
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                5,
                UnitType.JOULE.value.abbrev,
                UnitType.KILOWATT_HOUR.value.abbrev,
            ),
            0.000001389,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.000001389,
                UnitType.KILOWATT_HOUR.value.abbrev,
                UnitType.JOULE.value.abbrev,
            ),
            5,
            places=2,
        )

        # Square Meters to Hectares - Hectares to Square Meters
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.SQUARE_METER.value.abbrev,
                UnitType.HECTARE.value.abbrev,
            ),
            0.0003,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.0003,
                UnitType.HECTARE.value.abbrev,
                UnitType.SQUARE_METER.value.abbrev,
            ),
            3,
            places=2,
        )

        # Second to Minute - Minute to Second
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.SECOND.value.abbrev,
                UnitType.MINUTE.value.abbrev,
            ),
            0.05,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.05,
                UnitType.MINUTE.value.abbrev,
                UnitType.SECOND.value.abbrev,
            ),
            3,
            places=2,
        )

        # Second to Hour - Hour to Second
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.SECOND.value.abbrev,
                UnitType.HOUR.value.abbrev,
            ),
            0.000833333,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.000833333,
                UnitType.HOUR.value.abbrev,
                UnitType.SECOND.value.abbrev,
            ),
            3,
            places=2,
        )

        # Second to Day - Day to Second
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.SECOND.value.abbrev,
                UnitType.DAY.value.abbrev,
            ),
            0.000034722,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.000034722,
                UnitType.DAY.value.abbrev,
                UnitType.SECOND.value.abbrev,
            ),
            3,
            places=2,
        )

        # Second to Year - Year to Second
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                30,
                UnitType.SECOND.value.abbrev,
                UnitType.YEAR.value.abbrev,
            ),
            0.000000951,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.5,
                UnitType.YEAR.value.abbrev,
                UnitType.SECOND.value.abbrev,
            ),
            15_768_000,
            places=2,
        )

        # Minute to Hour - Hour to Minute
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.MINUTE.value.abbrev,
                UnitType.HOUR.value.abbrev,
            ),
            0.05,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.05,
                UnitType.HOUR.value.abbrev,
                UnitType.MINUTE.value.abbrev,
            ),
            3,
            places=2,
        )

        # Minute to Day - Day to Minute
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.MINUTE.value.abbrev,
                UnitType.DAY.value.abbrev,
            ),
            0.00208,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.5,
                UnitType.DAY.value.abbrev,
                UnitType.MINUTE.value.abbrev,
            ),
            720,
            places=2,
        )

        # Minute to Year - Year to Minute
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                6000,
                UnitType.MINUTE.value.abbrev,
                UnitType.YEAR.value.abbrev,
            ),
            0.0114,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.5,
                UnitType.YEAR.value.abbrev,
                UnitType.MINUTE.value.abbrev,
            ),
            262_800,
            places=2,
        )

        # Hour to Day - Day to Hour
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.HOUR.value.abbrev,
                UnitType.DAY.value.abbrev,
            ),
            0.125,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.125,
                UnitType.DAY.value.abbrev,
                UnitType.HOUR.value.abbrev,
            ),
            3,
            places=2,
        )

        # Hour to Year - Year to Hour
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.HOUR.value.abbrev,
                UnitType.YEAR.value.abbrev,
            ),
            0.000347222,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.000347222,
                UnitType.YEAR.value.abbrev,
                UnitType.HOUR.value.abbrev,
            ),
            3.0416,
            places=2,
        )

        # Day to Year - Year to Day
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                180,
                UnitType.DAY.value.abbrev,
                UnitType.YEAR.value.abbrev,
            ),
            0.4932,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.4,
                UnitType.YEAR.value.abbrev,
                UnitType.DAY.value.abbrev,
            ),
            146,
            places=2,
        )

        # Meter Year to Kilometer Year - Kilometer Year to Meter Year
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                3,
                UnitType.METER_YEAR.value.abbrev,
                UnitType.KILOMETER_YEAR.value.abbrev,
            ),
            0.003,
            places=2,
        )
        self.assertAlmostEqual(
            UnitType.convert_from_abbrev(
                0.003,
                UnitType.KILOMETER_YEAR.value.abbrev,
                UnitType.METER_YEAR.value.abbrev,
            ),
            3,
            places=2,
        )

    def test_convert_to_incompatible_unit(self):
        with self.assertRaises(ValueError):
            UnitType.convert_from_abbrev(
                5,
                UnitType.MILLIMETER.value.abbrev,
                UnitType.KILOWATT_HOUR.value.abbrev,
            )

        with self.assertRaises(ValueError):
            UnitType.convert_from_abbrev(
                3,
                UnitType.MILLILITER.value.abbrev,
                UnitType.KILOWATT_HOUR.value.abbrev,
            )

        with self.assertRaises(ValueError):
            UnitType.convert_from_abbrev(
                3,
                UnitType.MILLILITER.value.abbrev,
                UnitType.TONNE.value.abbrev,
            )

        with self.assertRaises(ValueError):
            UnitType.convert_from_abbrev(
                3,
                UnitType.KILOGRAM.value.abbrev,
                UnitType.KILOMETER.value.abbrev,
            )

    def test_get_by_abbrev(self):
        self.assertEqual(UnitType.get_by_abbrev('mm'), UnitType.MILLIMETER)
        self.assertEqual(UnitType.get_by_abbrev('m'), UnitType.METER)
        self.assertEqual(UnitType.get_by_abbrev('km'), UnitType.KILOMETER)

        self.assertEqual(UnitType.get_by_abbrev('mg'), UnitType.MILLIGRAM)
        self.assertEqual(UnitType.get_by_abbrev('g'), UnitType.GRAM)
        self.assertEqual(UnitType.get_by_abbrev('kg'), UnitType.KILOGRAM)
        self.assertEqual(UnitType.get_by_abbrev('t'), UnitType.TONNE)

        self.assertEqual(UnitType.get_by_abbrev('J'), UnitType.JOULE)
        self.assertEqual(UnitType.get_by_abbrev('cal'), UnitType.CALORIE)
        self.assertEqual(UnitType.get_by_abbrev('kWh'), UnitType.KILOWATT_HOUR)

        self.assertEqual(UnitType.get_by_abbrev('ml'), UnitType.MILLILITER)
        self.assertEqual(UnitType.get_by_abbrev('l'), UnitType.LITER)
        self.assertEqual(UnitType.get_by_abbrev('m3'), UnitType.CUBIC_METER)

        self.assertEqual(UnitType.get_by_abbrev('ha'), UnitType.HECTARE)
        self.assertEqual(UnitType.get_by_abbrev('m2'), UnitType.SQUARE_METER)

        self.assertEqual(UnitType.get_by_abbrev('m2*year'), UnitType.SQUARE_METER_YEAR)

        self.assertEqual(UnitType.get_by_abbrev('m*year'), UnitType.METER_YEAR)
        self.assertEqual(UnitType.get_by_abbrev('km*year'), UnitType.KILOMETER_YEAR)

        self.assertEqual(UnitType.get_by_abbrev('metric ton*km'), UnitType.METRIC_TON_KILOMETER)

        self.assertEqual(UnitType.get_by_abbrev('kg*day'), UnitType.KILOGRAMS_DAY)

        self.assertEqual(UnitType.get_by_abbrev('person*km'), UnitType.PERSON_KILOMETER)

        self.assertEqual(UnitType.get_by_abbrev('guest night'), UnitType.GUEST_NIGHT)
        self.assertEqual(UnitType.get_by_abbrev('unit'), UnitType.UNIT)

        with self.assertRaises(ValueError):
            UnitType.get_by_abbrev('invalid')
