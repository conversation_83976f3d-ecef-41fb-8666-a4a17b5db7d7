from typing import List, Dict
from cachetools import TTLCache, cached
from propelauth_py import init_base_auth, OrgQueryOrderBy
from config import config


cache = TTLCache(maxsize=100, ttl=300)

def _get_auth() -> object:
    return init_base_auth(
        config.PROPEL_AUTH_URL,
        config.PROPEL_AUTH_TOKEN,
    )


def get_orgs() -> List[str]:
    auth = _get_auth()
    page = 0
    orgs = []

    while True:  # Paginate through all orgs
        org_batch = auth.fetch_org_by_query(
            page_size=100,
            page_number=page,
            order_by=OrgQueryOrderBy.CREATED_AT_ASC,
        )

        orgs += org_batch["orgs"]
        page += 1

        if not org_batch["has_more_results"]:
            break

    return [org["name"].lower() for org in orgs]


@cached(cache)
def get_org_by_name(org_name: str) -> Dict | None:
    auth = _get_auth()
    orgs = auth.fetch_org_by_query(
        page_size=1,
        page_number=0,
        name=org_name,
    )

    org = next(
        (org for org in orgs["orgs"] if org["name"].lower() == org_name.lower()),
        None,
    )

    return org


def get_org_metadata_field(org_name: str, field_name: str) -> str | None:
    org = get_org_by_name(org_name)
    if not org or not org.metadata:
        return None

    return org.metadata.get(field_name)

DEFAULT_SUPPORTED_IMPACT_FACTORS = {
    "ReCiPe 2016 v1.03, midpoint (H)": [
        {
            "name": "climate change",
            "indicator": "global warming potential (GWP100)",
            "alias": "Climate Change",
            "unit": "kg CO2e/kg",
        }
    ],
}

def is_carbon_only(org_name: str) -> bool:
    return get_org_metadata_field(org_name, "supported_impact_factors") is None

def get_supported_impact_factors(org_name: str) -> List[Dict] | None:
    impact_factors = (
        get_org_metadata_field(org_name, "supported_impact_factors")
        or DEFAULT_SUPPORTED_IMPACT_FACTORS
    )
    supported_impact_factors = []

    for method, factors in impact_factors.items():
        impact_categories = [factor["name"] for factor in factors]
        supported_impact_factors.append(
            {"impact_categories": impact_categories, "lcia_method": method}
        )

    return supported_impact_factors
