from typing import List, Literal
import urllib.parse
from pydantic import BaseModel
import httpx
from config import config


class MLModelsRequestException(Exception):
    """Custom exception for ml-models-related errors."""

    def __init__(self, message=None):
        super().__init__(message)
        self.message = message

    def __str__(self):
        if bool(self.__cause__):
            return f"{self.message} (Caused by {self.__cause__})"
        return self.message

class Activity(BaseModel):
    activity_name: str
    reference_product_name: str
    product_information: str
    source: str
    geography: str | None = None
    similarity: float | None = None
    curated: bool | None = None
    activity_uuid: str | None = None

class ActivityResponse(BaseModel):
    matched_activity: Activity
    recommendations: List[Activity]
    explanation: str
    confidence: Literal["low", "medium", "high"]


class MLModels:
    def __init__(self) -> None:
        self._emissions_factor_endpoint = (
            config.ML_MODELS_ENDPOINT + "api" "/emissions-factor-matching"
        )
        self._category_prediction_endpoint = (
            config.ML_MODELS_ENDPOINT + "api" "/product-category-prediction"
        )
        self._chemical_prediction_endpoint = (
            config.ML_MODELS_ENDPOINT + "api" "/chemical-prediction"
        )

    async def predict_activity_recommendations(
        self,
        chemical_name: str,
        product_category: str | None,
        valid_units: List[str] | None = None,
        geography: str = "GLO",
        number_of_matches: int = 25,
        version: Literal["latest", "beta"] = "latest",
        lcia_method: str | None = None,
        carbon_only: bool | None = None,
    ) -> List[Activity] | ActivityResponse:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_LONG)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            payload = {
                "chemical_name": chemical_name,
                "geography": geography,
                "number_of_matches": number_of_matches,
            }
            if product_category:
                payload["product_category"] = product_category
            if valid_units:
                payload["valid_units"] = valid_units
            if lcia_method:
                payload["lcia_method"] = lcia_method
            if carbon_only:
                payload["carbon_only"] = carbon_only

            try:
                response = await http_client.post(
                    self._emissions_factor_endpoint + "/activities/recommendations",
                    json=payload,
                    headers={"api-version": version},
                )

                response.raise_for_status()
                data = response.json()

                return ActivityResponse(**data)

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        "Error Predicting Activity Recommendations: "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error


    async def predict_eol_activity(
        self,
        material: str,
        disposal_method: str,
        version: Literal["latest", "beta"] = "latest",
    ):
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_LONG)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            payload = {
                "material": material,
                "disposal_method": disposal_method,
            }

            try:
                response = await http_client.post(
                    self._emissions_factor_endpoint + "/eol/activity",
                    json=payload,
                    headers={"api-version": version},
                )

                response.raise_for_status()
                data = response.json()
                return data

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        "Error predicting eol activity: "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error

    async def predict_has_eol(self, product_category: str) -> bool:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_SHORT)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            try:
                response = await http_client.get(
                    (
                        self._chemical_prediction_endpoint
                        + "/predict-has-eol-disposal/"
                        + product_category
                    ),
                )

                response.raise_for_status()
                data = response.json()
                return data["has_eol_disposal"]

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error

    async def predict_eol_category(
        self,
        material_name: str,
        version: Literal["latest", "beta"] = "latest",
    ) -> str:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_LONG)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            try:
                response = await http_client.get(
                    (
                        self._chemical_prediction_endpoint
                        + "/predict-eol-material-classification/"
                        + material_name
                    ),
                    headers={"api-version": version},
                )

                response.raise_for_status()
                data = response.json()
                return data["classification"]

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        "Error predicting eol category: "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error

    async def geography_match(
        self,
        activity_name: str,
        reference_product_name: str,
        target_geography_iso: str,
        version: Literal["latest", "beta"] = "latest",
    ) -> Activity:
        """Get the closest target geography for a given activity."""
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_SHORT)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            params = {
                "activity_name": activity_name,
                "reference_product_name": reference_product_name,
                "target_geography_iso": target_geography_iso,
            }

            try:
                response = await http_client.get(
                    self._emissions_factor_endpoint + "/geography-match",
                    params=params,
                    headers={"api-version": version},
                )

                response.raise_for_status()
                data = response.json()

                return Activity(**data)

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        "Error getting geography match: "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error



    async def predict_product_category(
        self,
        product_name: str,
        version: Literal["latest", "beta"] = "latest",
    ) -> List[str]:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_LONG)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            try:
                encoded_product_name = urllib.parse.quote(product_name)
                response = await http_client.get(
                    (
                        self._category_prediction_endpoint
                        + "/product-name/"
                        + encoded_product_name
                    ),
                    headers={"api-version": version},
                )

                data = response.json()
                return data["category"]

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        f"Error predicting product category for '{product_name}' "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error

    async def predict_chemical_hscode(
        self,
        chemical_name: str,
        version: Literal["latest", "beta"] = "latest",
    ) -> str:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_SHORT)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            try:
                encoded_chemical_name = urllib.parse.quote(chemical_name)
                response = await http_client.get(
                    (
                        self._chemical_prediction_endpoint
                        + "/predict-hscode/"
                        + encoded_chemical_name
                    ),
                    headers={"api-version": version},
                )
                data = response.json()
                return data["hscode"]

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        f"Error predicting chemical hscode for '{chemical_name}' "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error

    async def predict_cas_number(
        self,
        chemical_name: str,
    ) -> str:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_SHORT)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            try:
                encoded_chemical_name = urllib.parse.quote(chemical_name)
                response = await http_client.get(
                    (
                        self._chemical_prediction_endpoint
                        + "/predict-cas-number/"
                        + encoded_chemical_name
                    ),
                )
                data = response.json()
                return data["cas_number"]

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        f"Error predicting chemical cas_number for '{chemical_name}' "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error

    async def predict_match_quality(
        self,
        process_name: str,
        activity_name: str,
    ) -> str:
        timeout = httpx.Timeout(config.ML_MODEL_TIMEOUT_LONG)

        async with httpx.AsyncClient(timeout=timeout) as http_client:
            try:
                params = {
                    "process_name": process_name,
                    "matched_activity_name": activity_name,
                }
                response = await http_client.get(
                    (
                        self._emissions_factor_endpoint
                        + "/match-quality"
                    ),
                    params=params,
                )
                data = response.json()
                return data["match_quality"]

            except httpx.HTTPStatusError as error:
                raise MLModelsRequestException(
                    (
                        f"Error predicting match quality for '{process_name}' "
                        f"and '{activity_name}' "
                        f"status code {error.response.status_code}"
                    ),
                ) from error

            except httpx.TimeoutException as error:
                raise MLModelsRequestException("Request Timeout") from error

            except httpx.RequestError as error:
                raise MLModelsRequestException("Request Error") from error
