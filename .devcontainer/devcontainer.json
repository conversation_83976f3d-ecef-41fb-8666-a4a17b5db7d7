{"image": "mcr.microsoft.com/devcontainers/python:0-3.10-bullseye", "features": {"docker-in-docker": {"version": "latest", "moby": true, "dockerDashComposeVersion": "v1"}}, "postCreateCommand": "apt-get update && apt-get install -y libreoffice && pip3 install --user -r requirements.txt", "customizations": {"vscode": {"extensions": ["vscodevim.vim", "ms-python.python", "ms-python.pylint"]}}, "remoteUser": "root"}