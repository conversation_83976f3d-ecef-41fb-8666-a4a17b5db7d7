#!/bin/sh
COMMON_PARAMS="api.app:app --host 0.0.0.0 --port $PORT"
UVICORN_PARAMS="$COMMON_PARAMS --workers $WORKERS --timeout-keep-alive 120"

if [ "$DEBUG" = "true" ]; then
    python -m debugpy --wait-for-client --listen ${DEBUG_PORT:-5678} -m uvicorn $COMMON_PARAMS
elif [ "$PROFILE" = "true" ]; then
    python -m cProfile -o profile.prof -m uvicorn $UVICORN_PARAMS
else
    uvicorn $UVICORN_PARAMS
fi
