#!/bin/bash

# Run Mysql instance locally
CONTAINER_NAME="lca-mysql"
MYSQL_TAG="8.0"

check_docker() {
    docker info >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Docker daemon not running, starting Docker..."
        sudo systemctl start docker
        sleep 5
        docker info >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            echo "Failed to start Docker."
            exit 1
        fi
    fi
    echo "Docker is running."
}

check_docker

if [ "$(docker ps -aq -f name=^${CONTAINER_NAME}$)" ]; then
    echo "Container $CONTAINER_NAME exists. Removing it..."
    docker stop $CONTAINER_NAME
    docker rm -f $CONTAINER_NAME
fi

chmod 644 $(pwd)/scripts/config/my.cnf

echo "Starting mysql container..."
docker run --name $CONTAINER_NAME -e MYSQL_ALLOW_EMPTY_PASSWORD=yes -p 3306:3306 -v ./scripts/config/my.cnf:/etc/my.cnf -d mysql:$MYSQL_TAG

until docker exec $CONTAINER_NAME mysqladmin ping --silent; do
    echo "Waiting for mysql to start..."
    sleep 2
done