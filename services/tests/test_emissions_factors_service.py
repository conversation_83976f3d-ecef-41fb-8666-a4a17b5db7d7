from unittest import TestCase
from sqlmodel import Session
from databases.shared.models.emissions_factor_value import EmissionFactorValueRW
from databases.tenant.repository.tenant_impact_indicator import TenantImpactIndicatorRepo
from databases.shared.repository.emission_factor_value import emission_factor_value
from databases.engine import engine_pool
from databases.shared.repository import (
    emissions_factors,
    intermediate_exchanges,
)
from databases.tenant.repository import (
    TenantEmissionsFactorRepo,
    TenantIntermediateExchangeRepo,
)
from databases.tenant.repository import TenantEmissionsFactorValueRepo
from setup.db import setup_server
from services.emissions_factors_service import (
    Exchange,
    EmissionsFactorsService
)
from clients.ml_models import Activity
from utils.async_call import safe_async


class TestEmissionsFactorsService(TestCase):
    tenant_id = "testtenant1"
    emissions_factor = {
        "activity_name": "water production, deionised",
        "reference_product": "water, deionised",
        "geography": "RoW",
        "source": "Ecoinvent 3.11"
    }

    # Add exchange test data as class variable
    exchange_data = {
        "exchange_name": "sodium chloride, powder",
        "exchange_amount": 2,
        "exchange_unit": "kg",
        "exchange_activity_name": "sodium chloride production, powder",
        "exchange_reference_product": "sodium chloride, powder",
        "exchange_geography": "RER",
        "exchange_source": "Ecoinvent 3.11"
    }

    @classmethod
    def setUpClass(cls):

        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.exec(
                f"DROP DATABASE IF EXISTS tenant_{cls.tenant_id}"
            )
            session.commit()

        setup_server(engine, tenant_ids=[cls.tenant_id])


    def setUp(self):
        engine = engine_pool.get_tenant_engine(self.tenant_id)
        with Session(engine) as session:
            session.exec(
                "DELETE FROM tenant_emissions_factor;"
            )
            session.commit()

    def test_get_emissions_factor_match(self):
        activity_name = "sodium chloride powder"
        geography = "GB"

        service = EmissionsFactorsService()
        ef_match = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=activity_name,
            geography=geography,
            number_of_matches=25,
            product_category=None,
            geography_modeling=True,
            valid_units=None,
            impact_factors=None,
            carbon_only=False,
        )

        self.assertIsNotNone(ef_match)
        self.assertEqual(len(ef_match.recommendations), 24)
        self.assertGreaterEqual(
            len(ef_match.matched_activity.exchanges),
            1,
            "Matched activity should have more than one exchange"
        )
        self.assertTrue(
            ef_match.confidence in ("low", "medium", "high")
        )
        top_match = ef_match.matched_activity
        # Get number of ef_values for top matched activity from
        # the shared database. Its currently 52 for 3 types of LCIA
        # methods supported
        self.assertGreaterEqual(
            len(top_match.elemental_ef_values),
            52
        )

    def test_get_emissions_factor_carbon_only(self):
        activity_name = "gel silica"
        geography = "GB"

        service = EmissionsFactorsService()
        ef_match = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=activity_name,
            geography=geography,
            number_of_matches=25,
            carbon_only=True,
        )

        self.assertIsNotNone(ef_match)
        top_match = ef_match.matched_activity

        self.assertEqual(top_match.source, "CarbonBright")

        ef_match = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=activity_name,
            geography=geography,
            number_of_matches=25,
            impact_factors=[{"lcia_method": "ReCiPe 2016 v1.03, midpoint (H)"}],
            carbon_only=False,
        )

        self.assertIsNotNone(ef_match)
        top_match = ef_match.matched_activity
        self.assertNotEqual(top_match.source, "CarbonBright")

    def test_create_custom_emissions_factor(self):
        activity_name = "Vibranium production"
        activity_type = "Raw Materials"
        reference_product = "Vibranium"
        geography = "GB"
        source = self.tenant_id
        unit = "kg"
        exchanges = [
            Exchange(
                exchange_name="electricity, medium voltage",
                amount=12,
                unit="kWh",
                input_stream=True,
                exchange_emissions_factor=Activity(
                    activity_name="electricity, medium voltage, residual mix",
                    reference_product_name="electricity, medium voltage",
                    geography="GB",
                    product_information="",
                    source="Ecoinvent 3.11"
                )
            ),
            Exchange(
                exchange_name="water production, deionised",
                amount=3,
                unit="kWh",
                input_stream=True,
                exchange_emissions_factor=Activity(
                    activity_name="water production, deionised",
                    reference_product_name="water, deionised",
                    geography="RoW",
                    product_information="",
                    source="Ecoinvent 3.11"
                ),
            )
        ]

        lcia_method="EF v3.1"
        impact_category_name="climate change"
        impact_category_indicator="global warming potential (GWP100)"
        impact_category_unit="kg CO2-Eq"
        efv1_amount = 1.23

        # create a EmissionFactorValueRW
        elemental_efv1 = EmissionFactorValueRW(
            lcia_method=lcia_method,
            impact_category_name=impact_category_name,
            impact_category_indicator=impact_category_indicator,
            impact_category_unit=impact_category_unit,
            amount=efv1_amount
        )

        service = EmissionsFactorsService()
        service.create_tenant_emissions_factor(
            tenant_id=self.tenant_id,
            activity_name=activity_name,
            activity_type=activity_type,
            reference_product=reference_product,
            geography=geography,
            source=source,
            unit=unit,
            exchanges=exchanges,
            elemental_ef_values=[elemental_efv1]
        )

        tenant_emissions_factors = TenantEmissionsFactorRepo(self.tenant_id)
        created_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            activity_name=activity_name,
            geography=geography,
            reference_product=reference_product,
            source=source,
        )

        self.assertEqual(
            created_emissions_factor.activity_name,
            activity_name,
        )
        self.assertEqual(
            created_emissions_factor.geography,
            geography,
        )
        self.assertEqual(
            created_emissions_factor.reference_product,
            reference_product,
        )
        self.assertEqual(
            created_emissions_factor.source,
            source,
        )

        # Get the total for emission factor value including the exchanges
        impact_indicator_id = TenantImpactIndicatorRepo(self.tenant_id) \
            .get_impact_indicator(lcia_method, impact_category_name, impact_category_indicator).id

        tenant_ef_values = TenantEmissionsFactorValueRepo(self.tenant_id)
        ef_values = tenant_ef_values.get_emissions_factor_values(
            emissions_factor_id=created_emissions_factor.id,
            impact_indicator_ids=[impact_indicator_id]
        )

        self.assertEqual(len(ef_values), 1)
        self.assertGreater(ef_values[0].amount, efv1_amount)

        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(self.tenant_id)
        intermediate_exchanges_ = tenant_intermediate_exchanges.get_all_for_emissions_factor_by_id(
            created_emissions_factor.id
        )

        self.assertEqual(len(intermediate_exchanges_), len(exchanges))
        for exchange in exchanges:
            self.assertTrue(
                any(
                    [
                        ie.exchange_name == exchange.exchange_name
                        and ie.amount == exchange.amount
                        and ie.unit == exchange.unit
                        and ie.input_stream == exchange.input_stream
                        and ie.exchange_emissions_factor.activity_name
                        == exchange.exchange_emissions_factor.activity_name
                        and ie.exchange_emissions_factor.reference_product
                        == exchange.exchange_emissions_factor.reference_product_name
                        and ie.exchange_emissions_factor.geography
                        == exchange.exchange_emissions_factor.geography
                    ]
                    for ie in intermediate_exchanges_
                )
            )

        #Verify custom emissions factor is added to recommendations
        _activity_name = "gel silica"
        _geography = "CN"

        custom_emissions_factors = tenant_emissions_factors.get_custom_emissions_factors()
        service = EmissionsFactorsService()
        ef_match = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=_activity_name,
            geography=_geography,
            custom_emissions_factors=custom_emissions_factors,
            number_of_matches=25,
            carbon_only=True,
        )
        self.assertIsNotNone(ef_match)

        custom_ef_found = False
        for recommendation in ef_match.recommendations:
            if (recommendation.activity_name == activity_name and
                recommendation.reference_product_name == reference_product and
                recommendation.geography == geography and
                recommendation.source == self.tenant_id):
                custom_ef_found = True
                break

        self.assertTrue(
            custom_ef_found,
            "Custom emissions factor should be added in recommendations"
        )

    def _test_create_intermediate_exchange(self):
        exchange_name = self.exchange_data["exchange_name"]
        exchange_amount = self.exchange_data["exchange_amount"]
        exchange_unit = self.exchange_data["exchange_unit"]
        exchange_activity_name = self.exchange_data["exchange_activity_name"]
        exchange_reference_product = self.exchange_data["exchange_reference_product"]
        exchange_geography = self.exchange_data["exchange_geography"]
        exchange_source = self.exchange_data["exchange_source"]

        service = EmissionsFactorsService()
        created_ef, _ = service.copy_emissions_factor_to_tenant_emissions_factors(
            tenant_id=self.tenant_id,
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(self.tenant_id)
        created_intermediate_exchanges = (
            tenant_intermediate_exchanges
            .get_all_for_emissions_factor_by_id(
                created_ef.id,
            )
        )

        service.create_intermediate_exchange_emissions_factor(
            tenant_id=self.tenant_id,
            parent_emissions_factor_id=created_ef.id,
            exchange_name=exchange_name,
            exchange_amount=exchange_amount,
            exchange_unit=exchange_unit,
            activity_name=exchange_activity_name,
            reference_product=exchange_reference_product,
            geography=exchange_geography,
            source=exchange_source,
        )

        tenant_emissions_factors = TenantEmissionsFactorRepo(self.tenant_id)
        updated_ef = tenant_emissions_factors.get_emissions_factor(
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        updated_intermediate_exchanges = (
            tenant_intermediate_exchanges
            .get_all_for_emissions_factor_by_id(
                updated_ef.id,
            )
        )

        self.assertEqual(
            len(created_intermediate_exchanges) + 1,
            len(updated_intermediate_exchanges),
        )

        new_exchange = [
            ie for ie in updated_intermediate_exchanges
            if ie.exchange_name == exchange_name
            and ie.amount == exchange_amount
            and ie.unit == exchange_unit
            and ie.exchange_emissions_factor.activity_name == exchange_activity_name
            and ie.exchange_emissions_factor.reference_product == exchange_reference_product
            and ie.exchange_emissions_factor.geography == exchange_geography
            and ie.exchange_emissions_factor.source == exchange_source
        ][0]

        self.assertIsNotNone(new_exchange)

    def test_update_intermediate_exchange(self):
        service = EmissionsFactorsService()
        created_ef, _ = service.copy_emissions_factor_to_tenant_emissions_factors(
            tenant_id=self.tenant_id,
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        # Get initial impact values
        values_initial = TenantEmissionsFactorValueRepo(self.tenant_id).get_emissions_factor_values(
            created_ef.id
        )
        impact_initial = {
            (value.impact_indicator.lcia_method,
             value.impact_indicator.category,
             value.impact_indicator.indicator): value.amount
            for value in values_initial
        }

        # Create intermediate exchange
        exchange = service.create_intermediate_exchange_emissions_factor(
            tenant_id=self.tenant_id,
            parent_emissions_factor_id=created_ef.id,
            exchange_name=self.exchange_data["exchange_name"],
            exchange_amount=self.exchange_data["exchange_amount"],
            exchange_unit=self.exchange_data["exchange_unit"],
            activity_name=self.exchange_data["exchange_activity_name"],
            reference_product=self.exchange_data["exchange_reference_product"],
            geography=self.exchange_data["exchange_geography"],
            source=self.exchange_data["exchange_source"],
        )

        exchange_ef_values = (
            TenantEmissionsFactorValueRepo(self.tenant_id)
            .get_emissions_factor_values(
                exchange.exchange_emissions_factor.id
            )
        )

        # Get impact values after first exchange
        values_after_create_intermediate_exchange = (
            TenantEmissionsFactorValueRepo(self.tenant_id)
            .get_emissions_factor_values(
                created_ef.id
            )
        )
        impact_after_create_intermediate_exchange = {
            (value.impact_indicator.lcia_method,
             value.impact_indicator.category,
             value.impact_indicator.indicator): value.amount
            for value in values_after_create_intermediate_exchange
        }

        # Update exchange with new amount
        updated_exchange_amount = 3
        updated_exchange = service.update_intermediate_exchange_emissions_factor(
            tenant_id=self.tenant_id,
            exchange_id=exchange.id,
            exchange_name=self.exchange_data["exchange_name"],
            exchange_amount=updated_exchange_amount,
            exchange_unit=self.exchange_data["exchange_unit"],
            activity_name=self.exchange_data["exchange_activity_name"],
            reference_product=self.exchange_data["exchange_reference_product"],
            geography=self.exchange_data["exchange_geography"],
            source=self.exchange_data["exchange_source"],
        )

        # Verify exchange was updated
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(self.tenant_id)
        updated_exchange = tenant_intermediate_exchanges.get_by_id(updated_exchange.id)
        self.assertEqual(
            updated_exchange.amount,
            updated_exchange_amount,
        )

        # Get final impact values
        values_after_update_intermediate_exchange = (
            TenantEmissionsFactorValueRepo(self.tenant_id)
            .get_emissions_factor_values(
                created_ef.id
            )
        )
        impact_after_update_intermediate_exchange = {
            (value.impact_indicator.lcia_method,
             value.impact_indicator.category,
             value.impact_indicator.indicator): value.amount
            for value in values_after_update_intermediate_exchange
        }

        # Verify impact values increased by expected amounts
        for impact_key in impact_initial:
            # Get the exchange emission factor's impact value for this indicator

            exchange_value = next(
                value.amount
                for value in exchange_ef_values
                if (value.impact_indicator.lcia_method,
                    value.impact_indicator.category,
                    value.impact_indicator.indicator) == impact_key
            )

            # Calculate expected impacts
            expected_initial = impact_initial[impact_key]
            expected_after_create = (
                impact_initial[impact_key]
                + (self.exchange_data["exchange_amount"] * exchange_value)
            )
            expected_after_update = (
                impact_initial[impact_key]
                + (updated_exchange_amount * exchange_value)
            )

            # Assert values match expectations
            # Initial impact values should be the same as the created emissions factor
            self.assertAlmostEqual(
                impact_initial[impact_key],
                expected_initial,
                places=4,
                msg=f"Initial impact mismatch for {impact_key}"
            )

            self.assertAlmostEqual(
                impact_after_create_intermediate_exchange[impact_key],
                expected_after_create,
                places=4,
                msg=f"Impact mismatch after creating intermediate exchange for {impact_key}"
            )
            self.assertAlmostEqual(
                impact_after_update_intermediate_exchange[impact_key],
                expected_after_update,
                places=4,
                msg=f"Impact mismatch after updating intermediate exchange for {impact_key}"
            )

        tenant_emissions_factors = TenantEmissionsFactorRepo(self.tenant_id)
        updated_ef = tenant_emissions_factors.get_emissions_factor_by_id(
            emissions_factor_id=created_ef.id,
        )
        self.assertIsNotNone(updated_ef)

    def test_copy_emissions_factor_to_tenant_emissions_factors(self):
        expected_emissions_factor = emissions_factors.get_emissions_factor(
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        self.assertIsNotNone(expected_emissions_factor)

        service = EmissionsFactorsService()
        service.copy_emissions_factor_to_tenant_emissions_factors(
            tenant_id=self.tenant_id,
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        tenant_emissions_factors = TenantEmissionsFactorRepo(self.tenant_id)
        created_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            self.emissions_factor["activity_name"],
            geography=self.emissions_factor["geography"],
            reference_product=self.emissions_factor["reference_product"],
            source=self.emissions_factor["source"],
        )

        self.assertIsNotNone(created_emissions_factor)
        self.assertEqual(
            expected_emissions_factor.activity_name,
            created_emissions_factor.activity_name,
        )

        self.assertEqual(
            expected_emissions_factor.reference_product,
            created_emissions_factor.reference_product,
        )

        self.assertEqual(
            expected_emissions_factor.geography,
            created_emissions_factor.geography,
        )

        self.assertEqual(
            expected_emissions_factor.source,
            created_emissions_factor.source,
        )

        expected_intermediate_exchanges = intermediate_exchanges.get_all_for_emissions_factor(
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(self.tenant_id)
        tenant_ies = tenant_intermediate_exchanges.get_all_for_emissions_factor_by_id(
            created_emissions_factor.id,
        )
        self.assertEqual(len(expected_intermediate_exchanges), len(tenant_ies))

        # Compare the emission factor values
        expected_ef_values = emission_factor_value.get_emissions_factor_values(
            emissions_factor_id=expected_emissions_factor.id
        )
        tenant_ef_values = (
          TenantEmissionsFactorValueRepo(self.tenant_id)
          .get_emissions_factor_values(
              emissions_factor_id=created_emissions_factor.id
          )
        )
        self.assertEqual(len(expected_ef_values), len(tenant_ef_values))


    def _test_update_intermediate_exchange_emissions_factor(self):
        service = EmissionsFactorsService()
        service.copy_emissions_factor_to_tenant_emissions_factors(
            tenant_id=self.tenant_id,
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        tenant_emissions_factors = TenantEmissionsFactorRepo(self.tenant_id)
        created_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            self.emissions_factor["activity_name"],
            geography=self.emissions_factor["geography"],
            reference_product=self.emissions_factor["reference_product"],
            source=self.emissions_factor["source"],
        )

        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(self.tenant_id)
        tenant_ies = tenant_intermediate_exchanges.get_all_for_emissions_factor_by_id(
            created_emissions_factor.id,
        )

        exchange = [
            ie for ie in tenant_ies
            if ie.exchange_name == "electricity, medium voltage"
            and (
                ie.exchange_emissions_factor.activity_name
                == "market group for electricity, medium voltage"
            )
        ][0]

        new_exchange = service.update_intermediate_exchange_emissions_factor(
            self.tenant_id,
            exchange_id=exchange.id,
            activity_name=exchange.exchange_emissions_factor.activity_name,
            reference_product=exchange.exchange_emissions_factor.reference_product,
            geography="GLO",
            source=exchange.exchange_emissions_factor.source,
        )

        updated_exchange = tenant_intermediate_exchanges.get_by_id(new_exchange.id)
        self.assertEqual(updated_exchange.exchange_emissions_factor.geography, "GLO")

    def test_geography_modeling(self):
        """
        Test that geography modeling works correctly by:
        1. Getting a match for an emissions factor
        2. Verifying that electricity exchanges are updated with geography-specific values
        3. Verifying that non-electricity exchanges remain unchanged
        """
        ingredient_name = "methanol production, from coal gasification"
        geography = "NG"
        service = EmissionsFactorsService()

        # Test response without geography modeling
        non_geo_response = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=ingredient_name,
            geography=geography,
            number_of_matches=1,
            product_category=None,
            geography_modeling=False,
            valid_units=None,
        )

        self.assertIsNotNone(non_geo_response)
        self.assertIsNotNone(non_geo_response.matched_activity)
        non_geo_matched_activity = non_geo_response.matched_activity
        self.assertFalse(non_geo_matched_activity.modified)
        self.assertNotEqual(non_geo_matched_activity.geography, geography)
        self.assertNotIn(
            f"(customized for {geography})",
            non_geo_matched_activity.activity_name.lower()
        )
        self.assertIn(
            "methanol",
            non_geo_matched_activity.activity_name.lower()
        )

        # Test response with geography modeling
        geo_response = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=ingredient_name,
            geography=geography,
            number_of_matches=1,
            product_category=None,
            geography_modeling=True,
            valid_units=None,
        )

        self.assertIsNotNone(geo_response)
        self.assertIsNotNone(geo_response.matched_activity)
        matched_activity = geo_response.matched_activity
        self.assertTrue(matched_activity.modified)

        self.assertIn(
            f"(customized for {geography})",
            matched_activity.activity_name
        )
        self.assertIn(
            "methanol",
            matched_activity.activity_name.lower()
        )

        electricity_exchanges_non_geo = [
            exchange for exchange in non_geo_matched_activity.exchanges
            if "electricity" in exchange.exchange_name.lower()
        ]
        electricity_exchanges_geo = [
            exchange for exchange in matched_activity.exchanges
            if "electricity" in exchange.exchange_name.lower()
        ]

        self.assertLessEqual(
            len(electricity_exchanges_geo),
            len(electricity_exchanges_non_geo),
            "Geography modeling should result in fewer electricity exchanges"
        )

        # Get list of geographies for electricity exchanges
        geographies_for_electricity_exchanges = sorted([
            exchange.exchange_emissions_factor.geography
            for exchange in electricity_exchanges_geo
        ])

        # get list of geographies for electricity exchanges in non-geo response
        geographies_for_electricity_exchanges_non_geo = sorted([
            exchange.exchange_emissions_factor.geography
            for exchange in electricity_exchanges_non_geo
        ])

        self.assertNotEqual(
            geographies_for_electricity_exchanges,
            geographies_for_electricity_exchanges_non_geo,
            "Geography modeling should result in different geographies for electricity exchanges"
        )


    def test_geography_modeling_no_electricity(self):
        """Test that geography modeling works correctly when there are no electricity exchanges:
        1. Use a material name with a curated match for a carbonbright (co2 only) emissions factor
        2. Verify that the activity is not modified
        3. Verify that the geography is set to the EF geography
        """
        ingredient_name = "Silica Gel"  # Known to have no electricity exchanges
        geography = "US"
        service = EmissionsFactorsService()

        # Test response without geography modeling
        non_geo_response = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=ingredient_name,
            geography=geography,
            number_of_matches=1,
            product_category=None,
            geography_modeling=False,
            valid_units=None,
        )
        expected_geography = "RoW"
        self.assertIsNotNone(non_geo_response)
        self.assertIsNotNone(non_geo_response.matched_activity)
        non_geo_matched_activity = non_geo_response.matched_activity
        self.assertFalse(non_geo_matched_activity.modified)
        self.assertEqual(non_geo_matched_activity.geography, expected_geography)
        self.assertNotIn(
            f"(customized for {geography})",
            non_geo_matched_activity.activity_name.lower()
        )
        self.assertIn(
            "silica",
            non_geo_matched_activity.activity_name.lower()
        )
        # assert that the activity has no intermediate exchanges
        self.assertEqual(len(non_geo_matched_activity.exchanges), 0)


        # Test response with geography modeling
        geo_response = safe_async(
            service.get_emissions_factor_match,
            ingredient_name=ingredient_name,
            geography=geography,
            number_of_matches=1,
            product_category=None,
            geography_modeling=True,
            valid_units=None,
        )
        self.assertIsNotNone(geo_response)
        self.assertIsNotNone(geo_response.matched_activity)
        self.assertFalse(geo_response.matched_activity.modified)
        self.assertEqual(geo_response.matched_activity.geography, expected_geography)
        self.assertNotIn(
            f"(customized for {geography})",
            geo_response.matched_activity.activity_name
        )
        self.assertIn(
            "silica",
            geo_response.matched_activity.activity_name.lower()
        )
        # assert that the activity has no intermediate exchanges
        self.assertEqual(len(geo_response.matched_activity.exchanges), 0)

    def test_delete_intermediate_exchange(self):
        """Test that deleting an intermediate exchange correctly updates impact values"""
        # Setup initial emissions factor
        service = EmissionsFactorsService()
        created_ef, _ = service.copy_emissions_factor_to_tenant_emissions_factors(
            tenant_id=self.tenant_id,
            activity_name=self.emissions_factor["activity_name"],
            reference_product=self.emissions_factor["reference_product"],
            geography=self.emissions_factor["geography"],
            source=self.emissions_factor["source"],
        )

        tenant_ef_value_repo = TenantEmissionsFactorValueRepo(self.tenant_id)
        # Get initial impact values
        initial_values = tenant_ef_value_repo.get_emissions_factor_values(
            created_ef.id
        )
        initial_impact = {
            (value.impact_indicator.lcia_method,
             value.impact_indicator.category,
             value.impact_indicator.indicator): value.amount
            for value in initial_values
        }

        # Create intermediate exchange
        exchange = service.create_intermediate_exchange_emissions_factor(
            tenant_id=self.tenant_id,
            parent_emissions_factor_id=created_ef.id,
            exchange_name=self.exchange_data["exchange_name"],
            exchange_amount=self.exchange_data["exchange_amount"],
            exchange_unit=self.exchange_data["exchange_unit"],
            activity_name=self.exchange_data["exchange_activity_name"],
            reference_product=self.exchange_data["exchange_reference_product"],
            geography=self.exchange_data["exchange_geography"],
            source=self.exchange_data["exchange_source"],
        )

        # Get impact values after exchange creation
        with_exchange_values = tenant_ef_value_repo.get_emissions_factor_values(
            created_ef.id
        )
        with_exchange_impact = {
            (value.impact_indicator.lcia_method,
             value.impact_indicator.category,
             value.impact_indicator.indicator): value.amount
            for value in with_exchange_values
        }

        # Delete the exchange
        service.delete_intermediate_exchange(
            tenant_id=self.tenant_id,
            exchange_id=exchange.id
        )

        # Get final impact values
        final_values = tenant_ef_value_repo.get_emissions_factor_values(
            created_ef.id
        )
        final_impact = {
            (value.impact_indicator.lcia_method,
             value.impact_indicator.category,
             value.impact_indicator.indicator): value.amount
            for value in final_values
        }
        # Get the exchange emission factor's impact value for this indicator
        exchange_ef_values = tenant_ef_value_repo.get_emissions_factor_values(
            exchange.exchange_emissions_factor.id
        )
        # Verify impact values changed correctly
        for impact_key in initial_impact:

            exchange_value = next(
                value.amount
                for value in exchange_ef_values
                if (value.impact_indicator.lcia_method,
                    value.impact_indicator.category,
                    value.impact_indicator.indicator) == impact_key
            )

            # Calculate expected impacts
            expected_with_exchange = (
                initial_impact[impact_key]
                + (self.exchange_data["exchange_amount"] * exchange_value)
            )

            # Assert values match expectations
            self.assertAlmostEqual(
                with_exchange_impact[impact_key],
                expected_with_exchange,
                places=4,
                msg=f"Impact mismatch after exchange creation for {impact_key}"
            )

            # Calculate expected impacts after deleting the exchange
            expected_without_exchange = initial_impact[impact_key]

            # Assert values match expectations
            self.assertAlmostEqual(
                final_impact[impact_key],
                expected_without_exchange,
                places=4,
                msg=f"Impact mismatch after exchange deletion for {impact_key}"
            )
            # Verify that initial impact values are the same as final impact values
            self.assertAlmostEqual(
                initial_impact[impact_key],
                final_impact[impact_key],
                places=4
            )
        # Verify exchange was deleted
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(self.tenant_id)
        self.assertIsNone(tenant_intermediate_exchanges.get_by_id(exchange.id))
