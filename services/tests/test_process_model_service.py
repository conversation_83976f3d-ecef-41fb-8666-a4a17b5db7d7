# pylint: disable=protected-access
from unittest import TestCase
from unittest.mock import patch
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.constants.node_type import NodeTypes
from databases.shared.models import NodeType
from databases.tenant.models import Product, Node, Edge, TenantEmissionsFactor, Address
from databases.tenant.repository import TenantEmissionsFactorRepo
from services.emissions_factors_service import EmissionsFactorsService
from services.process_model_service import ProcessModelService
from cli import setup_server

def mock_get_by_name_factory(
    to_node_max_inputs: int,
    to_node_min_inputs: int,
    to_node_max_outputs: int,
    to_node_min_outputs: int,
    from_node_max_inputs: int,
    from_node_min_inputs: int,
    from_node_max_outputs: int,
    from_node_min_outputs: int,
    to_node_type: str,
    from_node_type: str,
):
    def get_by_name(name: str) -> NodeType | None:
        if name == to_node_type:
            return NodeType(
                id=1,
                name=to_node_type,
                max_inputs=to_node_max_inputs,
                min_inputs=to_node_min_inputs,
                max_outputs=to_node_max_outputs,
                min_outputs=to_node_min_outputs,
            )
        if name == from_node_type:
            return NodeType(
                id=2,
                name=from_node_type,
                max_inputs=from_node_max_inputs,
                min_inputs=from_node_min_inputs,
                max_outputs=from_node_max_outputs,
                min_outputs=from_node_min_outputs,
            )

        return None

    return get_by_name

emissions_factors_service = EmissionsFactorsService()

class TestProcessModelService(TestCase):
    tenant_id = "testtenant1"
    test_product = Product(
        product_id="1",
        product_name="Product",
        product_type="product",
        brand="Brand",
        country_of_use="United States",
        uses_per_package=1,
    )

    @classmethod
    def setUpClass(cls):
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.exec(
                f"DROP DATABASE IF EXISTS tenant_{cls.tenant_id}"
            )
            session.commit()

        setup_server(engine, tenant_ids=[cls.tenant_id])

        with Session(engine_pool.get_tenant_engine(cls.tenant_id)) as session:
            session.add(cls.test_product)
            session.commit()
            session.refresh(cls.test_product)

    def setUp(self):
        with Session(engine_pool.get_tenant_engine(self.tenant_id)) as session:
            session.exec(
                "DELETE FROM node;"
            )
            session.commit()

        self.nodes = [
            Node(
                id=1,
                product_id=self.test_product.product_id,
                name="Citric Acid",
                quantity=1,
                amount=1,
                unit="kg",
                node_type="material",
                emissions_factor=TenantEmissionsFactor(
                    activity_name="citric acid production",
                    reference_product="citric acid",
                    geography="RoW",
                    source="Ecoinvent 3.11",
                    reference_product_amount=1,
                    activity_type="",
                    unit="kg",
                    kg_co2e=0,
                    activity_description="",
                ),
                location=Address(
                    city="Chicago",
                    country="United States",
                )
            ),
            Node(
                id=2,
                product_id=self.test_product.product_id,
                name="Water",
                quantity=1,
                amount=1,
                unit="kg",
                node_type="material",
                emissions_factor=TenantEmissionsFactor(
                    activity_name="water production, deionised",
                    reference_product="water, deionised",
                    activity_type="",
                    geography="RoW",
                    source="Ecoinvent 3.11",
                ),
                location=Address(
                    city="Los Angeles",
                    country="United States",
                )
            ),
            Node(
                id=3,
                product_id=self.test_product.product_id,
                name="Citric Acid Transportation",
                quantity=1,
                node_type="transportation",
                location=Address(
                    city="Los Angeles",
                    country="United States",
                )
            ),
            Node(
                id=5,
                product_id=self.test_product.product_id,
                name="Product Production",
                quantity=1,
                node_type="production",
                emissions_factor=TenantEmissionsFactor(
                    activity_name="water production, deionised",
                    activity_type="",
                    reference_product="water, deionised",
                    geography="RoW",
                    source="Ecoinvent 3.11",
                ),
                location=Address(
                    city="Los Angeles",
                    country="United States",
                )
            ),
        ]
        self.edges = [
            Edge(
                product_id=self.test_product.product_id,
                from_node_id=1,
                to_node_id=3,
            ),
            Edge(
                product_id=self.test_product.product_id,
                from_node_id=3,
                to_node_id=5,
            ),
            Edge(
                product_id=self.test_product.product_id,
                from_node_id=2,
                to_node_id=5,
            ),
        ]

        tenant_emissions_factors = TenantEmissionsFactorRepo(self.tenant_id)
        for node in self.nodes:
            if node.emissions_factor:
                emissions_factor = tenant_emissions_factors.get_emissions_factor(
                    activity_name=node.emissions_factor.activity_name,
                    reference_product=node.emissions_factor.reference_product,
                    geography=node.emissions_factor.geography,
                    source=node.emissions_factor.source,
                )
                if not emissions_factor:
                    emissions_factor, _ = (
                        emissions_factors_service.copy_emissions_factor_to_tenant_emissions_factors(
                            self.tenant_id,
                            activity_name=node.emissions_factor.activity_name,
                            reference_product=node.emissions_factor.reference_product,
                            geography=node.emissions_factor.geography,
                            source=node.emissions_factor.source,
                        )
                    )
                emissions_factor_id = emissions_factor.id
            else:
                emissions_factor_id = None

            with Session(engine_pool.get_tenant_engine(self.tenant_id)) as session:
                location = Address(**node.location.dict())
                session.add(location)
                session.flush()

                node = Node(
                    **node.dict(
                        exclude={
                            "emissions_factor",
                            "location",
                            "location_id",
                            "emissions_factor_id",
                        }
                    ),
                    location_id=location.id,
                    emissions_factor_id=emissions_factor_id
                )

                session.add(node)
                session.commit()
                session.refresh(node)

        for edge in self.edges:
            with Session(engine_pool.get_tenant_engine(self.tenant_id)) as session:
                session.add(edge)
                session.commit()
                session.refresh(edge)

    @classmethod
    def tearDownClass(cls):
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.exec(
                f"DROP DATABASE IF EXISTS tenant_{cls.tenant_id}"
            )
            session.commit()

    def test_get_process_model(self):
        process_model_service = ProcessModelService(self.tenant_id)
        nodes, edges = (
            process_model_service
            .get_process_model_for_product(self.test_product.product_id)
        )
        self.assertEqual(len(nodes), len(self.nodes))
        self.assertEqual(len(edges), len(self.edges))

        for node in nodes:
            self.assertTrue(
                any(
                    node
                    for node_ in self.nodes
                    if node.id == node_.id
                    and node.product_id == node_.product_id
                    and node.name == node_.name
                    and node.amount == node_.amount
                    and node.unit == node_.unit
                    and node.node_type == node_.node_type
                    and node.location.city == node_.location.city
                    and node.location.country == node_.location.country
                )
            )

        for edge in edges:
            self.assertTrue(
                any(
                    edge_
                    for edge_ in self.edges
                    if edge.from_node_id == edge_.from_node_id
                    and edge.to_node_id == edge_.to_node_id
                    and edge.product_id == edge_.product_id
                )
            )

    def test_delete_process_model(self):
        process_model_service = ProcessModelService(self.tenant_id)
        process_model_service.delete_process_model_for_product(self.test_product.product_id)
        nodes, edges = (
            process_model_service.get_process_model_for_product(self.test_product.product_id)
        )
        self.assertEqual(len(nodes), 0)
        self.assertEqual(len(edges), 0)

    @patch("services.process_model_service.node_types.get_by_name")
    def test_validate_create_edge(self, mock_get_by_name):
        process_model_service = ProcessModelService(self.tenant_id)
        eol_node = Node(
            id=6,
            product_id=self.test_product.product_id,
            quantity=1,
            name="EOL",
            node_type=NodeTypes.EOL.value,
            location=Address(
                city="Chicago",
                country="United States",
            )
        )

        # Test valid edge
        mock_get_by_name.side_effect = mock_get_by_name_factory(
            to_node_max_inputs=1,
            to_node_min_inputs=1,
            to_node_max_outputs=0,
            to_node_min_outputs=0,
            from_node_max_inputs=None,
            from_node_min_inputs=1,
            from_node_max_outputs=1,
            from_node_min_outputs=0,
            to_node_type=NodeTypes.EOL.value,
            from_node_type=NodeTypes.PRODUCTION.value,
        )

        try:
            process_model_service._validate_adding_edge(self.nodes[-1], eol_node)
        except ValueError as error:
            self.fail(error)

        # Test invalid edge (max outgoing inputs)
        mock_get_by_name.side_effect = mock_get_by_name_factory(
            to_node_max_inputs=1,
            to_node_min_inputs=1,
            to_node_max_outputs=0,
            to_node_min_outputs=0,
            from_node_max_inputs=None,
            from_node_min_inputs=1,
            from_node_max_outputs=0,
            from_node_min_outputs=0,
            to_node_type=NodeTypes.EOL.value,
            from_node_type=NodeTypes.PRODUCTION.value,
        )
        with self.assertRaises(ValueError) as context:
            process_model_service._validate_adding_edge(self.nodes[-1], eol_node)

        self.assertEqual(
            str(context.exception),
            (
                "Invalid number of outgoing inputs for from node "
                "for node-type production. Max outputs: 0, current: 0"
            )
        )

        # Test invalid edge (max incoming inputs)
        mock_get_by_name.side_effect = mock_get_by_name_factory(
            to_node_max_inputs=0,
            to_node_min_inputs=0,
            to_node_max_outputs=0,
            to_node_min_outputs=0,
            from_node_max_inputs=None,
            from_node_min_inputs=1,
            from_node_max_outputs=1,
            from_node_min_outputs=0,
            to_node_type=NodeTypes.EOL.value,
            from_node_type=NodeTypes.PRODUCTION.value,
        )
        with self.assertRaises(ValueError) as context:
            process_model_service._validate_adding_edge(self.nodes[-1], eol_node)

        self.assertEqual(
            str(context.exception),
            (
                "Invalid number of incoming inputs for to node "
                "for node-type eol. Max inputs: 0, current: 0"
            )
        )

    @patch("services.process_model_service.node_types.get_by_name")
    def test_validate_deleting_edge(self, mock_get_by_name):
        process_model_service = ProcessModelService(self.tenant_id)

        # Test deleting an edge which results in an invalid state
        mock_get_by_name.side_effect = mock_get_by_name_factory(
            to_node_max_inputs=1,
            to_node_min_inputs=1,
            to_node_max_outputs=0,
            to_node_min_outputs=0,
            from_node_max_inputs=None,
            from_node_min_inputs=1,
            from_node_max_outputs=1,
            from_node_min_outputs=1,
            to_node_type=NodeTypes.PRODUCTION.value,
            from_node_type=NodeTypes.TRANSPORTATION.value,
        )

        with self.assertRaises(ValueError) as context:
            process_model_service._validate_deleting_edge(self.nodes[-2], self.nodes[-1])

        self.assertEqual(
            str(context.exception),
            (
                "Invalid number of outgoing inputs for from node "
                "for node-type transportation. Min outputs: 1, current: 1"
            )
        )

        # Test deleting an edge which results in a valid state
        mock_get_by_name.side_effect = mock_get_by_name_factory(
            to_node_max_inputs=0,
            to_node_min_inputs=0,
            to_node_max_outputs=0,
            to_node_min_outputs=0,
            from_node_max_inputs=None,
            from_node_min_inputs=1,
            from_node_max_outputs=1,
            from_node_min_outputs=0,
            to_node_type=NodeTypes.PRODUCTION.value,
            from_node_type=NodeTypes.TRANSPORTATION.value,
        )
        try:
            process_model_service._validate_deleting_edge(self.nodes[-2], self.nodes[-1])
        except ValueError as error:
            self.fail(error)
