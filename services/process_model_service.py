from typing import <PERSON><PERSON>
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.models import <PERSON><PERSON>, NodeRead, Edge, Address
from databases.shared.repository import node_types
from databases.tenant.repository import (
    NodeRepo,
    EdgeRepo,
)


class ProcessModelService:
    def __init__(self, tenant_id: str):
        self.nodes = NodeRepo(tenant_id)
        self.edges = EdgeRepo(tenant_id)

    def get_process_model_for_product(
        self,
        product_id: int,
    ) -> <PERSON><PERSON>[list[NodeRead], list[Edge]]:
        """Get all nodes and edges for a product."""
        nodes = self.nodes.get_all_by_product_id(product_id)
        edges = self.edges.get_all_by_product_id(product_id)

        return nodes, edges

    def delete_process_model_for_product(
        self,
        product_id: int,
    ) -> <PERSON><PERSON>[list[NodeRead], list[Edge]]:
        """Delete all nodes and edges for a product."""
        nodes = self.nodes.get_all_by_product_id(product_id)
        edges = self.edges.get_all_by_product_id(product_id)

        for node in nodes:
            self.nodes.delete(node.id)

        return nodes, edges

    def get_node_by_name(
        self,
        product_id: str,
        name: str,
    ) -> NodeRead:
        return self.nodes.get_node_by_name(product_id, name)

    def create_node(
        self,
        node: Node,
        address: Address | None = None,
    ) -> Node:
        created_node = self.nodes.create(node, address)

        return created_node

    def create_edge(
        self,
        from_node_id: int,
        to_node_id: int,
    ) -> Edge:
        """Create an edge from a node to another node."""
        from_node = self.nodes.get_node(from_node_id)
        to_node = self.nodes.get_node(to_node_id)

        self._validate_adding_edge(from_node, to_node)

        edge = Edge(
            product_id=from_node.product_id,
            from_node_id=from_node_id,
            to_node_id=to_node_id,
        )
        created_edge = self.edges.create(edge)

        return created_edge

    def delete_node(self, node_id: int) -> Node:
        """Delete a node and reconnect incoming and outgoing edges."""
        node = self.nodes.get_node(node_id)
        incoming_edges = self.edges.get_all_to_nodes(node_id)

        if not incoming_edges:
            raise ValueError("Node has no incoming edges.")

        for edge in incoming_edges:
            self._validate_deleting_edge(edge.from_node, edge.to_node)

        outgoing_edges = self.edges.get_all_from_nodes(node_id)
        for edge in outgoing_edges:
            self._validate_deleting_edge(edge.from_node, edge.to_node)

        self._reconnect_edges(incoming_edges, outgoing_edges)

        node = self.nodes.delete(node_id)

        return node

    def _reconnect_edges(self, incoming_edges: list[Edge], outgoing_edges: list[Edge]) -> None:
        """Reconnect incoming and outgoing edges."""
        for incoming_edge in incoming_edges:
            for outgoing_edge in outgoing_edges:
                self.create_edge(incoming_edge.from_node_id, outgoing_edge.to_node_id)

    def _validate_deleting_edge(self, from_node: Node, to_node: Node) -> None:
        """Validate edge connection between two nodes when deleting an edge."""
        if from_node.product_id != to_node.product_id:
            raise ValueError("Nodes must belong to the same product.")

        from_node_type = node_types.get_by_name(from_node.node_type)
        to_node_type = node_types.get_by_name(to_node.node_type)

        from_node_outgoing_edges = len(self.edges.get_all_from_nodes(from_node.id))
        to_node_incoming_edges = len(self.edges.get_all_to_nodes(to_node.id))

        if from_node_type.min_outputs is not None:
            if from_node_outgoing_edges <= from_node_type.min_outputs:
                raise ValueError(
                    "Invalid number of outgoing inputs for from node "
                    f"for node-type {from_node_type.name}. "
                    f"Min outputs: {from_node_type.min_outputs}, "
                    f"current: {from_node_outgoing_edges}"
                )

        if to_node_type.min_inputs is not None:
            if to_node_incoming_edges <= to_node_type.min_inputs:
                raise ValueError(
                    "Invalid number of incoming inputs for to node "
                    f"for node-type {to_node_type.name}. "
                    f"Min inputs: {to_node_type.min_inputs}, "
                    f"current: {to_node_incoming_edges}"
                )

    def _validate_adding_edge(self, from_node: Node, to_node: Node) -> None:
        """Validate edge connection between two nodes when adding an edge."""
        if from_node.product_id != to_node.product_id:
            raise ValueError("Nodes must belong to the same product.")

        from_node_type_name = (
            NodeTypes.MATERIAL.value
            if from_node.node_type == NodeTypes.PACKAGING
            else from_node.node_type
        )
        to_node_type_name = (
            NodeTypes.MATERIAL.value
            if to_node.node_type == NodeTypes.PACKAGING
            else to_node.node_type
        )

        from_node_type = node_types.get_by_name(from_node_type_name)
        to_node_type = node_types.get_by_name(to_node_type_name)

        from_node_outgoing_edges = len(self.edges.get_all_from_nodes(from_node.id))
        to_node_incoming_edges = len(self.edges.get_all_to_nodes(to_node.id))

        if from_node_type.max_outputs is not None:
            if from_node_outgoing_edges >= from_node_type.max_outputs:
                raise ValueError(
                    "Invalid number of outgoing inputs for from node "
                    f"for node-type {from_node_type.name}. "
                    f"Max outputs: {from_node_type.max_outputs}, "
                    f"current: {from_node_outgoing_edges}"
                )

        if to_node_type.max_inputs is not None:
            if to_node_incoming_edges >= to_node_type.max_inputs:
                raise ValueError(
                    "Invalid number of incoming inputs for to node "
                    f"for node-type {to_node_type.name}. "
                    f"Max inputs: {to_node_type.max_inputs}, "
                    f"current: {to_node_incoming_edges}"
                )

    def get_node(self, node_id: int) -> NodeRead:
        """Get a node by its ID."""
        return self.nodes.get_node(node_id)

    def update_node(self, node: Node) -> NodeRead:
        """Update a node with the provided data.
        Args:
            node: The node to update
            
        Returns:
            The updated node
        """
        existing_node = self.nodes.get_node(node.id)
        if not existing_node:
            raise ValueError(f"Node with ID {node.id} not found")

        updated_node = self.nodes.update(node.id, node)

        return updated_node
