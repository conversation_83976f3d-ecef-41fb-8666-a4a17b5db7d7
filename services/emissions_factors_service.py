from typing import List, <PERSON><PERSON>, Literal
import asyncio
from enum import Enum
from pydantic import BaseModel
from fastapi import HTTPException
from databases.shared.repository.emissions_factors import emissions_factors
from databases.shared.repository.emission_factor_value import emission_factor_value
from databases.shared.repository.intermediate_exchange import intermediate_exchanges

from databases.shared.models import (
    IntermediateExchangeRead,
)

from databases.tenant.models.tenant_emissions_factor_value import TenantEmissionsFactorValueRead
from databases.tenant.repository import (
    TenantEmissionsFactorRepo,
    TenantIntermediateExchangeRepo,
)
from databases.tenant.models import (
    TenantEmissionsFactor,
    TenantIntermediateExchange,
    TenantIntermediateExchangeRead,
)
from databases.tenant.repository.tenant_emissions_factor import ImpactValueInputs
from databases.tenant.repository.tenant_emissions_factor_value import TenantEmissionsFactorValueRepo
from databases.shared.models.emissions_factor_value import EmissionFactorValueRW

from clients.ml_models import (
    MLModels,
    Activity,
    MLModelsRequestException,
)
from utils.geocoding import get_country_info
from utils.logger import logger
from exceptions import DatabaseError



class Exchange(BaseModel):
    exchange_name: str
    amount: float
    unit: str
    input_stream: bool
    exchange_emissions_factor: Activity


class ActivityWithExchanges(BaseModel):
    activity_name: str
    reference_product_name: str
    product_information: str
    source: str
    unit: str | None = None
    geography: str | None = None
    curated: bool | None = None
    exchanges: List[Exchange]
    modified: bool
    elemental_ef_values: List[EmissionFactorValueRW] = []


class ActivityResponse(BaseModel):
    matched_activity: ActivityWithExchanges
    recommendations: List[Activity]
    explanation: str
    confidence: Literal["low", "medium", "high"]

class MatchType(str, Enum):
    CURATED = "curated"
    RECOMMENDATION = "recommendation"
    OVERRIDE = "override"

class EmissionsFactorsService:
    def __init__(self) -> None:
        self.client = MLModels()

    async def get_emissions_factor_match(
        self,
        ingredient_name: str,
        geography: str,
        number_of_matches: int,
        product_category: str | None = None,
        geography_modeling: bool = False,
        valid_units: List[str] | None = None,
        impact_factors: List[str] | None = None,
        emissions_factor_override: TenantEmissionsFactor | None = None,
        custom_emissions_factors: List[TenantEmissionsFactor] | None = None,
        carbon_only: bool = False,
        api_version: str = "latest",
    ) -> ActivityResponse:
        try:
            country_info = get_country_info(geography)
            geography = country_info.alpha_2_code
        except Exception:
            geography = "GLO"

        number_of_matches = number_of_matches or 25

        try:
            if impact_factors:
                lcia_method = impact_factors[0]["lcia_method"]
            else:
                lcia_method = None

            activity_recommendations = (
                await self.client.predict_activity_recommendations(
                    chemical_name=ingredient_name,
                    product_category=product_category,
                    geography=geography,
                    number_of_matches=number_of_matches,
                    valid_units=valid_units,
                    version=api_version,
                    lcia_method=lcia_method,
                    carbon_only=carbon_only,
                )
            )

            if emissions_factor_override:
                matched_activity = Activity(
                    activity_name=emissions_factor_override.activity_name,
                    reference_product_name=emissions_factor_override.reference_product,
                    product_information="",
                    geography=emissions_factor_override.geography,
                    source=emissions_factor_override.source,
                )
                match_type = MatchType.OVERRIDE.value

                matched_emissions_factor = emissions_factor_override
            else:
                curated_match = self._get_curated_match(ingredient_name, carbon_only)
                match_type = (
                    MatchType.CURATED.value
                    if curated_match
                    else MatchType.RECOMMENDATION.value
                )
                matched_activity = (
                    curated_match or activity_recommendations.matched_activity
                )

                matched_emissions_factor = emissions_factors.get_emissions_factor(
                    matched_activity.activity_name,
                    matched_activity.geography,
                    matched_activity.reference_product_name,
                    matched_activity.source,
                )

            exchanges = intermediate_exchanges.get_all_for_emissions_factor(
                activity_name=matched_activity.activity_name,
                reference_product=matched_activity.reference_product_name,
                geography=matched_activity.geography,
                source=matched_activity.source,
            )

            if (
                geography_modeling and
                matched_activity.geography != geography and
                not emissions_factor_override
            ):
                matched_activity_with_exchanges = await self._get_custom_activity_for_geography(
                    geography,
                    matched_activity,
                    exchanges,
                )
            else:
                matched_activity_with_exchanges = ActivityWithExchanges(
                    **matched_activity.dict(),
                    unit=matched_emissions_factor.unit,
                    exchanges=[
                        self._convert_to_exchange(exchange)
                        for exchange in exchanges
                    ],
                    modified=False
                )

            matched_activity_with_exchanges.curated = match_type == MatchType.CURATED.value

            explanation = None
            confidence = None
            if match_type == MatchType.CURATED.value:
                explanation = "This is a curated match by CarbonBright."
                confidence = "high"
            elif match_type == MatchType.OVERRIDE.value:
                explanation = "This is a preferred match."
                confidence = "high"
            else:
                explanation = activity_recommendations.explanation
                confidence = activity_recommendations.confidence

            if custom_emissions_factors:
                activity_recommendations.recommendations.extend(
                    [
                        Activity(
                            activity_name=custom_emissions_factor.activity_name,
                            reference_product_name=custom_emissions_factor.reference_product,
                            product_information=custom_emissions_factor.activity_description,
                            source=custom_emissions_factor.source,
                            geography=custom_emissions_factor.geography,
                        )
                        for custom_emissions_factor in custom_emissions_factors
                    ]
                )

            return ActivityResponse(
                matched_activity=matched_activity_with_exchanges,
                recommendations=activity_recommendations.recommendations,
                confidence=confidence,
                explanation=explanation,
            )

        except MLModelsRequestException as ml_models_error:
            raise ml_models_error

        except Exception as error:
            logger.exception(error)
            raise Exception(f"Error predicting emissions factor: {error}") from error

    def _get_curated_match(self, material_name: str, carbon_only: bool) -> Activity | None:
        try:
            curated_emissions_factor = emissions_factors.get_curated_emissions_factor(
                raw_material=material_name,
            )
            if not curated_emissions_factor:
                return None

            if not carbon_only:
                if not curated_emissions_factor.source.startswith("Ecoinvent"):
                    return None

            return Activity(
                activity_name=curated_emissions_factor.activity_name,
                reference_product_name=curated_emissions_factor.reference_product,
                product_information=curated_emissions_factor.activity_description,
                source=curated_emissions_factor.source,
                geography=curated_emissions_factor.geography,
                curated=True
            )

        except Exception:
            logger.exception(
                f"Error getting curated emissions factor for '{material_name}'"
            )
            return None

    async def _get_custom_activity_for_geography(
        self,
        geography: str,
        activity: Activity,
        exchanges: List[IntermediateExchangeRead],
    ) -> ActivityWithExchanges:
        """
        Creates a customized activity for a specific geography by calculating impact values
        for each LCIA method, category, and indicator combination. The method:
        1. Gets the base emissions factor and its impact values
        2. For each impact value:
            - Calculates the total impact from exchanges
            - Determines the elemental exchange value by subtracting the
                total impact from exchanges from the base impact value
            - Updates the electricity related exchanges with electricity exchange values
                corresponding to the given geography
        3. Returns an ActivityWithExchanges with the calculated impact indicators
            and updated exchanges
        """
        activity_emissions_factor = emissions_factors.get_emissions_factor(
            activity.activity_name,
            activity.geography,
            activity.reference_product_name,
            activity.source,
        )
        exchange_to_ef_values = []
        for exchange in exchanges:
            exchange_to_ef_values.append(
                (
                    exchange,
                    emission_factor_value.get_emissions_factor_values(
                        exchange.exchange_emissions_factor.id
                    )
                )
            )


        # Get all total impact values for current emissions factor
        values = emission_factor_value.get_emissions_factor_values(
                    activity_emissions_factor.id
                ) or []

        elemental_ef_values = self._calculate_elemental_ef_values(values, exchange_to_ef_values)

        geo_exchanges, modified = await self._get_geo_custom_exchanges(geography, exchanges)

        if modified:
            activity_name = (
                f"{activity.activity_name} (customized for {geography})"
            )
        else:
            activity_name = activity.activity_name

        return ActivityWithExchanges(
            **activity.dict(exclude={"activity_name": True}),
            activity_name=activity_name,
            unit=activity_emissions_factor.unit,
            exchanges=geo_exchanges,
            modified=modified,
            elemental_ef_values=elemental_ef_values
        )

    def _calculate_elemental_ef_values(
        self,
        values: List[EmissionFactorValueRW],
        exchange_to_ef_values: List[Tuple[IntermediateExchangeRead, List[EmissionFactorValueRW]]]
    ) -> List[EmissionFactorValueRW]:
        elemental_ef_values = []
        for value in values:
            exchange_impact = self._get_total_intermediate_impact(value, exchange_to_ef_values)

            # Calculate elemental exchange value
            elemental_exchange_value = value.amount - exchange_impact

            # add to list of EmissionFactorValueRW
            elemental_ef_values.append(
                EmissionFactorValueRW(
                    lcia_method=value.lcia_method,
                    impact_category_name=value.impact_category_name,
                    impact_category_indicator=value.impact_category_indicator,
                    impact_category_unit=value.impact_category_unit,
                    amount=elemental_exchange_value
                )
            )
        return elemental_ef_values


    def create_tenant_emissions_factor(
        self,
        tenant_id: str,
        activity_name: str,
        activity_type: str,
        reference_product: str,
        geography: str,
        source: str,
        unit: str,
        exchanges: List[Exchange],
        elemental_ef_values: List[EmissionFactorValueRW],
        activity_description: str | None = None,
    ) -> Tuple[TenantEmissionsFactor, List[TenantIntermediateExchangeRead]]:
        tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)

        parent_emissions_factor = tenant_emissions_factors.add_emissions_factor(
            activity_name=activity_name,
            activity_type=activity_type,
            reference_product=reference_product,
            geography=geography,
            source=source,
            unit=unit,
            activity_description=activity_description,
        )


        tenant_exchanges = []
        exchange_to_ef_values = []
        for exchange in exchanges:
            exchange_emissions_factor = tenant_emissions_factors.get_emissions_factor(
                exchange.exchange_emissions_factor.activity_name,
                exchange.exchange_emissions_factor.geography,
                exchange.exchange_emissions_factor.reference_product_name,
                exchange.exchange_emissions_factor.source,
            )

            if not exchange_emissions_factor:
                exchange_emissions_factor, _ = (
                    self.copy_emissions_factor_to_tenant_emissions_factors(
                        tenant_id,
                        exchange.exchange_emissions_factor.activity_name,
                        exchange.exchange_emissions_factor.reference_product_name,
                        exchange.exchange_emissions_factor.geography,
                        exchange.exchange_emissions_factor.source,
                    )
                )

            tenant_exchange = TenantIntermediateExchange(
                **exchange.dict(
                    exclude={
                        "exchange_emissions_factor": True,
                    }
                ),
                parent_emissions_factor_id=parent_emissions_factor.id,
                exchange_emissions_factor_id=exchange_emissions_factor.id,
            )
            tenant_exchanges.append(tenant_exchange)
            exchange_to_ef_values.append(
                (exchange,
                TenantEmissionsFactorValueRepo(tenant_id).get_emissions_factor_values(
                    exchange_emissions_factor.id
                )
                )
            )
        exchanges = tenant_intermediate_exchanges.add_exchanges(tenant_exchanges)

        self._calculate_and_update_impact_values(
            tenant_emissions_factors,
            parent_emissions_factor.id,
            elemental_ef_values,
            exchange_to_ef_values
        )

        return parent_emissions_factor, exchanges

    def _calculate_and_update_impact_values(
        self,
        tenant_emissions_factors: TenantEmissionsFactorRepo,
        parent_emissions_factor_id: int,
        elemental_ef_values: List[EmissionFactorValueRW],
        exchange_to_ef_values: List[Tuple[IntermediateExchangeRead, List[EmissionFactorValueRW]]]
    ) -> None:
        """
        Calculate and update impact values for an emissions factor based on its
          elemental and intermediate values.

        Args:
            tenant_emissions_factors: Repository for tenant emissions factors
            parent_emissions_factor_id: ID of the emissions factor to update impact values for
            elemental_ef_values: List of elemental emission factor values
            exchange_to_ef_values: List of tuples containing exchanges and their
            emission factor values
        """
        impact_values_to_add = []
        emissions_factor_value_rw = (
            self._convert_ex_value_to_emission_factor_value_rw(exchange_to_ef_values)
        )
        if elemental_ef_values:
            # If we have elemental EF values, calculate total impacts including
            # both elemental and intermediate
            for elemental_ef_value in elemental_ef_values:
                total_intermediate_impact = self._get_total_intermediate_impact(
                    elemental_ef_value,
                    emissions_factor_value_rw
                )
                total_impact_value = elemental_ef_value.amount + total_intermediate_impact

                impact_values_to_add.append(
                    ImpactValueInputs(
                        ef_value=elemental_ef_value,
                        total_impact_value=total_impact_value,
                        emissions_factor_id=parent_emissions_factor_id
                    )
                )
        else:
            # If no elemental EF values, use just the intermediate impacts from exchanges
            # Get unique LCIA methods and categories from exchange values
            unique_impact_categories = set()
            for _, ef_values in emissions_factor_value_rw:
                for ef_value in ef_values:
                    unique_impact_categories.add((
                        ef_value.lcia_method,
                        ef_value.impact_category_name,
                        ef_value.impact_category_indicator,
                        ef_value.impact_category_unit
                    ))

            # Calculate total impact for each unique category
            for lcia_method, category_name, category_indicator, category_unit \
                in unique_impact_categories:
                dummy_ef_value = EmissionFactorValueRW(
                    lcia_method=lcia_method,
                    impact_category_name=category_name,
                    impact_category_indicator=category_indicator,
                    impact_category_unit=category_unit,
                    amount=0  # Amount is 0 since we only want intermediate impacts
                )
                total_intermediate_impact = self._get_total_intermediate_impact(
                    dummy_ef_value,
                    emissions_factor_value_rw
                )
                impact_values_to_add.append(
                    ImpactValueInputs(
                        ef_value=dummy_ef_value,
                        total_impact_value=total_intermediate_impact,
                        emissions_factor_id=parent_emissions_factor_id
                    )
                )

        tenant_emissions_factors.bulk_replace_ef_values(
            parent_emissions_factor_id,
            impact_values_to_add
        )

    def copy_emissions_factor_to_tenant_emissions_factors(
        self,
        tenant_id: str,
        activity_name: str,
        reference_product: str,
        geography: str,
        source: str,
        new_name: str | None = None,
    ) -> Tuple[TenantEmissionsFactor, List[TenantIntermediateExchangeRead]]:
        tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)
        existing_emissions_factor, exchanges = self.get_existing_emissions_factor_exchanges(
            tenant_emissions_factors,
            tenant_intermediate_exchanges,
            activity_name,
            geography,
            reference_product,
            source,
        )
        if existing_emissions_factor:
            return existing_emissions_factor, exchanges

        emissions_factor_ = emissions_factors.get_emissions_factor(
            activity_name,
            geography,
            reference_product,
            source,
        )

        try:
            parent_emissions_factor = tenant_emissions_factors.copy_shared_emissions_factor(
                emissions_factor_, new_name
            )
        except DatabaseError as error:
            existing_emissions_factor, exchanges = self.get_existing_emissions_factor_exchanges(
                tenant_emissions_factors,
                tenant_intermediate_exchanges,
                activity_name,
                geography,
                reference_product,
                source,
            )
            if existing_emissions_factor:
                return existing_emissions_factor, exchanges

            raise error

        shared_exchanges = intermediate_exchanges.get_all_for_emissions_factor(
            activity_name=activity_name,
            reference_product=reference_product,
            geography=geography,
            source=source,
        )

        tenant_exchanges = []
        # Copy shared exchanges to tenant exchanges
        for exchange in shared_exchanges:
            tenant_exchange_ef = tenant_emissions_factors.get_emissions_factor(
                activity_name=exchange.exchange_emissions_factor.activity_name,
                reference_product=exchange.exchange_emissions_factor.reference_product,
                geography=exchange.exchange_emissions_factor.geography,
                source=exchange.exchange_emissions_factor.source,
            )
            if not tenant_exchange_ef:
                exchange_ef = emissions_factors.get_emissions_factor(
                    activity_name=exchange.exchange_emissions_factor.activity_name,
                    geography=exchange.exchange_emissions_factor.geography,
                    reference_product=exchange.exchange_emissions_factor.reference_product,
                    source=exchange.exchange_emissions_factor.source,
                )

                tenant_exchange_ef = (
                    tenant_emissions_factors.copy_shared_emissions_factor(exchange_ef)
                )


            tenant_exchange = TenantIntermediateExchange(
                **exchange.dict(
                    exclude={
                        "id": True,
                        "parent_emissions_factor_id": True,
                        "exchange_emissions_factor_id": True,
                    }
                ),
                parent_emissions_factor_id=parent_emissions_factor.id,
                exchange_emissions_factor_id=tenant_exchange_ef.id,
            )
            tenant_exchanges.append(tenant_exchange)
        tenant_exchanges = tenant_intermediate_exchanges.add_exchanges(tenant_exchanges)

        return parent_emissions_factor, tenant_exchanges

    def create_intermediate_exchange_emissions_factor(
        self,
        tenant_id: str,
        parent_emissions_factor_id: int,
        exchange_name: str,
        exchange_amount: float,
        exchange_unit: str,
        activity_name: str,
        reference_product: str,
        geography: str,
        source: str,
    ) -> TenantIntermediateExchangeRead:
        tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)

        # Check if the emissions factor already exists, if not copy it from shared
        existing_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            activity_name,
            geography,
            reference_product,
            source,
        )

        if existing_emissions_factor is None:
            exchange_ef = emissions_factors.get_emissions_factor(
                activity_name,
                geography,
                reference_product,
                source,
            )
            new_emissions_factor = tenant_emissions_factors.copy_shared_emissions_factor(
                exchange_ef,
            )
        else:
            new_emissions_factor = existing_emissions_factor

        new_exchange = TenantIntermediateExchange(
            exchange_name=exchange_name,
            amount=exchange_amount,
            input_stream=exchange_amount >= 0,
            unit=exchange_unit,
            parent_emissions_factor_id=parent_emissions_factor_id,
            exchange_emissions_factor_id=new_emissions_factor.id,
        )

        # Calculate elemental EF values for the parent emissions factor
        # before adding new intermediate exchange
        elemental_ef_values = self._get_elemental_efs(
            tenant_id,
            parent_emissions_factor_id
        )

        exchange = tenant_intermediate_exchanges.add_exchanges([new_exchange])[0]

        # Get all exchanges for the parent emissions factor to recalculate impacts
        exchange_to_ef_values = self._get_exchange_ef_values(
            tenant_id,
            parent_emissions_factor_id
        )

        # Recalculate impact values
        self._calculate_and_update_impact_values(
            tenant_emissions_factors,
            parent_emissions_factor_id,
            elemental_ef_values,
            exchange_to_ef_values
        )

        return exchange

    def update_intermediate_exchange_emissions_factor(
        self,
        tenant_id: str,
        exchange_id: int,
        activity_name: str,
        reference_product: str,
        geography: str,
        source: str,
        new_name: str | None = None,
        exchange_name: str | None = None,
        exchange_amount: float | None = None,
        exchange_unit: str | None = None,
    ) -> TenantIntermediateExchangeRead:
        tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)

        # Since this is an existing exchange, the emissions factor should already exist
        existing_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            activity_name,
            geography,
            reference_product,
            source,
        )
        # Error if the emissions factor does not exist
        if not existing_emissions_factor:
            error_msg = (
                f"Emissions factor not found for exchange {exchange_id}. "
                f"Params: activity='{activity_name}', product='{reference_product}', "
                f"geography='{geography}', source='{source}'"
            )
            raise HTTPException(status_code=404, detail=error_msg)


        exchange = tenant_intermediate_exchanges.get_by_id(exchange_id)
        parent_emissions_factor_id = exchange.parent_emissions_factor.id

        new_exchange = TenantIntermediateExchange(
            exchange_name=(new_name or exchange_name or reference_product),
            amount=(exchange_amount or exchange.amount),
            input_stream=exchange.amount >= 0,
            unit=(exchange_unit or exchange.unit),
            parent_emissions_factor_id=parent_emissions_factor_id,
            exchange_emissions_factor_id=existing_emissions_factor.id,
        )

        # Calculate existing elemental EF values before updating the intermediate exchange
        elemental_ef_values = self._get_elemental_efs(
            tenant_id,
            parent_emissions_factor_id
        )

        # Delete old exchange and add new one
        tenant_intermediate_exchanges.delete_exchange(exchange_id)
        updated_exchange = tenant_intermediate_exchanges.add_exchanges([new_exchange])[0]

        # Get all exchanges and values (new and old) for the parent emissions factor
        # to recalculate impacts
        exchange_to_ef_values = self._get_exchange_ef_values(
            tenant_id,
            parent_emissions_factor_id
        )

        # Recalculate impact values
        self._calculate_and_update_impact_values(
            tenant_emissions_factors,
            parent_emissions_factor_id,
            elemental_ef_values,
            exchange_to_ef_values
        )

        return updated_exchange

    def _convert_to_exchange(self, exchange: IntermediateExchangeRead) -> Exchange:
        """Convert an IntermediateExchangeRead object into a standardized Exchange model."""

        return Exchange(
            **exchange.dict(
                exclude={"exchange_emissions_factor": True}
            ),
            exchange_emissions_factor=Activity(
                **exchange.exchange_emissions_factor.dict(
                    exclude={"reference_product": True, "activity_description": True}
                ),
                reference_product_name=exchange.exchange_emissions_factor.reference_product,
                product_information=exchange.exchange_emissions_factor.activity_description,
            ),
        )

    async def _get_geo_custom_exchanges(
        self,
        geography: str,
        exchanges: List[IntermediateExchangeRead]
    ) -> Tuple[List[IntermediateExchangeRead], bool]:
        electricity_exchanges = {}
        non_modified_exchanges = []

        #  combine electricity exchanges with the same name and combine the amounts
        for exchange in exchanges:
            if not "electricity" in exchange.exchange_name:
                non_modified_exchanges.append(exchange)
                continue
            if exchange.exchange_name in electricity_exchanges:
                electricity_exchanges[exchange.exchange_name].amount += exchange.amount
            else:
                electricity_exchanges[exchange.exchange_name] = exchange

        result_exchanges = []
        modified = False
        if electricity_exchanges:
            modified = True
            tasks = await asyncio.gather(
                *[
                    self.client.geography_match(
                        exchange.exchange_emissions_factor.activity_name,
                        exchange.exchange_emissions_factor.reference_product,
                        geography,
                    )
                    for exchange in electricity_exchanges.values()
                ]
            )

            for exchange, activity_ in zip(electricity_exchanges.values(), tasks):
                exchange_emissions_factor = emissions_factors.get_emissions_factor(
                    activity_.activity_name,
                    activity_.geography,
                    activity_.reference_product_name,
                    activity_.source,
                )
                if exchange_emissions_factor is None:
                    raise Exception(
                        f"Electricity exchange not found: {exchange.exchange_name}"
                    )
                if exchange.unit != exchange_emissions_factor.unit:
                    raise Exception(
                        f"Unit mismatch for electricity exchange: {exchange.exchange_name}"
                    )

                result_exchanges.append(
                    Exchange(
                        exchange_name=exchange.exchange_name,
                        amount=exchange.amount,
                        unit=exchange.unit,
                        input_stream=exchange.amount >= 0,
                        exchange_emissions_factor=activity_,
                    ),
                )
        result_exchanges.extend([
            self._convert_to_exchange(exchange)
            for exchange in non_modified_exchanges
        ])
        return result_exchanges, modified

    def _convert_ex_value_to_emission_factor_value_rw(
        self,
        exchange_to_ef_values:
            List[Tuple[TenantIntermediateExchange, List[TenantEmissionsFactorValueRead]]]
    ) -> List[Tuple[TenantIntermediateExchange, List[EmissionFactorValueRW]]]:
        """
        Convert TenantEmissionsFactorValueRead to EmissionFactorValueRW for each exchange.
        This method can go away later when TenantEmissionsFactorValueRead is replaced with a
        method that follows EmissionFactorValueRW format.
        """
        exchange_to_ef_values_rw = []
        for exchange, exchange_values in exchange_to_ef_values:
            ef_values_rw = [
                value.to_emission_factor_value_rw()
                for value in exchange_values
            ]
            exchange_to_ef_values_rw.append((exchange, ef_values_rw))
        return exchange_to_ef_values_rw


    def _get_total_intermediate_impact(
        self,
        impact_value: EmissionFactorValueRW,
        exchange_to_ef_values: List[Tuple[IntermediateExchangeRead, List[EmissionFactorValueRW]]],
    ) -> float:
        """
        Calculate the total intermediate impact from exchanges for given impact indicator
        of impact_value.
        """
        total_impact = 0
        for exchange, exchange_values in exchange_to_ef_values:
            exchange_value = next(
                (v.amount for v in exchange_values
                 if v.lcia_method == impact_value.lcia_method
                 and v.impact_category_name == impact_value.impact_category_name
                 and v.impact_category_indicator == impact_value.impact_category_indicator),
                0
            )
            total_impact += abs(exchange.amount) * exchange_value

        return total_impact

    def get_existing_emissions_factor_exchanges(
        self,
        tenant_emissions_factors: TenantEmissionsFactorRepo,
        tenant_intermediate_exchanges: TenantIntermediateExchangeRepo,
        activity_name: str,
        geography: str,
        reference_product: str,
        source: str,
    ) -> Tuple[TenantEmissionsFactor | None, List[IntermediateExchangeRead]]:

        existing_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            activity_name=activity_name,
            geography=geography,
            reference_product=reference_product,
            source=source,
        )

        if existing_emissions_factor:
            exchanges = tenant_intermediate_exchanges.get_all_for_emissions_factor(
                activity_name=activity_name,
                reference_product=reference_product,
                geography=geography,
                source=source,
            )
            return existing_emissions_factor, exchanges

        return None, []

    def _get_exchange_ef_values(
        self,
        tenant_id: str,
        parent_emissions_factor_id: int
    ) -> List[Tuple[TenantIntermediateExchange, List[TenantEmissionsFactorValueRead]]]:
        """
        Get exchange emission factor values for a given emissions factor ID.

        Args:
            tenant_id: ID of the tenant
            parent_emissions_factor_id: ID of the parent emissions factor

        Returns:
            List of tuples containing exchanges and their emission factor values
        """
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)

        # Get all exchanges for the parent emissions factor
        all_exchanges = tenant_intermediate_exchanges.get_all_for_emissions_factor_by_id(
            parent_emissions_factor_id
        )

        # Get emission factor values for each exchange
        exchange_to_ef_values = [
            (ex,
             TenantEmissionsFactorValueRepo(tenant_id).get_emissions_factor_values(
                 ex.exchange_emissions_factor.id
             ))
            for ex in all_exchanges
        ]

        return exchange_to_ef_values

    def _get_elemental_efs(
        self,
        tenant_id: str,
        emissions_factor_id: int
    ) -> List[EmissionFactorValueRW]:
        """
        Get elemental emission factor values for a given emissions factor ID.

        Args:
            tenant_id: ID of the tenant
            emissions_factor_id: ID of the emissions factor

        Returns:
            List of elemental emission factor values
        """
        # Get exchange EF values
        exchange_to_ef_values = self._get_exchange_ef_values(
            tenant_id,
            emissions_factor_id
        )

        # Get emission factor values
        tenant_ef_values = TenantEmissionsFactorValueRepo(tenant_id).get_emissions_factor_values(
            emissions_factor_id
        )
        # convert to EmissionFactorValueRW
        ef_values_rw = [
            value.to_emission_factor_value_rw()
            for value in tenant_ef_values
        ]
        # convert exchange_to_ef_values to EmissionFactorValueRW
        exchange_to_ef_values_rw = (
            self._convert_ex_value_to_emission_factor_value_rw(exchange_to_ef_values)
        )

        # Calculate elemental EF values
        return self._calculate_elemental_ef_values(ef_values_rw, exchange_to_ef_values_rw)

    def delete_intermediate_exchange(
        self,
        tenant_id: str,
        exchange_id: int,
    ) -> None:
        """
        Delete an intermediate exchange and recalculate impact values
        for the parent emissions factor.

        Args:
            tenant_id: ID of the tenant
            exchange_id: ID of the intermediate exchange to delete
        """
        tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)
        tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)

        # Get the exchange to be deleted
        exchange = tenant_intermediate_exchanges.get_by_id(exchange_id)
        if not exchange:
            raise HTTPException(
                status_code=404,
                detail=f"Intermediate exchange with ID {exchange_id} not found"
            )

        # Get elemental EF values before deleting the exchange
        elemental_ef_values = self._get_elemental_efs(
            tenant_id,
            exchange.parent_emissions_factor.id
        )

        # Delete the exchange
        tenant_intermediate_exchanges.delete_exchange(exchange_id)

        # Get remaining exchanges and their values
        exchange_to_ef_values = self._get_exchange_ef_values(
            tenant_id,
            exchange.parent_emissions_factor.id
        )

        # Recalculate impact values
        self._calculate_and_update_impact_values(
            tenant_emissions_factors,
            exchange.parent_emissions_factor.id,
            elemental_ef_values,
            exchange_to_ef_values
        )
