from clients.propelauth import get_supported_impact_factors
from databases.tenant.repository import ProductRepo
from pipelines_v2.process_model_walker import EmissionsWalker
from services.process_model_service import ProcessModelService
from utils.logger import logger
from exceptions import ResourceNotFoundException


DEFAULT_IMPACT_FACTOR = {
    "ReCiPe 2016 v1.03, midpoint (H)": [
        {
            "name": "climate change",
        }
    ],
}

class PipelineOperationUnAuthorizedException(Exception):
    pass

class PipelineService:
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id

    def walk(self, product_id: str):
        products = ProductRepo(self.tenant_id)
        product = products.get_product(product_id)
        if not product:
            raise ResourceNotFoundException(resource_type="Product", resource_name=product_id)

        cached_aggregation = product.get_lca_output_dict()
        if cached_aggregation:
            logger.debug(f"Cache hit for product {product_id}")
            try:
                return cached_aggregation
            except Exception as error:
                logger.error(f"Error parsing LCA output for product {product_id}: {error}")
                products.update_product_lca_output(product_id, None)

        logger.debug(f"Cache miss for product {product_id}")

        supported_impact_factors = get_supported_impact_factors(
            self.tenant_id,
        )

        process_model_service = ProcessModelService(self.tenant_id)
        nodes, edges = process_model_service.get_process_model_for_product(product_id)
        walker = EmissionsWalker(
            self.tenant_id,
            product_id,
            nodes,
            edges,
            supported_impact_factors,
        )
        aggregation = walker.walk().to_dict()
        products.update_product_lca_output(product_id, aggregation)

        return aggregation
