import json
from databases.tenant.models import PredictionOverride, TenantEmissionsFactor
from databases.tenant.repository.prediction_override import PredictionOverrideRepo
from databases.tenant.repository.tenant_emissions_factor import TenantEmissionsFactorRepo


class PredictionOverrideService:
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id

    def get_prediction_override(self, api_name: str, query: str) -> PredictionOverride | None:
        return PredictionOverrideRepo(self.tenant_id).get_prediction_override(api_name, query)

    def get_emissions_factor_match_override(self, query: str) -> TenantEmissionsFactor | None:
        override = self.get_prediction_override("emissions-factor-matching", query)
        if override:
            override_value = json.loads(override.override_value)
            return (
                TenantEmissionsFactorRepo(self.tenant_id).get_emissions_factor(
                    activity_name=override_value["activity_name"],
                    geography=override_value["geography"],
                    reference_product=override_value["reference_product_name"],
                    source=override_value["source"],
                )
            )
        return None

    def update_emissions_factor_match_override(
        self,
        query: str,
        activity_name: str,
        geography: str,
        reference_product_name: str,
        source: str,
    ) -> PredictionOverride:
        override_value = {
            "activity_name": activity_name,
            "geography": geography,
            "reference_product_name": reference_product_name,
            "source": source,
        }
        override = PredictionOverride(
            api_name="emissions-factor-matching",
            query=query,
            override_value=json.dumps(override_value),
        )
        return PredictionOverrideRepo(self.tenant_id).update_prediction_override(override)

    def create_emissions_factor_match_override(
        self,
        query: str,
        activity_name: str,
        geography: str,
        reference_product_name: str,
        source: str,
    ) -> PredictionOverride:
        override_value = {
            "activity_name": activity_name,
            "geography": geography,
            "reference_product_name": reference_product_name,
            "source": source,
        }
        override = PredictionOverride(
            api_name="emissions-factor-matching",
            query=query,
            override_value=json.dumps(override_value),
        )
        return PredictionOverrideRepo(self.tenant_id).create_prediction_override(override)
