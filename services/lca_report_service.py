import logging
from datetime import datetime
from databases.tenant.repository import ProductRepo, ProductAttributesRepo
from lca_report.document_adapters.bifma_document_adapter import (
    BifmaLCAReportAdapter,
    BifmaLCAReportData,
    ReportMeta as BifmaReportMeta,
    CompanyMeta as BifmaCompanyMeta,
)
from lca_report.document_adapters.process_model_adapter import (
    ProcessModelAdapter,
    ProcessModelData,
    CompanyMeta,
    ReportMeta,
)
from services.pipeline_service import PipelineService
from services.process_model_service import ProcessModelService
from clients.propelauth import get_org_metadata_field


class LCAReportService:
    def __init__(self, tenant_id: str) -> None:
        self._tenant_id = tenant_id
        self._product_repo = ProductRepo(tenant_id)
        self._product_attributes_repo = ProductAttributesRepo(tenant_id)
        self._pipeline_service = PipelineService(tenant_id)
        self._process_model_service = ProcessModelService(tenant_id)
        self._pcr_categories = get_org_metadata_field(tenant_id, "pcr_categories") or {}

    def create_lca_report(self, product_id: str) -> str:
        logging.info("[LCAReportService] create_lca_report called for product_id: %s", product_id)
        product = self._product_repo.get_product(product_id)
        product_attributes = self._product_attributes_repo.get_product_attributes(product_id)
        product_description = None
        functional_unit_description = None

        # Define target keys for attributes
        TARGET_DESCRIPTION_KEY = "productdescription"
        TARGET_FUNCTIONAL_UNIT_KEY = "functionalunitdescription"

        for attr, attr_def in product_attributes:
            if isinstance(attr_def.key, str):
                normalized_key = attr_def.key.strip().replace(" ", "").lower()

                if normalized_key == TARGET_DESCRIPTION_KEY and isinstance(attr.value, str):
                    product_description = attr.value

                elif normalized_key == TARGET_FUNCTIONAL_UNIT_KEY and isinstance(attr.value, str):
                    functional_unit_description = attr.value

        logging.info(
            "[LCAReportService] About to call pipeline_service.walk "
            "for product_id: %s",
            product_id
        )
        lca_output = self._pipeline_service.walk(product_id)
        logging.info(
            "[LCAReportService] pipeline_service.walk complete "
            "for product_id: %s",
            product_id
        )

        if self._pcr_categories.get(product.primary_category) == "BIFMA":
            logging.info(
                "[LCAReportService] Using BIFMA report path "
                "for category: %s",
                product.primary_category
            )
            data = BifmaLCAReportData(
                lca_output=lca_output,
                report_meta=BifmaReportMeta(
                    product_name=product.product_name,
                    country_of_use=product.country_of_use,
                    product_category=product.primary_category,
                    date=datetime.now().strftime("%Y-%m-%d"),
                    month_and_year_of_study=datetime.now().strftime('%B %Y'),
                ),
                company_meta=BifmaCompanyMeta(
                    company_name=product.brand,
                    manufacturer=product.brand,
                    country_of_origin=product.factory_locale["country"],
                ),
                product_description=product_description,
                functional_unit_description=functional_unit_description,
            )
            logging.info("[LCAReportService] Instantiating BifmaLCAReportAdapter")
            document_adapter = BifmaLCAReportAdapter(data)
        else:
            logging.info(
                "[LCAReportService] Using DEFAULT report path "
                "for category: %s",
                product.primary_category
            )
            report_meta = ReportMeta(
                country_of_use=product.country_of_use,
                unit=" Unit",
                functional_unit=1,
                functional_type=product.product_name,
                date=datetime.now().strftime("%Y-%m-%d"),
            )

            company_meta = CompanyMeta(
                company_name=product.brand.capitalize(),
                manufacturer=product.brand.capitalize(),
                country_of_origin=product.factory_locale["country"],
            )
            aggregation = self._pipeline_service.walk(product.product_id)
            nodes, _ = self._process_model_service.get_process_model_for_product(product.product_id)

            document_adapter = ProcessModelAdapter(
                ProcessModelData(
                    report_meta=report_meta,
                    company_meta=company_meta,
                    product_info=product,
                    aggregation=aggregation,
                    nodes=nodes,
                )
            )

        logging.info("[LCAReportService] Returning generated report")
        return document_adapter.create_lca_report()
