from services.emissions_factors_service import EmissionsFactorsService
from databases.shared.repository import emissions_factors
from databases.tenant.models import Ingredient
from databases.tenant.repository import IngredientRepo


class IngredientService:
    def __init__(self, tenant_id: str) -> None:
        self.tenant_id = tenant_id
        self.ingredients = IngredientRepo(tenant_id)
        self.emissions_factors = emissions_factors

    async def update_ingredient_ef_if_not_exists(
        self,
        ingredient_name: str,
        geography: str = "GLO",
    ) -> Ingredient | None:
        ef_matching_service = EmissionsFactorsService()
        existing_ingredient = self.ingredients.get_ingredient(ingredient_name)

        if existing_ingredient:
            if existing_ingredient.ef_kgco2e:
                return existing_ingredient

        ef_match = await ef_matching_service.get_emissions_factor_match(
            ingredient_name=ingredient_name,
            geography=geography,
            number_of_matches=25,
            api_version="latest"
        )

        emissions_factor = emissions_factors.get_emissions_factor(
            ef_match.matched_activity.activity_name,
            ef_match.matched_activity.geography,
            ef_match.matched_activity.reference_product_name,
            ef_match.matched_activity.source,
        )

        if not emissions_factor:
            return None

        ingredient = Ingredient(
            name=ingredient_name,
            chem_name=ingredient_name,
            ef_kgco2e=emissions_factor.kg_co2e,
            ef_source=emissions_factor.source,
        )

        return self.ingredients.upsert_ingredient(ingredient)
