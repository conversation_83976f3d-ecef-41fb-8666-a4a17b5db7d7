import os
from enum import Enum
from typing import Any
from dotenv import load_dotenv

load_dotenv()


class EnvironmentVariableNotSet(Exception):
    def __init__(self, var_name: str) -> None:
        super().__init__(f"Environment variable {var_name} not set")


class DefaultConfig(Enum):
    TESTING_MODE = False
    ADMIN_DB_NAME = "admin"
    SHARED_DB_NAME = "lca"
    DB_POOL_SIZE = 15
    DB_MAX_OVERFLOW = 30
    DB_LOGGING_ENABLED = False
    PORT_NUM = 5000
    MYSQL_SERVICE_NAME = "lca-mysql"
    FORCE_RESEED_TRADE_DATA = False
    BACKGROUND_SEEDING = False
    ML_MODEL_TIMEOUT_LONG = 180
    ML_MODEL_TIMEOUT_SHORT = 90
    TEST_TENANTS = "testtenant1"

class Config:
    def __init__(self):
        self.MYSQL_SERVICE_NAME = self.get_env_or_default(
            "MYSQL_SERVICE_NAME", DefaultConfig.MYSQL_SERVICE_NAME.value
        )
        self.HF_TOKEN = self.get_mandatory_env("HF_TOKEN")
        self.TESTING_MODE = self.get_env_or_default(
            "TESTING_MODE", DefaultConfig.TESTING_MODE.value
        )
        self.TEST_TENANTS = self.get_env_or_default(
            "TEST_TENANTS", DefaultConfig.TEST_TENANTS.value
        )
        self.DB_CONNECTION_STRING = self.get_mandatory_env("DB_CONNECTION_STRING")
        self.ADMIN_DB_NAME = self.get_env_or_default(
            "ADMIN_DB_NAME", DefaultConfig.ADMIN_DB_NAME.value
        )
        self.ADMIN_CONNECTION_STRING = (
            f"{self.DB_CONNECTION_STRING}/{self.ADMIN_DB_NAME}"
        )
        self.SHARED_DB_NAME = self.get_env_or_default(
            "SHARED_DB_NAME", DefaultConfig.SHARED_DB_NAME.value
        )
        self.SHARED_CONNECTION_STRING = (
            f"{self.DB_CONNECTION_STRING}/{self.SHARED_DB_NAME}"
        )
        self.DB_POOL_SIZE = self.get_env_or_default(
            "DB_POOL_SIZE", DefaultConfig.DB_POOL_SIZE.value
        )
        self.DB_MAX_OVERFLOW = self.get_env_or_default(
            "DB_MAX_OVERFLOW", DefaultConfig.DB_MAX_OVERFLOW.value
        )
        self.DB_LOGGING_ENABLED = self.get_env_or_default(
            "DB_LOGGING_ENABLED", DefaultConfig.DB_LOGGING_ENABLED.value
        )
        self.BACKGROUND_SEEDING = self.get_env_or_default(
            "BACKGROUND_SEEDING", DefaultConfig.BACKGROUND_SEEDING.value
        )
        self.FORCE_RESEED_TRADE_DATA = self.get_env_or_default(
            "FORCE_RESEED_TRADE_DATA", DefaultConfig.FORCE_RESEED_TRADE_DATA.value
        )
        self.PORT = self.get_env_or_default("PORT", DefaultConfig.PORT_NUM.value)
        self.PROPEL_AUTH_TOKEN = self.get_mandatory_env("PROPEL_AUTH_TOKEN")
        self.PROPEL_AUTH_URL = self.get_mandatory_env("PROPEL_AUTH_URL")
        self.GOOGLE_MAPS_API_KEY = self.get_mandatory_env("GOOGLE_MAPS_API_KEY")
        self.MAPBOX_ACCESS_TOKEN = self.get_mandatory_env("MAPBOX_ACCESS_TOKEN")
        self.ORS_TOKEN = self.get_mandatory_env("ORS_TOKEN")
        self.SENTRY_DSN = os.getenv("SENTRY_DSN")
        self.PREDICTION_ENDPOINT = os.getenv("PREDICTION_ENDPOINT")
        self.ML_MODELS_ENDPOINT = self.get_mandatory_env("ML_MODELS_ENDPOINT").rstrip("/") + "/"
        self.ML_MODEL_TIMEOUT_LONG = self.get_env_or_default(
            "ML_MODEL_TIMEOUT_LONG", DefaultConfig.ML_MODEL_TIMEOUT_LONG.value
        )
        self.ML_MODEL_TIMEOUT_SHORT = self.get_env_or_default(
            "ML_MODEL_TIMEOUT_SHORT", DefaultConfig.ML_MODEL_TIMEOUT_SHORT.value
        )

    def get_mandatory_env(self, var_name) -> Any:
        """
        >>> config = Config()
        >>> os.environ['TEST_VAR'] = 'value'
        >>> config.get_mandatory_env('TEST_VAR')
        'value'

        >>> del os.environ['TEST_VAR']
        >>> config.get_mandatory_env('TEST_VAR')
        Traceback (most recent call last):
        ...
        config.EnvironmentVariableNotSet: Environment variable TEST_VAR not set
        """
        env_value = os.getenv(var_name)
        if not env_value:
            raise EnvironmentVariableNotSet(var_name)
        return env_value

    def get_env_or_default(self, var_name, default_value) -> Any:
        """
        >>> config = Config()
        >>> os.environ['TEST_VAR_STR'] = 'hello'
        >>> config.get_env_or_default('TEST_VAR_STR', 'default')
        'hello'

        >>> config.get_env_or_default('UNSET_VAR', 'default')
        'default'

        >>> os.environ['TEST_VAR_INT'] = '42'
        >>> config.get_env_or_default('TEST_VAR_INT', 10)
        42

        >>> config.get_env_or_default('UNSET_VAR', 10)
        10

        >>> os.environ['TEST_BOOL'] = 'false'
        >>> config.get_env_or_default('TEST_BOOL', True)
        False

        >>> os.environ['TEST_BOOL2'] = 'true'
        >>> config.get_env_or_default('TEST_BOOL2', False)
        True
        """
        env_value = os.getenv(var_name)
        if not env_value:
            return default_value

        # Convert to appropriate type based on default value's type
        if isinstance(default_value, bool):
            return env_value.lower() in ["true", "1"]
        if isinstance(default_value, int):
            return int(env_value)
        if isinstance(default_value, float):
            return float(env_value)
        if isinstance(default_value, str):
            return env_value
        raise ValueError(
            f"Unsupported type {type(default_value)} for default value {default_value}"
        )


config = Config()
