FROM python:3.10-slim

WORKDIR /app

ENV PORT=5000
ENV WORKERS=4
ENV HF_HOME=/home/<USER>

RUN mkdir -p $HF_HOME && \
    apt-get update && \
    apt install -y  --no-install-recommends sqlite3 && \
    apt install -y  --no-install-recommends default-jre && \
    apt install -y  --no-install-recommends wget && \
    wget https://cb-blob-storage.s3.amazonaws.com/LibreOffice_7.4.0.1_Linux_x86-64_deb.tar.gz -O - | tar -xz && \
    cd LibreOffice_7.4.0.1_Linux_x86-64_deb/DEBS && \
    dpkg -i *.deb && \
    ln -s /usr/local/bin/libreoffice7.4 /usr/local/bin/soffice && \
    apt install -y --no-install-recommends dialog && \
    apt install -y --no-install-recommends openssh-server && \
    rm -rf /var/lib/apt/lists/* && \
    echo "root:Docker!" | chpasswd

COPY sshd_config /etc/ssh/

COPY requirements.txt /requirements.txt

RUN pip install --no-cache-dir -r /requirements.txt
RUN pip install --no-cache-dir uvicorn

COPY . .

EXPOSE $PORT 2222

COPY entrypoint.sh /entrypoint.sh
COPY startservice.sh /startservice.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]