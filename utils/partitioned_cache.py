from abc import ABC, abstractmethod
import threading
import j<PERSON>
from typing import Type, TypeVar
from diskcache import <PERSON><PERSON> as DC<PERSON>
from utils.logger import logger_instance

logger = logger_instance.get_logger()


class Cache(ABC):
    """Abstract base class for cache implementations."""
    def __init__(self, partition_key: str):
        self.partition_key = partition_key
        self.cache_hit: int = 0
        self.cache_miss: int = 0

    @abstractmethod
    def set(self, key: str, data: str):
        pass

    @abstractmethod
    def get(self, key: str) -> str | None:
        pass

    @abstractmethod
    def purge(self):
        pass

    @abstractmethod
    def purge_key(self, key: str):
        pass

    def get_cache_hit(self) -> int:
        return self.cache_hit

    def get_cache_miss(self) -> int:
        return self.cache_miss

T = TypeVar("T", bound=Cache)

class PartitionedCache:
    """Get cache for a specific cache partition key.
    >>> partitioned_cache = PartitionedCache(InMemoryCache)
    >>> cache_a = partitioned_cache.get_cache('partition_a')
    >>> cache_a.set('key1', 'data1')
    >>> cache_a.get('key1')
    'data1'
    >>> cache_a.purge_key('key1')
    >>> cache_a.get('key1') is None
    True
    >>> cache_b = partitioned_cache.get_cache('partition_b')
    >>> cache_b.get('key1') is None
    True
    >>> partitioned_cache.purge()
    >>> cache_a.get('key1') is None
    True
    """
    def __init__(self, cache_class: Type[T]) -> None:
        logger.warning(f"Creating new PartitionedCache with cache class {cache_class}")
        self.cache_class = cache_class
        self.cached_caches: dict[str, Cache] = {}
        self._lock: threading.Lock = threading.Lock()

    def get_cache(self, partition_key):
        with self._lock:
            if partition_key in self.cached_caches:
                return self.cached_caches[partition_key]
            new_cache = self.cache_class(partition_key)
            self.cached_caches[partition_key] = new_cache
            return new_cache

    def purge(self):
        with self._lock:
            for cache in self.cached_caches.values():
                cache.purge()
            self.cached_caches = {}


class InMemoryCache(Cache):
    """In memory implementation of Cache."""
    def __init__(self, partition_key: str):
        super().__init__(partition_key)
        self.cache: dict[str, str] = {}
        self._lock: threading.Lock = threading.Lock()

    def set(self, key: str, data: str):
        with self._lock:
            self.cache[key] = data

    def get(self, key: str) -> str | None:
        with self._lock:
            if key in self.cache:
                self.cache_hit += 1
                return self.cache[key]
            self.cache_miss += 1
            return None

    def purge(self):
        with self._lock:
            self.cache = {}
            self.cache_hit = 0
            self.cache_miss = 0

    def purge_key(self, key: str):
        with self._lock:
            if key in self.cache:
                del self.cache[key]

class DiskCache(Cache):
    def __init__(self, partition_key: str):
        super().__init__(partition_key)
        self.cached_db_file: str = f'tmp/{partition_key}'
        self._lock: threading.Lock = threading.Lock()

    def set(self, key: str, data: str) -> None:
        try:
            with self._lock, DCache(self.cached_db_file) as cache_db:
                cache_db.set(key, json.dumps(data))
                logger.debug(f"Persisted {key} to cache")
        except Exception as exception:
            logger.exception(f"Failed to write cache for {key} : {exception}")

    def get(self, key: str) -> str | None:
        try:
            with self._lock, DCache(self.cached_db_file) as cache_db:
                if key in cache_db:
                    self.cache_hit += 1
                    logger.debug(f"Found {key} in cache")
                    return json.loads(cache_db.get(key))
            self.cache_miss += 1
            return None
        except Exception as exception:
            logger.exception(f"Failed to read {key} from cache: {exception}")
            return None

    def purge(self):
        with self._lock, DCache(self.cached_db_file) as cache_db:
            cache_db.clear()
            self.cache_hit = 0
            self.cache_miss = 0

    def purge_key(self, key: str):
        try:
            with self._lock, DCache(self.cached_db_file) as cache_db:
                if key in cache_db:
                    del cache_db[key]
                logger.debug(f"Deleted {key} from cache")
        except Exception as exception:
            logger.exception(f"Failed to delete cache for {key} : {exception}")
