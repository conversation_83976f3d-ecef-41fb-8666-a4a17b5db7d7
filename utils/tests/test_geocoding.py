import unittest
from unittest.mock import patch, MagicMock
from utils.geocoding import fetch_coordinates, get_country_info


class TestGeocoding(unittest.TestCase):
    """Test cases for geocoding functionality."""

    def setUp(self):
        # Clear any existing cache before each test
        self.patcher = patch('utils.cache.read_cache')
        self.mock_read_cache = self.patcher.start()
        self.mock_read_cache.return_value = None

        self.write_patcher = patch('utils.cache.write_cache')
        self.mock_write_cache = self.write_patcher.start()

    def tearDown(self):
        self.patcher.stop()
        self.write_patcher.stop()

    @patch('requests.get')
    def test_fetch_coordinates_caching(self, mock_get):
        """Test that fetch_coordinates properly caches results."""
        # Mock the Mapbox API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "features": [
                {
                    "geometry": {
                        "coordinates": [10.0, 20.0]  # [longitude, latitude]
                    }
                }
            ]
        }
        mock_get.return_value = mock_response

        # First call - should hit the API
        location = "London, UK"
        result = fetch_coordinates(location)

        # Verify API was called
        mock_get.assert_called_once()
        self.assertEqual(result.latitude, 20.0)
        self.assertEqual(result.longitude, 10.0)

        # Verify cache was written
        self.mock_write_cache.assert_called_once_with(
            f"MAPBOX_GEOCODING_{location}",
            {"latitude": 20.0, "longitude": 10.0}
        )

        # Reset mocks
        mock_get.reset_mock()
        self.mock_write_cache.reset_mock()

        # Set up cache hit for second call
        self.mock_read_cache.return_value = {"latitude": 20.0, "longitude": 10.0}

        # Second call - should use cache
        result2 = fetch_coordinates(location)

        # Verify API was not called
        mock_get.assert_not_called()
        self.assertEqual(result2.latitude, 20.0)
        self.assertEqual(result2.longitude, 10.0)

        # Verify cache was not written again
        self.mock_write_cache.assert_not_called()

    @patch('requests.get')
    def test_fetch_coordinates_cache_miss(self, mock_get):
        """Test that fetch_coordinates properly handles cache misses."""
        # Mock the Mapbox API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "features": [
                {
                    "geometry": {
                        "coordinates": [10.0, 20.0]  # [longitude, latitude]
                    }
                }
            ]
        }
        mock_get.return_value = mock_response

        # Ensure cache miss
        self.mock_read_cache.return_value = None

        # Call function
        location = "Paris, France"
        result = fetch_coordinates(location)

        # Verify API was called
        mock_get.assert_called_once()
        self.assertEqual(result.latitude, 20.0)
        self.assertEqual(result.longitude, 10.0)

        # Verify cache was written
        self.mock_write_cache.assert_called_once_with(
            f"MAPBOX_GEOCODING_{location}",
            {"latitude": 20.0, "longitude": 10.0}
        )


    @patch('utils.geocoding.CountryInfo')
    def test_get_country_info_caching(self, mock_country_info):
        """Test that get_country_info properly caches results."""
        # Mock the CountryInfo response
        mock_country = MagicMock()
        mock_country.info.return_value = {
            "ISO": {
                "alpha2": "GB",
                "alpha3": "GBR"
            },
            "name": "United Kingdom",
            "capital": "London",
            "area": 242495.0
        }
        mock_country.capital_latlng.return_value = (51.5074, -0.1278)
        mock_country_info.return_value = mock_country

        # First call - should create a new CountryInfo object
        country_name = "United Kingdom"
        result = get_country_info(country_name)

        # Verify CountryInfo was called
        mock_country_info.assert_called_once_with(country_name)
        self.assertEqual(result.alpha_2_code, "GB")
        self.assertEqual(result.alpha_3_code, "GBR")
        self.assertEqual(result.name, "United Kingdom")
        self.assertEqual(result.capital, "London")
        self.assertEqual(result.capital_lat_lng, (51.5074, -0.1278))
        self.assertEqual(result.area_sq_km, 242495.0)

        # Verify cache was written
        self.mock_write_cache.assert_called_once_with(
            f"COUNTRY_INFO_{country_name.upper()}",
            {
                "alpha_2_code": "GB",
                "alpha_3_code": "GBR",
                "name": "United Kingdom",
                "capital": "London",
                "capital_lat_lng": (51.5074, -0.1278),
                "area_sq_km": 242495.0
            }
        )

        # Reset mocks
        mock_country_info.reset_mock()
        self.mock_write_cache.reset_mock()

        # Set up cache hit for second call
        self.mock_read_cache.return_value = {
            "alpha_2_code": "GB",
            "alpha_3_code": "GBR",
            "name": "United Kingdom",
            "capital": "London",
            "capital_lat_lng": (51.5074, -0.1278),
            "area_sq_km": 242495.0
        }

        # Second call - should use cache
        result2 = get_country_info(country_name)

        # Verify CountryInfo was not called
        mock_country_info.assert_not_called()
        self.assertEqual(result2.alpha_2_code, "GB")
        self.assertEqual(result2.alpha_3_code, "GBR")
        self.assertEqual(result2.name, "United Kingdom")
        self.assertEqual(result2.capital, "London")
        self.assertEqual(result2.capital_lat_lng, (51.5074, -0.1278))
        self.assertEqual(result2.area_sq_km, 242495.0)

        # Verify cache was not written again
        self.mock_write_cache.assert_not_called()

    @patch('utils.geocoding.CountryInfo')
    def test_get_country_info_cache_miss(self, mock_country_info):
        """Test that get_country_info properly handles cache misses."""
        # Mock the CountryInfo response
        mock_country = MagicMock()
        mock_country.info.return_value = {
            "ISO": {
                "alpha2": "FR",
                "alpha3": "FRA"
            },
            "name": "France",
            "capital": "Paris",
            "area": 551695.0
        }
        mock_country.capital_latlng.return_value = (48.8566, 2.3522)
        mock_country_info.return_value = mock_country

        # Ensure cache miss
        self.mock_read_cache.return_value = None

        # Call function
        country_name = "France"
        result = get_country_info(country_name)

        # Verify CountryInfo was called
        mock_country_info.assert_called_once_with(country_name)
        self.assertEqual(result.alpha_2_code, "FR")
        self.assertEqual(result.alpha_3_code, "FRA")
        self.assertEqual(result.name, "France")
        self.assertEqual(result.capital, "Paris")
        self.assertEqual(result.capital_lat_lng, (48.8566, 2.3522))
        self.assertEqual(result.area_sq_km, 551695.0)

        # Verify cache was written
        self.mock_write_cache.assert_called_once_with(
            f"COUNTRY_INFO_{country_name.upper()}",
            {
                "alpha_2_code": "FR",
                "alpha_3_code": "FRA",
                "name": "France",
                "capital": "Paris",
                "capital_lat_lng": (48.8566, 2.3522),
                "area_sq_km": 551695.0
            }
        )


if __name__ == '__main__':
    unittest.main()
