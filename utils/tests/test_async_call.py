import unittest
import threading
import asyncio
from utils.async_call import safe_async

async def async_test_function():
    await asyncio.sleep(1)
    return "Test Completed"

class TestSafeAsync(unittest.TestCase):
    def test_main_thread_no_running_loop(self):
        """Test safe_async in the main thread without a running event loop."""
        result = safe_async(async_test_function)
        self.assertEqual(result, "Test Completed")

    def test_main_thread_running_loop(self):
        """Test safe_async in the main thread with a running event loop."""
        def run_main_thread_event_loop():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(asyncio.sleep(2))

        event_loop_thread = threading.Thread(target=run_main_thread_event_loop)
        event_loop_thread.start()
        event_loop_thread.join()

        result = safe_async(async_test_function)
        self.assertEqual(result, "Test Completed")

    def test_non_main_thread(self):
        """Test safe_async in a non-main thread."""
        def non_main_thread_test():
            result = safe_async(async_test_function)
            self.assertEqual(result, "Test Completed")

        non_main_thread = threading.Thread(target=non_main_thread_test)
        non_main_thread.start()
        non_main_thread.join()
