from typing import Callable, TypeVar, Awaitable, Any
import asyncio
import concurrent.futures
import anyio


T = TypeVar('T')

def safe_async(async_func: Callable[..., Awaitable[T]], *args: Any, **kwargs: Any) -> T:
    """Safely run async function in a synchronous context."""
    try:
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        if loop.is_running():
            with concurrent.futures.ThreadPoolExecutor() as pool:
                future = pool.submit(lambda: anyio.run(async_func, *args, **kwargs))
                return future.result()
        return loop.run_until_complete(async_func(*args, **kwargs))
    except Exception as error:
        raise RuntimeError(f"Error in async function: {error}") from error
