from urllib import parse
from typing import List
from dataclasses import dataclass
import requests as req
from pydantic import BaseModel
from tenacity import retry, stop_after_attempt, wait_exponential
from geopy.geocoders import GoogleV3
import googlemaps
from countryinfo import CountryInfo
import pycountry
from config import config
from utils import cache
from utils.logger import logger_instance

logger = logger_instance.get_logger()
gmaps = googlemaps.Client(key=config.GOOGLE_MAPS_API_KEY)


@dataclass(frozen=True)
class Mappings:
    non_iso_country_codes = {"UK": "United Kingdom"}


@dataclass(frozen=True)
class Country(BaseModel):
    alpha_2_code: str
    alpha_3_code: str
    name: str
    capital: str | None = None
    capital_lat_lng: tuple | None = None
    area_sq_km: float | None = None

class Coordinates(BaseModel):
    def __init__(self, latitude: float, longitude: float) -> None:
        super().__init__(latitude=latitude, longitude=longitude)

    latitude: float
    longitude: float


def get_country_iso2_from_iso3(iso3: str) -> str:
    return pycountry.countries.get(alpha_3=iso3).alpha_2


def get_all_countries() -> List[Country]:
    countries = CountryInfo().all()
    all_countries = []

    for country_name in countries:
        try:
            country = CountryInfo(country_name)
            info = country.info()

            country_data = Country(
                alpha_2_code=info.get("ISO", {}).get("alpha2"),
                alpha_3_code=info.get("ISO", {}).get("alpha3"),
                name=info.get("name"),
                capital=info.get("capital"),
                capital_lat_lng=country.capital_latlng()
                if info.get("capital")
                else None,
                area_sq_km=info.get("area"),
            )

            all_countries.append(country_data)
        except Exception as error:
            raise Exception(f"Error retrieving countries: {error}") from error

    return all_countries


def get_country_info(country: str) -> Country:
    """
    Get country information by country name or ISO-2/ISO-3 code with caching
    Note: There is no country with 2 or 3 letter name.
        So, if the length of the country is 2 or 3, it is considered as ISO-2 or ISO-3 code
    """
    try:
        # Skip geocoding for special geographic scopes
        if country.upper() in ["GLO", "RER", "ROW"]:  # Global, Europe, Rest of World
            return None

        # Generate cache key
        cache_key = f"COUNTRY_INFO_{country.upper()}"

        # Try cache first
        cached_country = cache.read_cache(cache_key)
        if cached_country:
            logger.info(f"Using cached country info for {country}")
            return Country(**cached_country)

        # If not in cache, proceed with original logic
        if len(country) == 3:
            # Convert ISO-3 code to ISO-2 code
            country = get_country_iso2_from_iso3(country)

        _country = CountryInfo(country)

        info = _country.info()

        country_data = Country(
            alpha_2_code=info.get("ISO", {}).get("alpha2"),
            alpha_3_code=info.get("ISO", {}).get("alpha3"),
            name=info.get("name"),
            capital=info.get("capital"),
            capital_lat_lng=_country.capital_latlng() if info.get("capital") else None,
            area_sq_km=info.get("area"),
        )

        # Cache the result
        cache.write_cache(cache_key, country_data.dict())
        return country_data

    except Exception:
        logger.error(f"Error retrieving data for {country}")
        return None


def geocode(address: str):
    if config.TESTING_MODE:
        logger.info("Testing mode enabled, skipping geocoding")
        return None

    cache_key = f"geocode_{address}"
    cached_result = cache.read_cache(cache_key)
    if cached_result:
        return cached_result

    try:
        geolocator = GoogleV3(api_key=config.GOOGLE_MAPS_API_KEY)
        location = geolocator.geocode(address)

        if location:
            coordinates = {"lat": location.latitude, "lng": location.longitude}
            cache.write_cache(cache_key, coordinates)
            return coordinates

        return None
    except Exception:
        logger.exception(f"Error geocoding address {address}")
        return None


def search_poi(poi: str):
    if config.TESTING_MODE:
        logger.info("Testing mode enabled, skipping geocoding")
        return None

    cache_key = f"search_poi_{poi}"
    cached_result = cache.read_cache(cache_key)
    if cached_result:
        return cached_result

    try:
        places = gmaps.places(poi)
        if not places["status"] == "OK":
            return None

        for place in places["results"]:
            if "point_of_interest" in place.get(
                "types", []
            ) or "establishment" in place.get("types", []):
                poi_data = {}
                if "plus_code" in place and "compound_code" in place["plus_code"]:
                    # compound_code: 78V2+V2 Getafe, Spai
                    city_and_country = " ".join(
                        place["plus_code"]["compound_code"].split(" ")[1:]
                    )
                    poi_data["city"] = (
                        city_and_country.split(",", maxsplit=1)[0].strip()
                        if len(city_and_country.split(","))
                        else None
                    )
                if "geometry" in place and "location" in place["geometry"]:
                    poi_data["address"] = place["formatted_address"]
                    address_segments = place["formatted_address"].split(",")
                    poi_data["country"] = address_segments[
                        len(address_segments) - 1
                    ].strip()
                    poi_data["latlng"] = place["geometry"]["location"]
                    cache.write_cache(cache_key, poi_data)
                    return poi_data

        return None
    except Exception:
        logger.exception(f"Error geocoding poi {poi}")
        return None

@retry(stop=stop_after_attempt(4), wait=wait_exponential(multiplier=2, min=2, max=600))
def fetch_coordinates(location: str) -> Coordinates:
    """Fetch coordinates using MapBox with caching"""
    cache_key = f"MAPBOX_GEOCODING_{location}"

    # Try cache first
    cached_coords = cache.read_cache(cache_key)
    if cached_coords:
        logger.info(f"Using cached coordinates for {location}")
        return Coordinates(**cached_coords)

    try:
        # If not in cache, fetch from Mapbox
        # pylint: disable=line-too-long
        url = f"https://api.mapbox.com/geocoding/v5/mapbox.places/{parse.quote(location)}.json?access_token={config.MAPBOX_ACCESS_TOKEN}"
        response = req.get(url, timeout=60)
        data = response.json()
        coords = data["features"][0]["geometry"]["coordinates"]
        logger.info(f"Got coords {coords} for {location}")

        coordinates = Coordinates(
            latitude=coords[1],
            longitude=coords[0],
        )

        # Cache the result
        cache.write_cache(cache_key, coordinates.dict())
        return coordinates
    except Exception as error:
        raise error

class RouteNotConstructableError(Exception):
    """Raised when a route cannot be constructed"""


class RateLimitExceededError(Exception):
    """Raised when the rate limit is exceeded for Mapbox"""

def generate_land_route(
    origin: Coordinates, destination: Coordinates
):
    """
    Generate a land route using
    Mapbox.
    """

    try:
        coords = (
            f"{origin.longitude},{origin.latitude};"
            f"{destination.longitude},{destination.latitude}"
        )

        base_url = "https://api.mapbox.com/directions/v5/mapbox"
        params = {
            "alternatives": "false",
            "exclude": "ferry",
            "geometries": "geojson",
            "overview": "simplified",
            "steps": "false",
            "access_token": config.MAPBOX_ACCESS_TOKEN,
        }

        # construct URL
        param_string = "&".join(f"{k}={v}" for k, v in params.items())
        url = f"{base_url}/driving/{coords}?{param_string}"

        response = req.get(
            url, headers={"Content-Type": "application/json"}, timeout=60
        )

        # Handle response status
        if response.status_code != 200:
            logger.warning(
                f"Got status code {response.status_code}. "
                f"Response {response.text}. "
                f"For url {url}"
            )

            if response.status_code in [400, 404]:
                raise RouteNotConstructableError("Route Not Constructable")

            if response.status_code == 429:
                raise RateLimitExceededError("Rate Limit Exceeded")

        data = response.json()

        # Validate response data
        if not "routes" in data or not data["routes"]:
            raise RouteNotConstructableError("No valid routes found")

        return data

    except req.exceptions.RequestException as error:
        raise Exception(f"Request to Mapbox API failed: {error}") from error
