"""Utils pertaining to tree traversals."""
from typing import Dict, List, Any

def get_leaf_nodes_lfo(tree: Dict) -> List[Any]:
    nodes = []
    queue = list(tree.items())

    while queue:
        category, subcategories = queue.pop(0)

        if isinstance(subcategories, dict):
            queue.extend(subcategories.items())
            if not subcategories.keys():
                nodes.append(category)

        elif isinstance(subcategories, list):
            for subcategory in subcategories:
                if isinstance(subcategory, dict):
                    queue.extend(subcategory.items())
                    if not subcategory.keys():
                        nodes.append(category)
    return nodes

def get_parent_nodes(tree: Dict, value: Any) -> List[Any]:
    queue = [(tree, [])]

    while queue:
        current_subtree, current_parents = queue.pop(0)

        for key, subcategories in current_subtree.items():
            if key == value:
                return current_parents

            if isinstance(subcategories, dict):
                queue.append((subcategories, current_parents + [key]))

            elif isinstance(subcategories, list):
                for subcategory in subcategories:
                    if isinstance(subcategory, dict):
                        queue.append((subcategory, current_parents + [key]))

    return []
