from datetime import datetime
import sys
from loguru import logger


# pylint: disable=attribute-defined-outside-init
class Logger:
    _logger_instance = None

    def __new__(cls):
        if cls._logger_instance is None:
            cls._logger_instance = super().__new__(cls)
            cls._logger_instance._configure_logger()

        return cls._logger_instance

    def _configure_logger(self):
        self.logger = logger

        current_date = datetime.now().strftime("%Y%m%d")
        log_file_path = f"logs/lca_calculator_core_{current_date}.log"

        logger.add(
            log_file_path,
            rotation="100 MB",
            retention="30 days",
            compression="zip",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}",
            backtrace=True,
        )

        # Terminate program on FATAL/CRITICAL log level
        logger.add(lambda _: sys.exit(1), level="CRITICAL")

    def get_logger(self):
        return self.logger


logger_instance = Logger()
