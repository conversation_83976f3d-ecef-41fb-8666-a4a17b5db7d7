import dbm
import json
import threading
from typing import Any
from utils.logger import logger_instance

logger = logger_instance.get_logger()

CACHE_DB_FILE = "storage/cache/cache"

"""
TODO: Replace DBM with RocksDB for performance
TODO: purge_cache()
"""


_lock: threading.Lock = threading.Lock()

def write_cache(key, data) -> None:
    try:
        with _lock, dbm.open(CACHE_DB_FILE, "c") as cache_db:
            cache_db[key] = json.dumps(data)
            logger.debug(f"Persisted {key} to cache")
    except Exception as exception:
        logger.exception(f"Failed to write cache for {key} : {exception}")

def read_cache(key) -> Any | None:
    try:
        with _lock, dbm.open(CACHE_DB_FILE, "c") as cache_db:
            if key in cache_db:
                logger.debug(f"Found {key} in cache")
                return json.loads(cache_db[key])

        return None
    except Exception as exception:
        logger.exception(f"Failed to read {key} from cache: {exception}")
        return None
