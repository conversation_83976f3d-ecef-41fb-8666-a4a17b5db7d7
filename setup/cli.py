import argparse
from databases.engine import engine_pool
from setup.db import (
    make_revision,
    revert_revision,
    upgrade_head,
    setup,
    setup_server
)
from utils.logger import logger

setup_parser = argparse.ArgumentParser(
    description="Manage database setups and migrations."
)

setup_parser.add_argument(
    "--setup-server",
    action="store_true",
    help="Set up the server.",
)
setup_parser.add_argument(
    "--clean-init",
    action="store_true",
    help="Perform a clean initialization of all databases.",
)
setup_parser.add_argument(
    "--clean-init-shared-db",
    action="store_true",
    help="Perform a clean initialization of shared DB Alembic versioning.",
)
setup_parser.add_argument(
    "--clean-init-tenant-db",
    action="store_true",
    help="Perform a clean initialization of tenant DB Alembic versioning.",
)
setup_parser.add_argument(
    "--tenant-ids",
    type=str,
    required=False,
    help="Specify specific Tenant IDs (comma separated))",
)

setup_parser.add_argument(
    "--make-revision",
    type=str,
    required=False,
    help="Create a new revision for a database. (admin, shared, tenant)",
)

setup_parser.add_argument(
    "--revert-revision",
    action="store_true",
    required=False,
    help="Revert the last revision for a database. (admin, shared, tenant)",
)

setup_parser.add_argument(
    "--upgrade-head",
    action="store_true",
    required=False,
    help="Upgrade the head for a database. (admin, shared, tenant, all)",
)

setup_parser.add_argument(
    "--setup",
    action="store_true",
    required=False,
    help="Setup a specific database. (admin, shared, tenant, all)",
)

setup_parser.add_argument(
    "--database",
    type=str,
    required=False,
    help="Database for which to create a new revision.",
)

@logger.catch
def main() -> None:
    args = setup_parser.parse_args()
    # Split comma separated tenant IDs into a list
    engine = engine_pool.get_root_engine()
    tenant_ids = None
    if args.tenant_ids:
        tenant_ids = args.tenant_ids.split(",")

    if args.make_revision:
        if not args.database:
            raise ValueError("Database argument is required for make-revision")

        make_revision(engine, args.database, args.make_revision)

    if args.revert_revision:
        if not args.database:
            raise ValueError("Database argument is required for revert-revision")

        revert_revision(engine, args.database)

    if args.upgrade_head:
        if not args.database:
            raise ValueError("Database argument is required for upgrade-head")

        upgrade_head(engine, args.database)

    if args.setup:
        if not args.database:
            raise ValueError("Database argument is required for setup")

        setup(engine, args.database)

    if args.setup_server:
        setup_server(engine, tenant_ids)

if __name__ == "__main__":
    main()
