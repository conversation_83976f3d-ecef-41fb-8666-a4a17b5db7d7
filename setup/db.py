from typing import List
import os
from enum import Enum
from sqlmodel import Session
from huggingface_hub import snapshot_download
from clients.propelauth import get_orgs
from databases.shared.seeds import seed_all as seed_shared_all
from databases.migrations import MigrationManager
from config import config
from utils.logger import logger


class DatabaseType(str, Enum):
    ADMIN = "admin"
    SHARED = "shared"
    TENANT = "tenant"
    ALL = "all"

def check_if_primary_data_exists(org_name: str) -> bool:
    repo_id = f"CarbonBright/primary-data-{org_name}"
    try:
        snapshot_download(
            repo_id=repo_id,
            repo_type="dataset",
        )
        return True
    except Exception:
        return False

def setup_admin_database(engine):
    with Session(engine) as session:
        session.execute(f"CREATE DATABASE IF NOT EXISTS {config.ADMIN_DB_NAME};")
        logger.info(f"Created database {config.ADMIN_DB_NAME}")

    migration_manager = MigrationManager(
        config_file_path="./databases/admin/alembic.ini",
        script_location="./databases/admin/alembic",
        db_url=config.ADMIN_CONNECTION_STRING,
    )

    try:
        migration_manager.upgrade_head()
        logger.info("Upgraded alembic head")

    except Exception as error:
        logger.error(f"Alembic upgrade error for {config.ADMIN_DB_NAME}: {str(error)}")

def setup_shared_database(engine):
    with Session(engine) as session:
        session.execute(f"CREATE DATABASE IF NOT EXISTS {config.SHARED_DB_NAME};")
        logger.info(f"Created database {config.SHARED_DB_NAME}")

    migration_manager = MigrationManager(
        config_file_path="./databases/shared/alembic.ini",
        script_location="./databases/shared/alembic",
        db_url=config.SHARED_CONNECTION_STRING,
    )

    try:
        migration_manager.upgrade_head()
        logger.info("Upgraded alembic head")

        logger.info("Seeding base data")
        seed_shared_all()
    except Exception as error:
        logger.critical(
            f"Alembic upgrade error for {config.SHARED_DB_NAME}: {str(error)}"
        )

def setup_tenant_databases(engine, tenant_ids: List[str] = None):
    if not tenant_ids:
        tenant_ids = get_orgs()

    migration_manager = MigrationManager(
        config_file_path="./databases/tenant/alembic.ini",
        script_location="./databases/tenant/alembic",
    )

    with Session(engine) as session:
        for tenant_id in tenant_ids:
            try:
                db_name = f"tenant_{tenant_id.lower()}"

                session.execute(f"CREATE DATABASE IF NOT EXISTS {db_name};")
                logger.info(f"Created database {db_name}")

                logger.info("Running alembic upgrade head")

                db_url = f"{config.DB_CONNECTION_STRING}/{db_name}"
                migration_manager.set_db_url(db_url)
                migration_manager.upgrade_head()

            except Exception:
                logger.exception(f"Error setting up tenant {tenant_id}")


def make_revision(engine, database: str, message: str):
    config_file_path = f"./databases/{database}/alembic.ini"
    script_location = f"./databases/{database}/alembic"

    if not os.path.exists(config_file_path) or not os.path.exists(script_location):
        raise FileNotFoundError(
            f"Config file or script location not found for {database}"
        )

    migration_manager = MigrationManager(
        config_file_path,
        script_location,
    )

    try:
        with Session(engine) as session:
            session.execute("CREATE DATABASE IF NOT EXISTS alembic_test;")
            logger.info("Created database alembic_test")

        migration_manager.set_db_url(config.DB_CONNECTION_STRING + "/alembic_test")
        migration_manager.upgrade_head()
        migration_manager.revision(message)
        logger.info("Created alembic revision")

        with Session(engine) as session:
            session.execute("DROP DATABASE alembic_test;")
            logger.info("Dropped database alembic_test")

    except Exception as error:
        logger.error(f"Alembic revision error for {database}: {str(error)}")


def revert_revision(engine, database: str):
    config_file_path = f"./databases/{database}/alembic.ini"
    script_location = f"./databases/{database}/alembic"

    if not os.path.exists(config_file_path) or not os.path.exists(script_location):
        raise FileNotFoundError(
            f"Config file or script location not found for {database}"
        )

    migration_manager = MigrationManager(
        config_file_path,
        script_location,
    )

    databases = (
        [f"tenant_{db.lower()}" for db in get_orgs()]
        if database == DatabaseType.TENANT
        else [get_db_proxy_name(database)]
    )

    try:
        for _database in databases:
            with Session(engine) as session:
                session.execute(f"CREATE DATABASE IF NOT EXISTS {_database};")
                logger.info(f"Created database {_database}")

            migration_manager.set_db_url(config.DB_CONNECTION_STRING + f"/{_database}")
            migration_manager.upgrade_head()
            migration_manager.downgrade_head()

        with Session(engine) as session:
            session.execute("CREATE DATABASE IF NOT EXISTS alembic_test;")
            logger.info("Created database alembic_test")

        migration_manager.set_db_url(config.DB_CONNECTION_STRING + "/alembic_test")
        migration_manager.upgrade_head()
        migration_manager.revert_revision()

        logger.info("Reverted alembic revision")

        with Session(engine) as session:
            session.execute("DROP DATABASE alembic_test;")
            logger.info("Dropped database alembic_test")

    except Exception as error:
        logger.error(f"Alembic revert error for {database}: {str(error)}")


def upgrade_head(engine, database: str):
    if database == DatabaseType.ALL:
        upgrade_head(engine, DatabaseType.ADMIN.value)
        upgrade_head(engine, DatabaseType.SHARED.value)
        upgrade_head(engine, DatabaseType.TENANT.value)
        return

    config_file_path = f"./databases/{database}/alembic.ini"
    script_location = f"./databases/{database}/alembic"

    if not os.path.exists(config_file_path) or not os.path.exists(script_location):
        raise FileNotFoundError(
            f"Config file or script location not found for {database}"
        )

    migration_manager = MigrationManager(
        config_file_path,
        script_location,
    )

    databases = (
        [f"tenant_{db.lower()}" for db in get_orgs()]
        if database == DatabaseType.TENANT
        else [get_db_proxy_name(database)]
    )

    try:
        for _database in databases:
            with Session(engine) as session:
                session.execute(f"CREATE DATABASE IF NOT EXISTS {_database};")
                logger.info(f"Created database {_database}")

            migration_manager.set_db_url(config.DB_CONNECTION_STRING + f"/{_database}")
            migration_manager.upgrade_head()
            logger.info(f"Upgraded alembic head for {_database}")
    except Exception as error:
        logger.error(f"Alembic upgrade head error for {database}: {str(error)}")

def get_db_proxy_name(database: str):
    if database == DatabaseType.ADMIN:
        return config.ADMIN_DB_NAME
    if database == DatabaseType.SHARED:
        return config.SHARED_DB_NAME
    return database

def setup(engine, database: str):
    if database == DatabaseType.ADMIN:
        setup_admin_database(engine)
    elif database == DatabaseType.SHARED:
        setup_shared_database(engine)
    elif database == DatabaseType.TENANT:
        setup_tenant_databases(engine)
    elif database == DatabaseType.ALL:
        setup_server(engine)
    else:
        raise ValueError(f"Invalid database: {database}")

def setup_server(engine, tenant_ids: List[str] = None):
    setup_admin_database(engine)
    setup_shared_database(engine)
    setup_tenant_databases(engine, tenant_ids)
