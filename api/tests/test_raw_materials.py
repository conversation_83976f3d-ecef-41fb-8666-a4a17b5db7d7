import unittest
from fastapi.testclient import TestClient
from sqlmodel import Session
from databases.engine import engine_pool
from databases.tenant.models import (
    TenantEmissionsFactor,
    TenantEmissionsFactorValue,
    TenantImpactIndicator,
)
from api.app import app
from setup.db import setup_server
from services.prediction_override_service import PredictionOverrideService


client = TestClient(app)

class TestRawMaterials(unittest.TestCase):
    tenant_id = "test"

    def setUp(self):
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.execute(f"DROP DATABASE IF EXISTS tenant_{self.tenant_id};")
            session.commit()

        with Session(engine) as session:
            session.execute(f"CREATE DATABASE tenant_{self.tenant_id};")
            session.commit()

        setup_server(engine, [self.tenant_id])

        with Session(engine_pool.get_tenant_engine(self.tenant_id)) as session:
            session.add(
                TenantEmissionsFactor(
                    id=1,
                    activity_name="Citric Acid Production",
                    activity_type="Raw Materials",
                    reference_product="Citric Acid",
                    reference_product_amount=1,
                    unit="kg",
                    geography="US",
                    source="Custom",
                    activity_description="Citric Acid Production Activity",
                )
            )
            session.add(
                TenantImpactIndicator(
                    id=1,
                    lcia_method="ReCiPe 2016 v1.03, midpoint (H)",
                    category="climate change",
                    indicator="global warming potential (GWP100)",
                    unit="kg CO2-Eq",
                    description="",
                )
            )
            session.add(
                TenantEmissionsFactorValue(
                    emissions_factor_id=1,
                    impact_indicator_id=1,
                    amount=2.5,
                )
            )
            session.commit()

    def test_emissions_factor_recommendation_override(self):
        prediction_override_service = PredictionOverrideService(self.tenant_id)
        prediction_override_service.create_emissions_factor_match_override(
            query="water",
            activity_name="Citric Acid Production",
            geography="US",
            reference_product_name="Citric Acid",
            source="Custom",
        )

        response = client.post(
            "/raw-materials/activities/recommendations",
            headers={
                "x-tenant-id": self.tenant_id,
            },
            json={
                "chemical_name": "water",
                "unit": "g",
                "geography": "US",
            },
        )
        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["matched_activity"]["activity_name"], "Citric Acid Production")
        self.assertEqual(data["matched_activity"]["reference_product_name"], "Citric Acid")
        self.assertEqual(data["matched_activity"]["source"], "Custom")
        self.assertEqual(data["matched_activity"]["geography"], "US")
