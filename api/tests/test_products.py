# pylint: disable=protected-access,import-outside-toplevel
import unittest
from fastapi.testclient import TestClient
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.repository import ProductRepo
from databases.tenant.models import (
    Node,
    Edge,
    Product,
    Address,
    TenantEmissionsFactor,
    TenantEmissionsFactorValue,
    TenantImpactIndicator,
)
from databases.tenant.constants.product import ProductStatus, ProductType
from services.process_model_service import ProcessModelService
from services.prediction_override_service import PredictionOverrideService
from setup.db import setup_server
from api.app import app
from pipelines_v2.utils.unit import UnitType


client = TestClient(app)

test_product = Product(
    product_id="1234567890",
    product_name="Test Product",
    primary_category="Laundry Detergent",
    brand="Test Brand",
    product_type=ProductType.PRODUCT.value,
    country_of_use="United States",
)

test_component = Product(
    product_id="component-1234567890",
    product_name="Test Component",
    primary_category="Laundry Detergent",
    product_type=ProductType.COMPONENT.value,
)

test_product_process_model = {
    "nodes": [
        Node(
            id=1,
            product_id="1234567890",
            name="Citric Acid",
            amount=100,
            quantity=1,
            unit="g",
            node_type=NodeTypes.MATERIAL.value,
            location=Address(
                city="Los Angeles",
                country="United States",
            ),
            emissions_factor=TenantEmissionsFactor(
                activity_name="Citric Acid Production",
                activity_type="Raw Materials",
                reference_product="Citric Acid",
                reference_product_amount=1,
                unit="kg",
                geography="US",
                source="Custom",
                activity_description="Citric Acid Production Activity",
            ),
            emissions_factor_id=1,
        ),
        Node(
            id=2,
            product_id="1234567890",
            name="Citric Acid Transport",
            quantity=1,
            node_type=NodeTypes.TRANSPORTATION.value,
            segment_type="auto"
        ),
         Node(
            id=5,
            product_id="1234567890",
            name="PET",
            amount=100,
            quantity=1,
            unit="g",
            node_type=NodeTypes.PACKAGING.value,
            location=Address(
                city="Los Angeles",
                country="United States",
            ),
            emissions_factor=TenantEmissionsFactor(
                activity_name="PET Production",
                activity_type="Raw Materials",
                reference_product="PET",
                reference_product_amount=1,
                unit="kg",
                geography="US",
                source="Custom",
                activity_description="PET Production Activity",
            ),
            emissions_factor_id=3,
        ),
        Node(
            id=6,
            product_id="1234567890",
            name="PET Transport",
            quantity=1,
            node_type=NodeTypes.TRANSPORTATION.value,
            segment_type="auto"
        ),
        Node(
            id=3,
            product_id="1234567890",
            name="Product Assemby",
            node_type=NodeTypes.BUNDLE.value,
            location=Address(
                city="Chicago",
                country="United States",
            ),
        ),
        Node(
            id=4,
            product_id="1234567890",
            name="Consumer Use",
            amount=100,
            quantity=1,
            unit="l",
            node_type=NodeTypes.USE.value,
            location=Address(
                city="Chicago",
                country="United States",
            ),
            emissions_factor=TenantEmissionsFactor(
                activity_name="Water Use",
                activity_type="Raw Materials",
                reference_product="Water",
                reference_product_amount=1,
                unit="l",
                geography="US",
                source="Custom",
                activity_description="Water Use Activity",
            ),
            emissions_factor_id=2,
        ),
        Node(
            id=7,
            product_id="1234567890",
            name="End of Life",
            quantity=1,
            node_type=NodeTypes.EOL.value,
            segment_type="auto",
            location=Address(
                city="Chicago",
                country="United States",
            ),
        ),
    ],
    "edges": [
        Edge(
            from_node_id=1,
            to_node_id=2,
        ),
        Edge(
            from_node_id=2,
            to_node_id=3,
        ),
        Edge(
            from_node_id=5,
            to_node_id=6,
        ),
        Edge(
            from_node_id=6,
            to_node_id=3,
        ),
        Edge(
            from_node_id=3,
            to_node_id=4,
        ),
        Edge(
            from_node_id=4,
            to_node_id=7,
        )
    ],
}

class TestProducts(unittest.TestCase):
    tenant_id = "test"

    def setUp(self):
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.execute(f"DROP DATABASE IF EXISTS tenant_{self.tenant_id};")
            session.commit()

        with Session(engine) as session:
            session.execute(f"CREATE DATABASE tenant_{self.tenant_id};")
            session.commit()

        setup_server(engine, [self.tenant_id])

        with Session(engine_pool.get_tenant_engine(self.tenant_id)) as session:
            session.add(
                TenantEmissionsFactor(
                    id=1,
                    activity_name="Citric Acid Production",
                    activity_type="Raw Materials",
                    reference_product="Citric Acid",
                    reference_product_amount=1,
                    unit="kg",
                    geography="US",
                    source="Custom",
                    activity_description="Citric Acid Production Activity",
                )
            )
            session.add(
                TenantImpactIndicator(
                    id=1,
                    lcia_method="ReCiPe 2016 v1.03, midpoint (H)",
                    category="climate change",
                    indicator="global warming potential (GWP100)",
                    unit="kg CO2-Eq",
                    description="",
                )
            )
            session.add(
                TenantEmissionsFactorValue(
                    emissions_factor_id=1,
                    impact_indicator_id=1,
                    amount=2.5,
                )
            )
            session.add(
                TenantEmissionsFactor(
                    id=2,
                    activity_name="Water Use",
                    activity_type="Raw Materials",
                    reference_product="Water",
                    reference_product_amount=1,
                    unit="l",
                    geography="US",
                    source="Custom",
                    activity_description="Water Use Activity",
                )
            )
            session.add(
                TenantEmissionsFactorValue(
                    emissions_factor_id=2,
                    impact_indicator_id=1,
                    amount=3,
                )
            )
            session.add(
                TenantEmissionsFactor(
                    id=3,
                    activity_name="PET Production",
                    activity_type="Raw Materials",
                    reference_product="PET",
                    reference_product_amount=1,
                    unit="kg",
                    geography="US",
                    source="Custom",
                    activity_description="PET Production Activity",
                )
            )
            session.add(
                TenantEmissionsFactorValue(
                    emissions_factor_id=3,
                    impact_indicator_id=1,
                    amount=3,
                )
            )
            session.commit()

    def tearDown(self):
        """Clean up test database after each test."""
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.execute(f"DROP DATABASE IF EXISTS tenant_{self.tenant_id};")
            session.commit()

    def _create_process_model_payload(self, process_model_data):
        """Helper method to create process model payload from process model data."""
        return {
            "nodes": [
                {
                    **node.dict(),
                    "emissions_factor": {
                        "activity_name": node.emissions_factor.activity_name,
                        "geography": node.emissions_factor.geography,
                        "source": node.emissions_factor.source,
                        "reference_product_name": node.emissions_factor.reference_product,
                    } if node.emissions_factor else None,
                    "location": node.location.dict() if node.location else None,
                } for node in process_model_data["nodes"]
            ],
            "edges": [
                {
                    "from_node_id": next(
                        node.id
                        for node in process_model_data["nodes"]
                        if node.id == edge.from_node_id
                    ),
                    "to_node_id": next(
                        node.id
                        for node in process_model_data["nodes"]
                        if node.id == edge.to_node_id
                    ),
                } for edge in process_model_data["edges"]
            ],
        }

    def _create_process_model(self):
        """Create process model for test product."""
        payload = self._create_process_model_payload(test_product_process_model)
        response = client.post(
            f"/v2/products/{self.tenant_id}/{test_product.product_id}/process-model",
            json=payload,
        )
        return response

    def _create_and_activate_test_products(self):
        """Helper method to create and activate test product and component with their
        process models."""
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))
        product_repo.create_product(Product(**test_component.dict()))

        # Create process model for the test product
        create_process_model_response = self._create_process_model()
        self.assertEqual(create_process_model_response.status_code, 200)

        # Create process model for component using the same payload creation method
        component_payload = self._create_process_model_payload(
            test_product_process_model
        )
        create_component_process_model_response = client.post(
            f"/v2/products/{self.tenant_id}/{test_component.product_id}/process-model",
            json=component_payload,
        )
        self.assertEqual(create_component_process_model_response.status_code, 200)

        # Activate both products
        activate_product_response = client.post(
            f"/products/{self.tenant_id}/{test_product.product_id}/activate",
        )
        self.assertEqual(activate_product_response.status_code, 200)

        activate_component_response = client.post(
            f"/products/{self.tenant_id}/{test_component.product_id}/activate",
        )
        self.assertEqual(activate_component_response.status_code, 200)

        return product_repo

    def test_get_all_products(self):
        self._create_and_activate_test_products()
        response_get_all = client.get(
            f"/products/{self.tenant_id}",
        )

        self.assertEqual(response_get_all.status_code, 200)
        self.assertEqual(len(response_get_all.json()["products"]), 2)

    def test_get_products(self):
        self._create_and_activate_test_products()
        response_get_products = client.get(
            f"/products/{self.tenant_id}?product_type={ProductType.PRODUCT.value}",
        )

        self.assertEqual(response_get_products.status_code, 200)
        self.assertEqual(len(response_get_products.json()["products"]), 1)
        self.assertEqual(
            response_get_products.json()["products"][0]["product_id"],
            test_product.product_id
        )
        self.assertEqual(
            response_get_products.json()["products"][0]["product_name"],
            test_product.product_name
        )
        self.assertEqual(
            response_get_products.json()["products"][0]["product_type"],
            ProductType.PRODUCT.value
        )

    def test_get_components(self):
        self._create_and_activate_test_products()
        response_get_components = client.get(
            f"/products/{self.tenant_id}?product_type={ProductType.COMPONENT.value}",
        )

        self.assertEqual(response_get_components.status_code, 200)
        self.assertEqual(len(response_get_components.json()["products"]), 1)
        self.assertEqual(
            response_get_components.json()["products"][0]["product_id"],
            test_component.product_id
        )
        self.assertEqual(
            response_get_components.json()["products"][0]["product_name"],
            test_component.product_name
        )
        self.assertEqual(
            response_get_components.json()["products"][0]["product_type"],
            ProductType.COMPONENT.value
        )

    def test_create_product(self):
        response = client.post(
            f"/products/{self.tenant_id}",
            json=test_product.dict(),
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["product_id"], test_product.product_id)
        self.assertEqual(response.json()["product_name"], test_product.product_name)
        self.assertEqual(response.json()["brand"], test_product.brand)

        product_repo = ProductRepo(self.tenant_id)
        product = product_repo.get_product(test_product.product_id)
        self.assertEqual(product.product_id, test_product.product_id)
        self.assertEqual(product.product_name, test_product.product_name)
        self.assertEqual(product.brand, test_product.brand)

    def test_delete_product(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        delete_product_response = client.delete(
            f"/products/{self.tenant_id}/{test_product.product_id}",
        )

        self.assertEqual(delete_product_response.status_code, 200)

        product = product_repo.get_product(test_product.product_id)
        self.assertIsNone(product)

        nodes, edges = (
            ProcessModelService(self.tenant_id)
            .get_process_model_for_product(test_product.product_id)
        )
        self._assert_process_model_equal(nodes, [], edges, [])

    def test_backup_and_restore_product(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        self._create_process_model()

        backup_product_response = client.put(
            f"/products/{self.tenant_id}/backup-product/{test_product.product_id}",
        )

        self.assertEqual(backup_product_response.status_code, 200)

        product = product_repo.get_product(f"{test_product.product_id}_backup")
        self.assertEqual(product.product_name, f"{test_product.product_name}_backup")
        self.assertEqual(product.status, ProductStatus.TRANSIENT.value)

        restore_product_response = client.put(
            f"/products/{self.tenant_id}/restore-product/{test_product.product_id}_backup",
        )

        self.assertEqual(restore_product_response.status_code, 200)

        product = product_repo.get_product(test_product.product_id)
        self.assertEqual(product.product_name, test_product.product_name)
        self.assertEqual(product.status, ProductStatus.ACTIVE.value)

    def test_create_product_process_model(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        response = self._create_process_model()

        self.assertEqual(response.status_code, 200)

        process_model_service = ProcessModelService(self.tenant_id)
        nodes, edges = process_model_service.get_process_model_for_product(test_product.product_id)
        self._assert_process_model_equal(
            nodes,
            test_product_process_model["nodes"],
            edges,
            test_product_process_model["edges"]
        )

        prediction_override_service = PredictionOverrideService(self.tenant_id)
        for node in test_product_process_model["nodes"]:
            if node.emissions_factor:
                override = prediction_override_service.get_emissions_factor_match_override(
                    node.name,
                )
                self.assertIsNotNone(override)

                self.assertEqual(
                    override.activity_name,
                    node.emissions_factor.activity_name,
                )
                self.assertEqual(
                    override.geography,
                    node.emissions_factor.geography,
                )
                self.assertEqual(
                    override.reference_product,
                    node.emissions_factor.reference_product,
                )
                self.assertEqual(
                    override.source,
                    node.emissions_factor.source,
                )

    def test_product_weight_calculation(self):
        """Test that product weights are correctly calculated after creating a process model."""
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        # Create process model with known weights
        self._create_process_model()

        # Calculate expected weights from test_product_process_model
        material_weight = sum(
            UnitType.convert_from_abbrev(
                node.amount,
                node.unit,
                "g"
            ) * node.quantity
            for node in test_product_process_model["nodes"]
            if node.node_type == NodeTypes.MATERIAL.value
            and node.amount is not None
        )  # 100g * 1 = 100g

        packaging_weight = sum(
            UnitType.convert_from_abbrev(
                node.amount,
                node.unit,
                "g"
            ) * node.quantity
            for node in test_product_process_model["nodes"]
            if node.node_type == NodeTypes.PACKAGING.value
            and node.amount is not None
        )  # 100g * 1 = 100g

        expected_total_weight = material_weight + packaging_weight  # 200g

        # Get product and verify weights
        product = product_repo.get_product(test_product.product_id)
        self.assertEqual(product.total_weight, expected_total_weight)
        self.assertEqual(product.content_weight, material_weight)

    def test_get_product_process_model_cache(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        self._create_process_model()

        response = client.get(
            f"/v2/products/{self.tenant_id}/{test_product.product_id}/process-model/walk",
        )
        self.assertEqual(response.status_code, 200)

        product = product_repo.get_product(test_product.product_id)
        self.assertIsNotNone(product.lca_output)

    def test_product_process_model_cache_invalidation(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        create_process_model_response = self._create_process_model()
        self.assertEqual(create_process_model_response.status_code, 200)

        response = client.get(
            f"/v2/products/{self.tenant_id}/{test_product.product_id}/process-model/walk",
        )
        self.assertEqual(response.status_code, 200)

        product = product_repo.get_product(test_product.product_id)
        self.assertIsNotNone(product.lca_output)

        response = client.delete(
            f"/v2/products/{self.tenant_id}/{test_product.product_id}/process-model/",
        )

        self.assertEqual(response.status_code, 200)

        product = product_repo.get_product(test_product.product_id)
        self.assertIsNone(product.lca_output)

    def test_clone_product(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        self._create_process_model()

        response = client.post(
            f"/products/{self.tenant_id}/{test_product.product_id}/clone-product",
            json={},
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["product_id"], f"{test_product.product_id}-CLONE-1")
        self.assertEqual(response.json()["product_name"], f"{test_product.product_name} (COPY)")
        self.assertEqual(response.json()["cloned_from_product_id"], test_product.product_id)

        nodes, edges = (
            ProcessModelService(self.tenant_id)
            .get_process_model_for_product(f"{test_product.product_id}-CLONE-1")
        )
        expected_nodes = [
            Node(
                **node.dict(exclude={"product_id": True}),
                product_id=f"{test_product.product_id}-CLONE-1",
            )
            for node in test_product_process_model["nodes"]
        ]
        self._assert_process_model_equal(
            nodes,
            expected_nodes,
            edges,
            test_product_process_model["edges"]
        )

    def test_download_lca_report(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_product.dict()))

        self._create_process_model()

        response = client.post(
            f"/products/{self.tenant_id}/{test_product.product_id}/download-lca-report",
        )

        self.assertEqual(response.status_code, 200)

    def test_download_lca_report_for_component(self):
        product_repo = ProductRepo(self.tenant_id)
        product_repo.create_product(Product(**test_component.dict()))

        response = client.post(
            f"/products/{self.tenant_id}/{test_component.product_id}/download-lca-report",
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(
            response.json()["detail"],
            f"LCA report is not available for products of type '{ProductType.COMPONENT.value}'."
        )

    def test_create_product_with_duplicate_name(self):
        # First create a product
        response = client.post(
            f"/products/{self.tenant_id}",
            json=test_product.dict(),
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["product_id"], test_product.product_id)
        self.assertEqual(response.json()["product_name"], test_product.product_name)

        # Create another product with the same name but different ID
        duplicate_product = Product(
            product_id=test_product.product_id + "-duplicate",
            product_name=test_product.product_name,  # Same name as test_product
            primary_category="Laundry Detergent",
            brand="Test Brand",
            product_type=ProductType.PRODUCT.value,
            country_of_use="Canada",
        )

        response = client.post(
            f"/products/{self.tenant_id}",
            json=duplicate_product.dict(),
        )

        # Verify that creating a product with the same name but different ID is allowed
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()["product_id"], duplicate_product.product_id)
        self.assertEqual(response.json()["product_name"], duplicate_product.product_name)

        # Verify both products exist in the database
        product_repo = ProductRepo(self.tenant_id)
        first_product = product_repo.get_product(test_product.product_id)
        second_product = product_repo.get_product(duplicate_product.product_id)

        self.assertIsNotNone(first_product)
        self.assertIsNotNone(second_product)
        self.assertEqual(first_product.product_name, second_product.product_name)
        self.assertNotEqual(first_product.product_id, second_product.product_id)
        self.assertNotEqual(first_product.country_of_use, second_product.country_of_use)

    def _assert_process_model_equal(self, nodes, nodes_expected, edges, edges_expected):
        self.assertEqual(
            len(
                [
                    node
                    for node in nodes
                    if node.node_type not in [NodeTypes.TRANSPORTATION.value, NodeTypes.EOL.value]
                ]
            ),
            len(
                [
                    node
                    for node in nodes_expected
                    if node.node_type not in [NodeTypes.TRANSPORTATION.value, NodeTypes.EOL.value]
                ]
            ),
        )

        self.assertGreaterEqual(len(edges), len(edges_expected))
