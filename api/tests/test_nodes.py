from unittest import TestCase
from fastapi.testclient import TestClient
from api.routers import nodes_router
from databases.shared.constants.node_type import NodeTypes
from pipelines_v2.utils.unit import UnitType

client = TestClient(nodes_router)

class TestNodes(TestCase):
    def test_get_all_units(self):
        response = client.get("/units")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), len(UnitType.get_all()))

    def test_get_units_by_node_type(self):
        response = client.get(f"/units?node_type={NodeTypes.MATERIAL.value}")
        self.assertEqual(response.status_code, 200)

        units = response.json()

        for unit in units:
            self.assertIn(unit["category"], ["weight", "volume"])

        response = client.get(f"/units?node_type={NodeTypes.TRANSPORTATION.value}")
        self.assertEqual(response.status_code, 200)

        units = response.json()
        for unit in units:
            self.assertEqual(unit["category"], "mass-distance")

    def test_get_node_types(self):
        response = client.get("/node-types")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), len(NodeTypes))

        node_types = response.json()
        for node_type in node_types:
            self.assertIn(node_type["name"], [node_type.value for node_type in NodeTypes])
            self.assertIsNotNone(node_type["min_inputs"])
            self.assertIsNotNone(node_type["min_outputs"])
