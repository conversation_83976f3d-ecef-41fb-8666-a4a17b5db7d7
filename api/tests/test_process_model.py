# pylint: disable=protected-access,import-outside-toplevel,too-many-lines,line-too-long
import unittest
from typing import Dict, List

from fastapi.testclient import TestClient
from sqlmodel import Session

from databases.engine import engine_pool
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.constants.product import ProductType
from databases.tenant.models import Product
from pipelines_v2.process_model_walker.tests.test_helpers import NodeBuilder
from pipelines_v2.utils.unit import UnitType
from setup.db import setup_server
from utils.logger import logger
from api.app import app


client = TestClient(app)

test_product = Product(
    product_id="1234567890",
    product_name="Test Product",
    primary_category="Laundry Detergent",
    brand="Test Brand",
    product_type=ProductType.PRODUCT.value,
    country_of_use="United States",
)

test_nodes = [
    {
        "id": 1,
        "node_type": "bundle",
        "quantity": 1,
        "name": "Vibrant Colors Laundry Plus",
        "location": {
            "city": "London",
            "country": "United Kingdom"
        },
    },
    {
        "id": 2,
        "node_type": "material",
        "quantity": 1,
        "name": "Sodium Laurylglucosides Hydroxypropylsulfonate - Horizon Chemical Group",
        "amount": 0.8,
        "unit": "g",
        "emissions_factor": {
            "activity_name": "non-ionic surfactant production, ethylene oxide derivate",
            "reference_product_name": "non-ionic surfactant",
            "geography": "GLO",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Paris",
            "country": "France"
        }
    },
    {
        "id": 3,
        "node_type": "transportation",
        "quantity": 1,
        "name": "Sodium Laurylglucosides Hydroxypropylsulfonate - Horizon Chemical Group transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 4,
        "node_type": "material",
        "quantity": 1,
        "name": "Lauramine Oxide - Precision Chemicals Ltd.",
        "amount": 0.294,
        "unit": "g",
        "emissions_factor": {
            "activity_name": "amine oxides production",
            "reference_product_name": "amine oxides",
            "geography": "RoW",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Beijing",
            "country": "China"
        }
    },
    {
        "id": 5,
        "node_type": "transportation",
        "quantity": 1,
        "name": "Lauramine Oxide - Precision Chemicals Ltd. transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 6,
        "node_type": "material",
        "quantity": 1,
        "name": "Decyl Glucoside - Advanced Solvents & Reagents Co.",
        "amount": 0.14,
        "unit": "g",
        "emissions_factor": {
            "activity_name": "cocamide diethanolamine production",
            "reference_product_name": "cocamide diethanolamine",
            "geography": "RoW",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Mumbai",
            "country": "India"
        }
    },
    {
        "id": 7,
        "node_type": "transportation",
        "quantity": 1,
        "name": "Decyl Glucoside - Advanced Solvents & Reagents Co. transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 8,
        "node_type": "material",
        "quantity": 1,
        "name": "Propanediol - Precision Chemicals Ltd.",
        "amount": 0.11,
        "unit": "g",
        "emissions_factor": {
            "activity_name": "propylene glycol production, liquid",
            "reference_product_name": "propylene glycol, liquid",
            "geography": "RoW",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Mumbai",
            "country": "India"
        }
    },
    {
        "id": 9,
        "node_type": "transportation",
        "quantity": 1,
        "name": "Propanediol - Precision Chemicals Ltd. transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 10,
        "node_type": "material",
        "quantity": 1,
        "name": "Citric Acid - Horizon Chemical Group",
        "amount": 0.02,
        "unit": "g",
        "emissions_factor": {
            "activity_name": "citric acid production",
            "reference_product_name": "maize starch",
            "geography": "RoW",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Mumbai",
            "country": "India"
        }
    },
    {
        "id": 11,
        "node_type": "transportation",
        "quantity": 1,
        "name": "Citric Acid - Horizon Chemical Group transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 12,
        "node_type": "packaging",
        "quantity": 1,
        "name": "PET",
        "amount": 0.6,
        "unit": "g",
        "packaging_level": "primary",
        "component_name": "PET packaging",
        "emissions_factor": {
            "activity_name": "polyethylene terephthalate, granulate, bottle grade, recycled to generic market for PET, granulate, bottle grade",
            "reference_product_name": "polyethylene terephthalate, granulate, bottle grade",
            "geography": "GLO",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Paris",
            "country": "France"
        }
    },
    {
        "id": 13,
        "node_type": "transportation",
        "quantity": 1,
        "name": "PET transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 14,
        "node_type": "packaging",
        "quantity": 1,
        "name": "PP",
        "amount": 0.08,
        "unit": "g",
        "packaging_level": "primary",
        "component_name": "PP packaging",
        "emissions_factor": {
            "activity_name": "polypropylene, pellets, recycled to generic market for polypropylene, granulate",
            "reference_product_name": "polypropylene, granulate",
            "geography": "RER",
            "source": "Ecoinvent 3.11",
        },
        "location": {
            "city": "Paris",
            "country": "France"
        }
    },
    {
        "id": 15,
        "node_type": "transportation",
        "quantity": 1,
        "name": "PP transportation",
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 16,
        "name": 'Product Assembly',
        "node_type": 'bundle',
        "location": {
            "city": 'London',
            "country": 'United Kingdom',
        },
        "quantity": 1,
    },
    {
        "id": 17,
        "name": 'Factory to Retail',
        "node_type": 'transportation',
        "quantity": 1,
        "emissions_factor": None,
        "location": None
    },
    {
        "id": 18,
        "name": 'Consumer Use',
        "node_type": 'use',
        "amount": 0,
        "quantity": 1,
        "unit": 'kg',
        "location": {
            "city": 'Washington',
            "country": 'United States',
        },
        "emissions_factor": {
            "activity_name": 'water production, deionised',
            "reference_product_name": 'water, deionised',
            "geography": 'RoW',
            "source": 'Ecoinvent 3.11',
        },
    },
    {
        "id": 19,
        "name": 'End of Life',
        "node_type": 'eol',
        "amount": 0,
        "quantity": 1,
        "unit": 'kg',
        "emissions_factor": None,
        "location": {
            "city": 'Washington',
            "country": 'United States',
        },
    }
]

test_edges = [
    {
        "from_node_id": 2,
        "to_node_id": 3,
    },
    {
        "from_node_id": 3,
        "to_node_id": 1,
    },
    {
        "from_node_id": 4,
        "to_node_id": 5,
    },
    {
        "from_node_id": 5,
        "to_node_id": 1,
    },
    {
        "from_node_id": 6,
        "to_node_id": 7,
    },
    {
        "from_node_id": 7,
        "to_node_id": 1,
    },
    {
        "from_node_id": 8,
        "to_node_id": 9,
    },
    {
        "from_node_id": 9,
        "to_node_id": 1,
    },
    {
        "from_node_id": 10,
        "to_node_id": 11,
    },
    {
        "from_node_id": 11,
        "to_node_id": 1,
    },
    {
        "from_node_id": 12,
        "to_node_id": 13,
    },
    {
        "from_node_id": 13,
        "to_node_id": 1,
    },
    {
        "from_node_id": 14,
        "to_node_id": 15,
    },
    {
        "from_node_id": 15,
        "to_node_id": 1,
    },
    {
        "from_node_id": 1,
        "to_node_id": 16,
    },
    {
        "from_node_id": 16,
        "to_node_id": 17,
    },
    {
        "from_node_id": 17,
        "to_node_id": 18,
    },
    {
        "from_node_id": 18,
        "to_node_id": 19,
    }
]

expected_walk_results = {
    "default": {
        "materials": {
            "sequence_number": 1,
            "segment_nodes": [
                {
                    "node_id": 1,
                    "node_name": "Sodium Laurylglucosides Hydroxypropylsulfonate - Horizon Chemical Group",
                    "amount": 0.0008,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 1,
                                    "activity_name": "non-ionic surfactant production, ethylene oxide derivate",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "non-ionic surfactant",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 4.2697
                            },
                            "impact_amount": 0.0034157600000000003
                        }
                    ]
                },
                {
                    "node_id": 2,
                    "node_name": "Lauramine Oxide - Precision Chemicals Ltd.",
                    "amount": 0.000294,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 9,
                                    "activity_name": "amine oxides production",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "amine oxides",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 5.27185
                            },
                            "impact_amount": 0.0015499238999999998
                        }
                    ]
                },
                {
                    "node_id": 3,
                    "node_name": "Decyl Glucoside - Advanced Solvents & Reagents Co.",
                    "amount": 0.00014000000000000001,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 12,
                                    "activity_name": "cocamide diethanolamine production",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "cocamide diethanolamine",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 4.42972
                            },
                            "impact_amount": 0.0006201608
                        }
                    ]
                },
                {
                    "node_id": 4,
                    "node_name": "Propanediol - Precision Chemicals Ltd.",
                    "amount": 0.00011,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 17,
                                    "activity_name": "propylene glycol production, liquid",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "propylene glycol, liquid",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 5.30201
                            },
                            "impact_amount": 0.0005832211
                        }
                    ]
                },
                {
                    "node_id": 5,
                    "node_name": "Citric Acid - Horizon Chemical Group",
                    "amount": 2e-05,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 19,
                                    "activity_name": "citric acid production",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "maize starch",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 1.24804
                            },
                            "impact_amount": 2.4960800000000003e-05
                        }
                    ]
                }
            ]
        },
        "packaging": {
            "sequence_number": 2,
            "segment_nodes": [
                {
                    "node_id": 6,
                    "node_name": "PET",
                    "amount": 0.0006,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 20,
                                    "activity_name": "polyethylene terephthalate, granulate, bottle grade, recycled to generic market for PET, granulate, bottle grade",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "polyethylene terephthalate, granulate, bottle grade",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 1.07943
                            },
                            "impact_amount": 0.0006476579999999998
                        }
                    ]
                },
                {
                    "node_id": 7,
                    "node_name": "PP",
                    "amount": 8e-05,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 22,
                                    "activity_name": "polypropylene, pellets, recycled to generic market for polypropylene, granulate",
                                    "geography": "RER",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "polypropylene, granulate",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 1.40377
                            },
                            "impact_amount": 0.00011230160000000001
                        }
                    ]
                }
            ]
        },
        "transportation": {
            "sequence_number": 3,
            "segment_nodes": [
                {
                    "node_id": 8,
                    "node_name": "Sodium Laurylglucosides Hydroxypropylsulfonate - Horizon Chemical Group Transportation Segment 1",
                    "amount": 0.00036712800000000004,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 4.019280631200001e-05
                        }
                    ]
                },
                {
                    "node_id": 9,
                    "node_name": "Lauramine Oxide - Precision Chemicals Ltd. Transportation Segment 1",
                    "amount": 4.0262123999999994e-05,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 4.407857073396e-06
                        }
                    ]
                },
                {
                    "node_id": 10,
                    "node_name": "Decyl Glucoside - Advanced Solvents & Reagents Co. Transportation Segment 1",
                    "amount": 4.23983e-08,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 4.641723485700001e-09
                        }
                    ]
                },
                {
                    "node_id": 11,
                    "node_name": "Propanediol - Precision Chemicals Ltd. Transportation Segment 1",
                    "amount": 3.331295e-08,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 3.64706845305e-09
                        }
                    ]
                },
                {
                    "node_id": 12,
                    "node_name": "Citric Acid - Horizon Chemical Group Transportation Segment 1",
                    "amount": 6.0569000000000005e-09,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 6.631033551000001e-10
                        }
                    ]
                },
                {
                    "node_id": 13,
                    "node_name": "PET Transportation Segment 1",
                    "amount": 0.000275346,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 3.0144604734000002e-05
                        }
                    ]
                },
                {
                    "node_id": 14,
                    "node_name": "PP Transportation Segment 1",
                    "amount": 3.6712800000000005e-05,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 4.019280631200001e-06
                        }
                    ]
                },
                {
                    "node_id": 16,
                    "node_name": "Lauramine Oxide - Precision Chemicals Ltd. Transportation Segment 2",
                    "amount": 0.0035188566,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 44,
                                    "activity_name": "transport, freight, sea, container ship, heavy fuel oil",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, sea, container ship, heavy fuel oil",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0104205
                            },
                            "impact_amount": 3.66682452003e-05
                        }
                    ]
                },
                {
                    "node_id": 17,
                    "node_name": "Decyl Glucoside - Advanced Solvents & Reagents Co. Transportation Segment 2",
                    "amount": 0.0014599060000000002,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 44,
                                    "activity_name": "transport, freight, sea, container ship, heavy fuel oil",
                                    "geography": "GLO",
                                    "sourceol_material_classificatione": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, sea, container ship, heavy fuel oil",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0104205
                            },
                            "impact_amount": 1.5212950473000001e-05
                        }
                    ]
                },
                {
                    "node_id": 18,
                    "node_name": "Propanediol - Precision Chemicals Ltd. Transportation Segment 2",
                    "amount": 0.001147069,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 44,
                                    "activity_name": "transport, freight, sea, container ship, heavy fuel oil",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, sea, container ship, heavy fuel oil",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0104205
                            },
                            "impact_amount": 1.1953032514499997e-05
                        }
                    ]
                },
                {
                    "node_id": 19,
                    "node_name": "Citric Acid - Horizon Chemical Group Transportation Segment 2",
                    "amount": 0.00020855800000000002,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 44,
                                    "activity_name": "transport, freight, sea, container ship, heavy fuel oil",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, sea, container ship, heavy fuel oil",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0104205
                            },
                            "impact_amount": 2.173278639e-06
                        }
                    ]
                },
                {
                    "node_id": 21,
                    "node_name": "Lauramine Oxide - Precision Chemicals Ltd. Transportation Segment 3",
                    "amount": 0.0,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 0.0
                        }
                    ]
                },
                {
                    "node_id": 22,
                    "node_name": "Decyl Glucoside - Advanced Solvents & Reagents Co. Transportation Segment 3",
                    "amount": 0.0,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 0.0
                        }
                    ]
                },
                {
                    "node_id": 23,
                    "node_name": "Propanediol - Precision Chemicals Ltd. Transportation Segment 3",
                    "amount": 0.0,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 0.0
                        }
                    ]
                },
                {
                    "node_id": 24,
                    "node_name": "Citric Acid - Horizon Chemical Group Transportation Segment 3",
                    "amount": 0.0,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 0.0
                        }
                    ]
                },
                {
                    "node_id": 25,
                    "node_name": "Product Assembly Transportation Segment 1",
                    "amount": 0.0,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 0.0
                        }
                    ]
                },
                {
                    "node_id": 26,
                    "node_name": "Product Assembly Transportation Segment 2",
                    "amount": 0.022053738,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 44,
                                    "activity_name": "transport, freight, sea, container ship, heavy fuel oil",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, sea, container ship, heavy fuel oil",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0104205
                            },
                            "impact_amount": 0.00022981097682899998
                        }
                    ]
                },
                {
                    "node_id": 27,
                    "node_name": "Product Assembly Transportation Segment 3",
                    "amount": 0.00039894383199999995,
                    "unit": "kilometers",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 35,
                                    "activity_name": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "transport, freight, lorry, >32 metric ton, diesel, EURO 6",
                                    "reference_product_amount": 1.0,
                                    "unit": "metric ton*km",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.109479
                            },
                            "impact_amount": 4.3675971783527994e-05
                        }
                    ]
                }
            ]
        },
        "use": {
            "sequence_number": 5,
            "segment_nodes": [
                {
                    "node_id": 28,
                    "node_name": "Consumer Use",
                    "amount": 0.0,
                    "unit": "kg",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 24,
                                    "activity_name": "water production, deionised",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "water, deionised",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.000489347
                            },
                            "impact_amount": 0.0
                        }
                    ]
                }
            ]
        },
        "eol": {
            "sequence_number": 6,
            "segment_nodes": [
                {
                    "node_id": 29,
                    "node_name": "PP disposal (incineration)",
                    "amount": 1.9359999999999998e-05,
                    "unit": "g",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 50,
                                    "activity_name": "treatment of waste polypropylene, municipal incineration",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "waste polypropylene",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 2.61807
                            },
                            "impact_amount": 5.0685835199999994e-05
                        }
                    ]
                },
                {
                    "node_id": 30,
                    "node_name": "PP disposal (landfilling)",
                    "amount": 2.544e-05,
                    "unit": "g",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 62,
                                    "activity_name": "treatment of waste polypropylene, sanitary landfill",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "waste polypropylene",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.117414
                            },
                            "impact_amount": 2.98701216e-06
                        }
                    ]
                },
                {
                    "node_id": 31,
                    "node_name": "PP disposal (recycling)",
                    "amount": 3.52e-05,
                    "unit": "g",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 80,
                                    "activity_name": "waste polypropylene, for recycling, unsorted, Recycled Content cut-off",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "waste polypropylene, for recycling, unsorted",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0
                            },
                            "impact_amount": 0.0
                        }
                    ]
                },
                {
                    "node_id": 32,
                    "node_name": "PET disposal (incineration)",
                    "amount": 0.0001452,
                    "unit": "g",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 81,
                                    "activity_name": "treatment of waste polyethylene terephthalate, municipal incineration",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "waste polyethylene terephthalate",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 2.06946
                            },
                            "impact_amount": 0.000300485592
                        }
                    ]
                },
                {
                    "node_id": 33,
                    "node_name": "PET disposal (landfilling)",
                    "amount": 0.0001908,
                    "unit": "g",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 84,
                                    "activity_name": "treatment of waste polyethylene terephthalate, sanitary landfill",
                                    "geography": "RoW",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "waste polyethylene terephthalate",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0940195
                            },
                            "impact_amount": 1.7938920600000003e-05
                        }
                    ]
                },
                {
                    "node_id": 34,
                    "node_name": "PET disposal (recycling)",
                    "amount": 0.000264,
                    "unit": "g",
                    "impacts": [
                        {
                            "emissions_factor_value": {
                                "emissions_factor": {
                                    "id": 86,
                                    "activity_name": "waste polyethylene terephthalate, for recycling, sorted, Recycled Content cut-off",
                                    "geography": "GLO",
                                    "source": "Ecoinvent 3.11",
                                    "activity_type": "Raw Materials",
                                    "reference_product": "waste polyethylene terephthalate, for recycling, sorted",
                                    "reference_product_amount": 1.0,
                                    "unit": "kg",
                                    "activity_description": ""
                                },
                                "impact_indicator": {
                                    "id": 1,
                                    "lcia_method": "ReCiPe 2016 v1.03, midpoint (H)",
                                    "category": "climate change",
                                    "indicator": "global warming potential (GWP100)",
                                    "unit": "kg CO2-Eq",
                                    "description": ""
                                },
                                "amount": 0.0
                            },
                            "impact_amount": 0.0
                        }
                    ]
                }
            ]
        }
    }
}
class ScrapRateTestBuilder:
    """Builder for creating a test graph to verify scrap rate calculations."""

    def __init__(self, scrap_rate: float = 3.5, scrap_fate: str = "Recycled"):
        self.builder = NodeBuilder()
        self.scrap_rate = scrap_rate
        self.scrap_fate = scrap_fate
        self._edges = []

    def build(self) -> Dict:
        """Build the test graph with the following structure:

        Metal (Steel) --> Transport --> Die Cast (3.5% scrap) -----> Machined -----> Bundle
                                      ^
        Recycled Al    ---> Bundle -> Transport --|
        Non-recycled Al --> ^
        """
        # Create main process flow nodes
        bundle = self.builder.create_node("Nut & Bolt, Unpainted bundle", NodeTypes.BUNDLE.value)
        die_cast_w_scrap = self.builder.create_node(
            "Die Cast",
            NodeTypes.PRODUCTION.value,
            emissions_factor=self._get_die_cast_ef(),
            scrap_rate=self.scrap_rate,
            scrap_fate=self.scrap_fate
        )
        machined = self.builder.create_node(
            "Machined / Milled",
            NodeTypes.PRODUCTION.value,
            emissions_factor=self._get_milling_ef()
        )

        # Create material flow nodes
        recycled_al = self.builder.create_node(
            "Metal (Aluminum) (recycled)",
            NodeTypes.MATERIAL.value,
            amount=353.832984,
            emissions_factor=self._get_recycled_al_ef()
        )
        non_recycled_al = self.builder.create_node(
            "Metal (Aluminum) (non-recycled)",
            NodeTypes.MATERIAL.value,
            amount=825.610296,
            emissions_factor=self._get_primary_al_ef()
        )
        steel = self.builder.create_node(
            "Metal (Steel)",
            NodeTypes.MATERIAL.value,
            amount=500.54,
            emissions_factor=self._get_primary_al_ef()  # Using same EF for simplicity
        )
        steel_transport = self.builder.create_node("Metal (Steel) transportation", NodeTypes.TRANSPORTATION.value)
        al_bundle = self.builder.create_node("Metal (Aluminum) bundle", NodeTypes.BUNDLE.value)
        al_transport = self.builder.create_node("Metal (Aluminum) transportation", NodeTypes.TRANSPORTATION.value)

        # Create edges
        self._add_edges([
            # Main production flow
            (die_cast_w_scrap, machined),
            (machined, bundle),
            # Aluminum material flow
            (recycled_al, al_bundle),
            (non_recycled_al, al_bundle),
            (al_bundle, al_transport),
            # Connect materials to production
            (steel, steel_transport),
            (steel_transport, die_cast_w_scrap),
            (al_transport, die_cast_w_scrap)
        ])

        return {
            "nodes": self.builder.get_nodes(),
            "edges": self._edges
        }

    def _add_edges(self, node_pairs: List[tuple]) -> None:
        """Add edges between pairs of nodes."""
        for from_node, to_node in node_pairs:
            self._edges.append({
                "from_node_id": from_node["id"],
                "to_node_id": to_node["id"]
            })

    def _get_die_cast_ef(self) -> Dict:
        return {
            "activity_name": "magnesium-alloy production, AZ91, diecasting",
            "reference_product_name": "magnesium-alloy, AZ91, diecast",
            "geography": "RoW",
            "source": "Ecoinvent 3.11"
        }

    def _get_milling_ef(self) -> Dict:
        return {
            "activity_name": "steel milling, large parts",
            "reference_product_name": "steel removed by milling, large parts",
            "geography": "RoW",
            "source": "Ecoinvent 3.11"
        }

    def _get_recycled_al_ef(self) -> Dict:
        return {
            "activity_name": "treatment of aluminium scrap, post-consumer, prepared for recycling, at remelter",
            "reference_product_name": "aluminium, wrought alloy",
            "geography": "RoW",
            "source": "Ecoinvent 3.11"
        }

    def _get_primary_al_ef(self) -> Dict:
        return {
            "activity_name": "aluminium, ingot, primary, import from unspecified",
            "reference_product_name": "aluminium, primary, ingot",
            "geography": "IAI Area, North America",
            "source": "Ecoinvent 3.11"
        }


class TestProcessModel(unittest.TestCase):
    tenant_id = "test"

    def setUp(self):
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.execute(f"DROP DATABASE IF EXISTS tenant_{self.tenant_id};")
            session.commit()

        with Session(engine) as session:
            session.execute(f"CREATE DATABASE tenant_{self.tenant_id};")
            session.commit()

        setup_server(engine, [self.tenant_id])

    def tearDown(self):
        engine = engine_pool.get_root_engine()
        with Session(engine) as session:
            session.execute(f"DROP DATABASE IF EXISTS tenant_{self.tenant_id};")
            session.commit()

        result = self._outcome.result
        current_test_failed = not result.wasSuccessful()

        if current_test_failed:
            logger.error(f"Test failed: {self._testMethodName}")
            return

        test_method_name = self._testMethodName
        logger.success(f"Test passed: {test_method_name}")

    def test_process_model_correctness(self):
        response = client.post(
            f"/products/{self.tenant_id}",
            json=test_product.dict(),
        )

        self.assertEqual(response.status_code, 200)

        payload = {
            "nodes": test_nodes,
            "edges": test_edges
        }

        response = client.post(
            f"/v2/products/{self.tenant_id}/{test_product.product_id}/process-model",
            json=payload,
        )

        self.assertEqual(response.status_code, 200)

        #verify if factory location was updated from Product Assembly bundle node
        product_info = client.get(
            f"/products/{self.tenant_id}/{test_product.product_id}",
        )
        self.assertEqual(product_info.status_code, 200)
        product_data = product_info.json()

        # Verify factory location matches the Product Assembly bundle node location
        self.assertEqual(product_data["product"]["factory_locale"]["city"], "London")
        self.assertEqual(product_data["product"]["factory_locale"]["country"], "United Kingdom")

        response = client.get(
            f"/v2/products/{self.tenant_id}/{test_product.product_id}/process-model/walk",
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()

        for node in test_nodes:
            if node["node_type"] == "bundle":
                continue

            # Transform node type to segment key where "material" corresponds to "materials"
            segment_key = {
                "material": "materials"
            }.get(node["node_type"], node["node_type"])

            if node["node_type"] not in ["transportation", "eol"]:
                expected_emissions = next(
                    node_ for node_ in expected_walk_results["default"][segment_key]["segment_nodes"]
                    if node_["node_name"] == node["name"]
                )

                walker_emissions = next(
                    node_ for node_ in data["default"][segment_key]["segment_nodes"]
                    if node_["node_name"] == node["name"]
                )

                self.assertEqual(walker_emissions["amount"], expected_emissions["amount"])
                self.assertEqual(walker_emissions["unit"], expected_emissions["unit"])

                self.assertEqual(walker_emissions["impacts"][0]["impact_amount"], expected_emissions["impacts"][0]["impact_amount"])

                self._assert_dict_fields_equal(
                    walker_emissions["impacts"][0]["emissions_factor_value"]["emissions_factor"],
                    expected_emissions["impacts"][0]["emissions_factor_value"]["emissions_factor"],
                    ["activity_name", "geography", "source", "reference_product"]
                )

                self._assert_dict_fields_equal(
                    walker_emissions["impacts"][0]["emissions_factor_value"]["impact_indicator"],
                    expected_emissions["impacts"][0]["emissions_factor_value"]["impact_indicator"],
                    ["lcia_method", "category", "indicator", "unit"]
                )

            if node["node_type"] == "transportation":
                expected_emissions_nodes = [
                    node_ for node_ in expected_walk_results["default"]["transportation"]["segment_nodes"]
                    if node_["node_name"].startswith(node["name"])
                ]

                walker_emissions_nodes = [
                    node_ for node_ in data["default"]["transportation"]["segment_nodes"]
                    if node_["node_name"].startswith(node["name"])
                ]

                self.assertEqual(len(walker_emissions_nodes), len(expected_emissions_nodes))

                for walker_emissions, expected_emissions in zip(walker_emissions_nodes, expected_emissions_nodes):
                    self._assert_percent_difference(walker_emissions["amount"], expected_emissions["amount"])
                    self.assertEqual(walker_emissions["unit"], expected_emissions["unit"])

                    self._assert_percent_difference(walker_emissions["impacts"][0]["impact_amount"], expected_emissions["impacts"][0]["impact_amount"])

                    self._assert_dict_fields_equal(
                        walker_emissions["impacts"][0]["emissions_factor_value"]["emissions_factor"],
                        expected_emissions["impacts"][0]["emissions_factor_value"]["emissions_factor"],
                        ["activity_name", "geography", "source", "reference_product"]
                    )

                    self._assert_dict_fields_equal(
                        walker_emissions["impacts"][0]["emissions_factor_value"]["impact_indicator"],
                        expected_emissions["impacts"][0]["emissions_factor_value"]["impact_indicator"],
                        ["lcia_method", "category", "indicator", "unit"]
                    )

            if node["node_type"] == "eol":
                expected_emissions_nodes = [
                    node_ for node_ in expected_walk_results["default"]["eol"]["segment_nodes"]
                    if node_["node_name"].startswith(node["name"])
                ]

                walker_emissions_nodes = [
                    node_ for node_ in data["default"]["eol"]["segment_nodes"]
                    if node_["node_name"].startswith(node["name"])
                ]

                self.assertEqual(len(walker_emissions_nodes), len(expected_emissions_nodes))

                for walker_emissions, expected_emissions in zip(walker_emissions_nodes, expected_emissions_nodes):
                    self.assertEqual(walker_emissions["amount"], expected_emissions["amount"])
                    self.assertEqual(walker_emissions["unit"], expected_emissions["unit"])

                    self.assertEqual(walker_emissions["impacts"][0]["impact_amount"], expected_emissions["impacts"][0]["impact_amount"])

                    self._assert_dict_fields_equal(
                        walker_emissions["impacts"][0]["emissions_factor_value"]["emissions_factor"],
                        expected_emissions["impacts"][0]["emissions_factor_value"]["emissions_factor"],
                        ["activity_name", "geography", "source", "reference_product"]
                    )

                    self._assert_dict_fields_equal(
                        walker_emissions["impacts"][0]["emissions_factor_value"]["impact_indicator"],
                        expected_emissions["impacts"][0]["emissions_factor_value"]["impact_indicator"],
                        ["lcia_method", "category", "indicator", "unit"]
                    )

    def test_scrap_rate(self):
        test_product_id = "123456789123"
        scrap_rate = 3.5
        scrap_fate = "Recycled"
        response = client.post(
            f"/products/{self.tenant_id}",
            json={
                "product_id": test_product_id,
                "product_name": "Test Product with scrap rate",
                **test_product.dict(exclude={"product_id", "product_name"}),
            },
        )

        self.assertEqual(response.status_code, 200)

        # Create test graph using the builder
        test_builder = ScrapRateTestBuilder(scrap_rate=scrap_rate, scrap_fate=scrap_fate)
        payload = test_builder.build()

        response = client.post(
            f"/v2/products/{self.tenant_id}/{test_product_id}/process-model",
            json=payload,
        )

        self.assertEqual(response.status_code, 200)

        response = client.get(
            f"/v2/products/{self.tenant_id}/{test_product_id}/process-model/walk",
        )

        self.assertEqual(response.status_code, 200)
        data = response.json()

        input_material_nodes = [node for node in payload["nodes"] if node["node_type"] == "material"]

        for input_material in input_material_nodes:
            output_material_node = [node for node in data["default"]["materials"]["segment_nodes"] if node["node_name"] == input_material["name"]][0]
            scrap_disposal_node_name = f"{input_material['name']} scrap disposal ({scrap_fate})"
            scrap_disposal_node = [node for node in data["default"]["eol"]["segment_nodes"] if node["node_name"] == scrap_disposal_node_name]
            if not scrap_disposal_node:
                continue
            scrap_disposal_node = scrap_disposal_node[0]
            input_material_amount_kg = UnitType.convert_from_abbrev(
                input_material["amount"],
                input_material["unit"],
                "kg"
            )
            # verify material node amount gets added
            self.assertAlmostEqual(output_material_node["amount"], (input_material_amount_kg * (1 + scrap_rate / 100)), places=5)
            self.assertAlmostEqual(scrap_disposal_node["amount"], (input_material_amount_kg * scrap_rate / 100), places=5)

    def _assert_percent_difference(self, value, expected_value, tolerance=0.01):
        percent_difference = abs(value - expected_value) / expected_value
        self.assertLess(percent_difference, tolerance)

    def _assert_dict_fields_equal(self, actual, expected, fields):
        """
        Assert that specific fields in two dictionaries are equal.

        Args:
            actual: The actual dictionary
            expected: The expected dictionary
            fields: List of field names to compare
        """

        for field in fields:
            self.assertEqual(
                actual[field],
                expected[field],
                f"Field '{field}' mismatch: {actual[field]} != {expected[field]}"
            )
