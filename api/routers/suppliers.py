from typing import List
from pydantic import BaseModel
from fastapi import APIRouter
from databases.tenant.models import Supplier, Address as AddressSqlModel
from databases.tenant.repository import SupplierRepo

router = APIRouter()

class CreateSupplierRequest(BaseModel):
    supplier_name: str
    supplier_type: str
    supplier_level: int
    country: str
    tax_registry_number: str | None = None
    primary_contact_name: str | None = None
    primary_contact_email: str | None = None
    website: str | None = None
    address_1: str | None = None
    address_2: str | None = None
    city: str | None = None
    latitude: float | None = None
    longitude: float | None = None
    state_or_province: str | None = None
    postal_code: str | None = None

class AddressResponse(BaseModel):
    country: str
    address_1: str | None = None
    address_2: str | None = None
    city: str | None = None
    latitude: float | None = None
    longitude: float | None = None
    state_or_province: str | None = None
    postal_code: str | None = None

class SupplierResponse(BaseModel):
    id: int
    supplier_type: str
    supplier_level: int
    primary_address: AddressResponse
    supplier_name: str | None = None
    tax_registry_number: str | None = None
    primary_contact_name: str | None = None
    primary_contact_email: str | None = None
    website: str | None = None

class SuppliersResponse(BaseModel):
    suppliers: List[SupplierResponse]

@router.post("/{tenant_id}/suppliers")
async def create_supplier(
    tenant_id: str,
    supplier_request: CreateSupplierRequest,
) -> SupplierResponse:
    supplier_repo = SupplierRepo(tenant_id)
    supplier = Supplier(
        supplier_name=supplier_request.supplier_name,
        supplier_type=supplier_request.supplier_type,
        supplier_level=supplier_request.supplier_level,
        tax_registry_number=supplier_request.tax_registry_number,
        primary_contact_name=supplier_request.primary_contact_name,
        primary_contact_email=supplier_request.primary_contact_email,
        website=supplier_request.website,
    )

    supplier_address = AddressSqlModel(
        address_1=supplier_request.address_1,
        address_2=supplier_request.address_2,
        city=supplier_request.city,
        state_or_provence=supplier_request.state_or_province,
        postal_code=supplier_request.postal_code,
        country=supplier_request.country,
    )

    new_supplier = supplier_repo.create_supplier(supplier, supplier_address)

    return SupplierResponse(
        id=new_supplier.id,
        supplier_type=new_supplier.supplier_type,
        supplier_level=new_supplier.supplier_level,
        primary_address=AddressResponse(
            country=new_supplier.primary_address.country,
            address_1=new_supplier.primary_address.address_1,
            address_2=new_supplier.primary_address.address_2,
            city=new_supplier.primary_address.city,
            state_or_provence=new_supplier.primary_address.state_or_province,
            postal_code=new_supplier.primary_address.postal_code,
        ),
        supplier_name=new_supplier.supplier_name,
        tax_registry_number=new_supplier.tax_registry_number,
        primary_contact_name=new_supplier.primary_contact_name,
        primary_contact_email=new_supplier.primary_contact_email,
        website=new_supplier.website,
    )

@router.get("/{tenant_id}/suppliers")
async def get_suppliers(
    tenant_id: str,
    supplier_type: str | None = None,
    supplier_level: str | None = None,
) -> SuppliersResponse:
    supplier_repo = SupplierRepo(tenant_id)
    suppliers = [
        SupplierResponse(
            id=supplier.id,
            supplier_type=supplier.supplier_type,
            supplier_level=supplier.supplier_level,
            primary_address=AddressResponse(
                country=supplier.primary_address.country,
                address_1=supplier.primary_address.address_1,
                address_2=supplier.primary_address.address_2,
                city=supplier.primary_address.city,
                state_or_provence=supplier.primary_address.state_or_province,
                postal_code=supplier.primary_address.postal_code,
            ),
            supplier_name=supplier.supplier_name,
            tax_registry_number=supplier.tax_registry_number,
            primary_contact_name=supplier.primary_contact_name,
            primary_contact_email=supplier.primary_contact_email,
            website=supplier.website,
        )
        for supplier in supplier_repo.get_suppliers(supplier_type, supplier_level)
    ]

    return SuppliersResponse(suppliers=suppliers)

@router.get("/{tenant_id}/suppliers/{supplier_id}")
async def get_supplier(
    tenant_id: str,
    supplier_id: int
) -> SupplierResponse:
    supplier_repo = SupplierRepo(tenant_id)
    supplier = supplier_repo.get_supplier(supplier_id)

    return SupplierResponse(
            id=supplier.id,
            supplier_type=supplier.supplier_type,
            supplier_level=supplier.supplier_level,
            primary_address=AddressResponse(
                country=supplier.primary_address.country,
                address_1=supplier.primary_address.address_1,
                address_2=supplier.primary_address.address_2,
                city=supplier.primary_address.city,
                latitude=supplier.primary_address.latitude,
                longitude=supplier.primary_address.longitude,
                state_or_provence=supplier.primary_address.state_or_province,
                postal_code=supplier.primary_address.postal_code,
            ),
            supplier_name=supplier.supplier_name,
            tax_registry_number=supplier.tax_registry_number,
            primary_contact_name=supplier.primary_contact_name,
            primary_contact_email=supplier.primary_contact_email,
            website=supplier.website,
        )
