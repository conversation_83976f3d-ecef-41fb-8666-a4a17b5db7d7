from pydantic import BaseModel
from fastapi import APIRouter, HTTPException
from databases.shared.models import ProductCategory
from databases.shared.repository import product_categories
from databases.shared.constants.product_category import category_to_functional_unit
from clients.ml_models import MLModels
from exceptions import ErrorMessages

router = APIRouter()
ml_models = MLModels()

@router.get("/")
async def get_product_categories() -> list[ProductCategory]:
    return product_categories.get_product_categories()

@router.get("/predict-category")
async def predict_category(product_name: str) -> ProductCategory:
    try:
        product_category_list = await ml_models.predict_product_category(product_name)
        if not product_category_list:
            raise HTTPException(
                status_code=404,
                detail=ErrorMessages.PRODUCT_CATEGORY_PREDICTION_NOT_FOUND.value,
            )

        product_category_leaf = product_category_list[-1]
        product_category = product_categories.get_product_category_by_name(product_category_leaf)
        if not product_category:
            raise HTTPException(
                status_code=404,
                detail=ErrorMessages.PRODUCT_CATEGORY_NOT_FOUND_BY_NAME.value.format(
                    product_category=product_category_leaf,
                )
            )

        return product_category

    except Exception as error:
        raise HTTPException(
            500,
            f"Error predicting product category: {error}",
        ) from error

class FunctionalUnitResponse(BaseModel):
    unit: str
    amount: int

@router.get("/{category_id}/functional-unit")
async def get_functional_unit_by_category(category_id: int):
    category = product_categories.get_product_category(category_id)
    if not category:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_CATEGORY_NOT_FOUND_BY_ID.value.format(
                category_id=category_id,
            ),
        )
    functional_unit = category_to_functional_unit.get(category.name, "L")

    return FunctionalUnitResponse(
        unit=functional_unit,
        amount=1,
    )
