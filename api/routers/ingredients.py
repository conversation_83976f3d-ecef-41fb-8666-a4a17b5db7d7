from fastapi import APIRouter, HTTPException
from api.schemas.ingredients import (
    SourceOfProcurementResponse,
)
from clients.ml_models import MLModels
from utils.geocoding import get_country_info

# pylint: disable=line-too-long
from pipelines_v2.ingredient_analysis.source_of_procurement import (
    predict_likely_source_of_procurement,
    IngredientSourceInput,
)
from exceptions import ErrorMessages

router = APIRouter()
ml_models = MLModels()

CAS_NUMBER_WATER = "7732-18-5"

# pylint: disable=unused-argument
@router.get("/{tenant_id}/source-of-procurement")
async def get_likely_source_of_procurement(
    tenant_id: str,
    ingredient_name: str,
    country: str,
) -> SourceOfProcurementResponse:
    country_info = get_country_info(country)
    if not country_info:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.COUNTRY_NOT_FOUND.value.format(country_name=country),
        )

    cas_number = await ml_models.predict_cas_number(ingredient_name)
    if cas_number == CAS_NUMBER_WATER:
        return SourceOfProcurementResponse(
            ingredient_name=ingredient_name,
            hscode=0,
            product_category="Water",
            manufacturing_country=country_info.alpha_3_code,
            country_code=country_info.alpha_3_code,
            country=country_info.name,
            locally_procured=True,
        )

    predicted_source = predict_likely_source_of_procurement(
        ingredient_source_input=IngredientSourceInput(
            ingredient_name=ingredient_name, country=country_info.alpha_3_code
        ),
    )
    if not predicted_source:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.SOURCE_COUNTRY_NOT_FOUND_FOR_INGREDIENT.value.format(
                ingredient_name=ingredient_name,
            ),
        )

    predicted_source_country_code = predicted_source.likely_source_of_procurement

    country_info = get_country_info(predicted_source_country_code)
    if not country_info:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.COUNTRY_NOT_FOUND.value.format(
                country_name=predicted_source_country_code,
            ),
        )

    return SourceOfProcurementResponse(
        ingredient_name=predicted_source.ingredient_name,
        hscode=predicted_source.hscode,
        product_category=predicted_source.product_category,
        manufacturing_country=predicted_source.manufacturing_country,
        country_code=predicted_source_country_code,
        country=country_info.name,
    )
