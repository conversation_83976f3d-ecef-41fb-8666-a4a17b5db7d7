from typing import List
from fastapi import APIRouter, Query, HTTPException
from databases.shared.models import NodeType
from databases.shared.constants.node_type import NodeTypes
from databases.shared.repository import node_types
from pipelines_v2.utils.unit import UnitType

router = APIRouter()

@router.get("/node-types")
async def get_node_types() -> List[NodeType]:
    try:
        return node_types.get_all()
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting node types: {error}"
        ) from error

@router.get("/units")
async def get_units(
    node_type: str | None = Query(None, description="The type of node to get units for"),
):
    if node_type and node_type not in [node_type.value for node_type in NodeTypes]:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid node type: {node_type}"
        )

    if node_type in [NodeTypes.MATERIAL, NodeTypes.PACKAGING]:
        units = [
            unit for unit in UnitType.get_all()
            if unit["category"] in ["weight", "volume"]
        ]
        return units

    if node_type == NodeTypes.TRANSPORTATION:
        return [
            unit for unit in UnitType.get_all()
            if unit["category"] == "mass-distance"
        ]
    return UnitType.get_all()
