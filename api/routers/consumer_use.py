import json
from fastapi import APIRouter,  HTTPException
from databases.shared.constants.node_type import NodeTypes
from databases.shared.repository import (
    fetch_consumer_usage,
    product_categories
)

router = APIRouter()

@router.get("/predict-process-model/{iso3}/{product_category}")
async def predict_process_model(iso3: str, product_category: str):
    product_category = product_categories.get_product_category_by_name(
        product_category,
    )

    if not product_category:
        raise HTTPException(
            status_code=404,
            detail=f"Product category '{product_category}' not found."
        )

    world_iso3 = "WLD"

    consumer_uses = fetch_consumer_usage(
        product_category.id,
        iso3,
    ) or fetch_consumer_usage(
        product_category.id,
        world_iso3,
    )

    if not consumer_uses:
        return {"nodes": [], "edges": []}

    nodes = []
    edges = []
    uses = json.loads(consumer_uses[1])
    for use in uses:
        nodes.append(
            {
                "name": use["resource"],
                "node_type": NodeTypes.USE.value,
                "amount": use["amount"],
                "unit": use["unit"],
            }
        )

    for i in range(len(nodes) - 1):
        edges.append({
            "source": nodes[i]["name"],
            "target": nodes[i + 1]["name"]
        })

    return {"nodes": nodes, "edges": edges}
