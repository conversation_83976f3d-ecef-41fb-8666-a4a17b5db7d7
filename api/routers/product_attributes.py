from fastapi import APIRouter, HTTPException
from databases.tenant.models import ProductAttributes, ProductAttributeDef
from databases.tenant.repository import ProductAttributesRepo
from api.schemas.product_attributes import (
    ProductAttributeDefInput,
    ProductAttributesInput,
    UpdateProductAttributeInput,
    ProductAttributeResponse,
)
from clients.ml_models import MLModels
from exceptions import ErrorMessages

router = APIRouter()
ml_models = MLModels()


@router.get("/{tenant_id}")
async def get_attribute_defs(
    tenant_id: str,
) -> list[ProductAttributeDef]:
    try:
        product_attributes = ProductAttributesRepo(tenant_id)
        attribute_defs = product_attributes.get_attribute_defs()
        return attribute_defs

    except Exception as error:
        raise HTTPException(
            500,
            f"Error getting attributes: {error}",
        ) from error


@router.get("/{tenant_id}/all")
async def get_all_product_attributes(
    tenant_id: str,
) -> list[ProductAttributeResponse]:
    try:
        product_attributes = ProductAttributesRepo(tenant_id)
        attributes = product_attributes.get_product_attributes()
        return [
            ProductAttributeResponse(
                value=attr.value,
                id=attr.attribute_def_id,
                product_id=attr.product_id,
                key=attr_def.key,
                default_value=attr_def.default_value,
                attribute_type=attr_def.type,
            )
            for attr, attr_def in attributes
        ]

    except Exception as error:
        raise HTTPException(
            500,
            f"Error getting product attributes: {error}",
        ) from error


@router.get("/{tenant_id}/{product_id:path}")
async def get_product_attributes(
    tenant_id: str,
    product_id: str,
) -> list[ProductAttributeResponse]:
    try:
        product_attributes = ProductAttributesRepo(tenant_id)
        attributes = product_attributes.get_product_attributes(product_id)
        return [
            ProductAttributeResponse(
                value=attr.value,
                id=attr.attribute_def_id,
                product_id=attr.product_id,
                key=attr_def.key,
                default_value=attr_def.default_value,
                attribute_type=attr_def.type,
            )
            for attr, attr_def in attributes
        ]

    except Exception as error:
        raise HTTPException(
            500,
            f"Error getting product attributes: {error}",
        ) from error


@router.post("/{tenant_id}")
async def create_product_attribute_def(
    tenant_id: str,
    attribute_def_input: ProductAttributeDefInput,
) -> ProductAttributeDef:
    product_attributes = ProductAttributesRepo(tenant_id)
    existing_attribute = product_attributes.get_attribute_def_by_key(
        attribute_def_input.key
    )
    if existing_attribute:
        raise HTTPException(
            status_code=400,
            detail=ErrorMessages.PRODUCT_ATTRIBUTE_EXISTS.value.format(
                attribute_key=attribute_def_input.key,
            ),
        )

    try:
        attribute = ProductAttributeDef(
            key=attribute_def_input.key,
        )
        return product_attributes.create_attribute_def(attribute)

    except Exception as error:
        raise HTTPException(
            500,
            f"Error creating attribute: {error}",
        ) from error


@router.put("/{tenant_id}")
async def update_product_attribute_def(
    tenant_id: str,
    attribute_def_input: ProductAttributesInput,
) -> ProductAttributeDef:
    product_attributes = ProductAttributesRepo(tenant_id)
    attribute_info = product_attributes.get_attribute_def_by_id(attribute_def_input.id)
    if not attribute_info:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_ATTRIBUTE_NOT_FOUND_BY_ID.value.format(
                attribute_id=attribute_def_input.id,
            ),
        )

    existing_attribute = product_attributes.get_attribute_def_by_key(
        attribute_def_input.key
    )
    if existing_attribute:
        raise HTTPException(
            status_code=400,
            detail=ErrorMessages.PRODUCT_ATTRIBUTE_EXISTS.value.format(
                attribute_key=attribute_def_input.key,
            ),
        )

    try:
        attribute_info.key = attribute_def_input.key
        product_attributes.upsert_attribute_def(attribute_info)

        updated_attribute = product_attributes.get_attribute_def_by_id(
            attribute_def_input.id
        )
        return updated_attribute

    except Exception as error:
        raise HTTPException(
            500,
            f"Error upserting product attribute: {error}",
        ) from error


@router.put("/{tenant_id}/{product_id:path}")
async def update_product_attribute(
    tenant_id: str,
    product_id: str,
    product_attribute_input: UpdateProductAttributeInput,
) -> ProductAttributeResponse:
    product_attributes = ProductAttributesRepo(tenant_id)

    if not product_attributes.get_attribute_def_by_id(product_attribute_input.id):
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_ATTRIBUTE_NOT_FOUND_BY_ID.value.format(
                attribute_id=product_attribute_input.id,
            ),
        )

    try:
        product_attribute = ProductAttributes(
            product_id=product_id,
            attribute_def_id=product_attribute_input.id,
            value=product_attribute_input.value,
        )
        product_attributes.upsert_product_attribute(product_attribute)

        updated_attribute = product_attributes.get_product_attribute_by_id(
            product_id, product_attribute_input.id
        )
        return ProductAttributeResponse(
            value=updated_attribute[0].value,
            id=updated_attribute[0].attribute_def_id,
            product_id=updated_attribute[0].product_id,
            key=updated_attribute[1].key,
            default_value=updated_attribute[1].default_value,
            attribute_type=updated_attribute[1].type,
        )

    except Exception as error:
        raise HTTPException(
            500,
            f"Error udpating product attribute: {error}",
        ) from error
