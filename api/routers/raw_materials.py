from fastapi import APIRouter, HTTPException, Header
from databases.shared.models import RawMaterial
from databases.shared.repository import raw_material_repo
from databases.tenant.repository import TenantEmissionsFactorRepo
from api.schemas.raw_materials import ActivityRecommendationsRequest
from services.emissions_factors_service import EmissionsFactorsService, ActivityResponse
from services.prediction_override_service import PredictionOverrideService
from pipelines_v2.utils.unit import get_valid_conversions
from clients.propelauth import get_supported_impact_factors, is_carbon_only


router = APIRouter()

@router.get("/")
async def get_raw_materials(
    is_packaging: bool | None = None,
    is_textile: bool | None = None,
) -> list[RawMaterial]:
    try:
        materials = raw_material_repo.get_raw_materials(is_packaging, is_textile)

        return materials
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting raw materials: {error}",
        ) from error


@router.post("/activities/recommendations")
async def get_recommended_activities(
    activity_request: ActivityRecommendationsRequest,
    api_version: str = Header("latest"),
    x_tenant_id: str | None = Header(
        None,
        description="Tenant ID (provide to filter by LCIA methods set for the tenant)",
    ),
) -> ActivityResponse:
    try:
        service = EmissionsFactorsService()

        if activity_request.unit:
            try:
                valid_units = get_valid_conversions(activity_request.unit)
            except ValueError as error:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid unit abbreviation: {activity_request.unit}",
                ) from error
        else:
            valid_units = None

        if x_tenant_id:
            supported_impact_factors = get_supported_impact_factors(
                x_tenant_id
            )
            carbon_only = is_carbon_only(x_tenant_id)
            emissions_factor_override = (
                PredictionOverrideService(x_tenant_id)
                .get_emissions_factor_match_override(activity_request.chemical_name)
            )
            tenant_emissions_factor_repo = TenantEmissionsFactorRepo(x_tenant_id)
            custom_emissions_factors = tenant_emissions_factor_repo.get_custom_emissions_factors()
        else:
            supported_impact_factors = None
            carbon_only = False
            emissions_factor_override = None
            custom_emissions_factors = None

        return await service.get_emissions_factor_match(
            ingredient_name=activity_request.chemical_name,
            geography=activity_request.geography,
            number_of_matches=activity_request.number_of_matches,
            valid_units=valid_units,
            product_category=activity_request.product_category,
            geography_modeling=activity_request.geography_modeling,
            impact_factors=supported_impact_factors,
            emissions_factor_override=emissions_factor_override,
            custom_emissions_factors=custom_emissions_factors,
            carbon_only=carbon_only,
            api_version=api_version,
        )

    except Exception as error:
        raise HTTPException(
            500,
            f"Error predicting emissions factor: {error}",
        ) from error
