from typing import List
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Query
from databases.shared.models.emissions_factor_value import EmissionFactorValueRW
from databases.shared.models import EmissionsFactors
from databases.shared.models.intermediate_exchange import IntermediateExchangeRead
from databases.shared.repository.intermediate_exchange import (
    intermediate_exchanges as intermediate_exchanges_repo
)
from databases.shared.repository.emissions_factors import (
    emissions_factors as emissions_factors_repo
)
from databases.tenant.models import (
    TenantEmissionsFactor,
    TenantIntermediateExchangeRead,
)
from databases.tenant.repository import (
    TenantEmissionsFactorRepo,
    TenantIntermediateExchangeRepo,
)
from services.emissions_factors_service import EmissionsFactorsService
from clients.propelauth import is_carbon_only
from utils.logger import logger_instance

logger = logger_instance.get_logger()

router = APIRouter()


class EmissionsFactor(BaseModel):
    activity_name: str
    activity_type: str
    reference_product_name: str
    geography: str
    source: str
    unit: str | None = None
    activity_description: str | None = None

class Exchange(BaseModel):
    exchange_name: str
    amount: float
    unit: str
    input_stream: bool
    exchange_emissions_factor: EmissionsFactor

class CreateEmissionsFactorRequest(BaseModel):
    parent_emissions_factor: EmissionsFactor
    exchanges: List[Exchange]
    elemental_ef_values: List[EmissionFactorValueRW]
    skip_if_exists: bool = False

class UpdateEmissionsFactorRequest(BaseModel):
    activity_name: str
    reference_product_name: str
    activity_description: str | None = None

class CombinedEmissionsFactors(BaseModel):
    tenant_emissions_factors: List[TenantEmissionsFactor]
    shared_emissions_factors: List[EmissionsFactors]

@router.get("/intermediate-exchanges")
async def get_shared_intermediate_exchanges(
    activity_name: str = Query(None),
    reference_product: str = Query(None),
    geography: str = Query(None),
    source: str = Query(None),
) -> List[IntermediateExchangeRead]:

    exchanges = intermediate_exchanges_repo.get_all_for_emissions_factor(
        activity_name=activity_name,
        reference_product=reference_product,
        geography=geography,
        source=source,
    )

    return exchanges

@router.post("/{tenant_id}")
async def create_emissions_factor(
    tenant_id: str,
    request: CreateEmissionsFactorRequest,
) -> TenantEmissionsFactor:
    try:
        # Check if emission factor already exists
        tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        existing_emissions_factor = tenant_emissions_factors.get_emissions_factor(
            activity_name=request.parent_emissions_factor.activity_name,
            geography=request.parent_emissions_factor.geography,
            reference_product=request.parent_emissions_factor.reference_product_name,
            source=request.parent_emissions_factor.source,
        )

        if existing_emissions_factor:
            if not request.skip_if_exists:
                raise HTTPException(
                    status_code=409,
                    detail="Emissions factor already exists"
                )
            return existing_emissions_factor

        # Create new emission factor if it doesn't exist
        service = EmissionsFactorsService()
        emissions_factor, _ = service.create_tenant_emissions_factor(
            tenant_id=tenant_id,
            activity_name=request.parent_emissions_factor.activity_name,
            activity_type=request.parent_emissions_factor.activity_type,
            reference_product=request.parent_emissions_factor.reference_product_name,
            activity_description=request.parent_emissions_factor.activity_description,
            geography=request.parent_emissions_factor.geography,
            source=request.parent_emissions_factor.source,
            unit=request.parent_emissions_factor.unit,
            exchanges=request.exchanges,
            elemental_ef_values=request.elemental_ef_values,
        )

        return emissions_factor
    except Exception as error:
        logger.exception("Error creating emissions factor")
        raise HTTPException(
            status_code=500, detail=f"Server error getting emissions factors: {error}"
        ) from error

@router.post("/{tenant_id}/bulk", response_model=List[TenantEmissionsFactor])
def bulk_create_emissions_factor(
    request: List[CreateEmissionsFactorRequest],
    tenant_id: str,
) -> List[TenantEmissionsFactor]:
    """
    Bulk create multiple emissions factors
    """
    try:
        service = EmissionsFactorsService()
        created_efs = []

        for ef_request in request:
            emissions_factor, _ = service.create_tenant_emissions_factor(
                tenant_id=tenant_id,
                activity_name=ef_request.parent_emissions_factor.activity_name,
                activity_type=ef_request.parent_emissions_factor.activity_type,
                reference_product=ef_request.parent_emissions_factor.reference_product_name,
                geography=ef_request.parent_emissions_factor.geography,
                source=ef_request.parent_emissions_factor.source,
                unit=ef_request.parent_emissions_factor.unit,
                exchanges=ef_request.exchanges,
                elemental_ef_values=ef_request.elemental_ef_values,
            )
            created_efs.append(emissions_factor)

        return created_efs

    except Exception as error:
        logger.exception("Error creating emissions factors")
        raise HTTPException(
            status_code=500,
            detail=f"Server error creating emissions factors: {error}"
        ) from error

@router.get("/")
async def search_emissions_factors(
    activity_name: str = Query(None),
    geography: str | None = Query(None, description="Comma-separated geography values. All values must be matched (AND logic)."),
    paging: int | None = Query(200),
) -> List[EmissionsFactors]:
    """
    Search emissions factors.

    When providing multiple values for geography (e.g., geography="US,CA"),
    the results will include only records that match ALL the specified values (AND logic).
    """
    try:
        emissions_factors = emissions_factors_repo.search_emissions_factors(
            activity_name=activity_name,
            geography=geography,
            paging=paging,
        )
        return emissions_factors
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Server error getting emissions factors: {error}"
        ) from error

@router.get("/{tenant_id}")
async def get_tenant_emissions_factors(tenant_id: str) -> List[TenantEmissionsFactor]:
    try:
        emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        return emissions_factors.get_all()
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Server error getting tenant emissions factors: {error}"
        ) from error

@router.get("/{tenant_id}/sources")
async def get_emissions_factor_sources(tenant_id: str) -> List[str]:
    try:
        carbon_only = is_carbon_only(tenant_id)
        sources = emissions_factors_repo.get_sources(carbon_only)
        sources.append(tenant_id.upper())
        return sources
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Server error getting tenant emissions factor sources: {error}"
        ) from error

@router.get("/{tenant_id}/all")
async def get_combined_emissions_factors(
    tenant_id: str,
    activity_name: str | None = Query(None),
    geography: str | None = Query(None, description="Comma-separated geography values. All values must be matched (AND logic)."),
    unit: str | None = Query(None, description="Comma-separated unit values. All values must be matched (AND logic)."),
    source: str | None = Query(None, description="Comma-separated source values. All values must be matched (AND logic)."),
    paging: int | None = Query(200),
) -> CombinedEmissionsFactors:
    """
    Get combined emissions factors for a tenant.

    When providing multiple values for a filter (e.g., geography="US,CA"),
    the results will include only records that match ALL the specified values (AND logic).
    Note that for single-value fields like geography, providing multiple values may result in no matches.
    """
    try:
        carbon_only = is_carbon_only(tenant_id)
        emissions_factors = TenantEmissionsFactorRepo(tenant_id)
        tenant_emissions_factors = emissions_factors.search_emissions_factors(
            activity_name=activity_name,
            geography=geography,
            unit=unit,
            source=source,
            paging=paging,
        )
        paging = paging - len(tenant_emissions_factors)

        print(source, tenant_id.upper())

        if source != tenant_id.upper() and paging > 0:
            shared_emissions_factors = emissions_factors_repo.search_emissions_factors(
                activity_name=activity_name,
                geography=geography,
                unit=unit,
                source=source,
                paging=paging,
                carbon_only=carbon_only,
            )
        else:
            shared_emissions_factors = []

        return CombinedEmissionsFactors(
            tenant_emissions_factors=tenant_emissions_factors,
            shared_emissions_factors=shared_emissions_factors,
        )
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Server error getting tenant emissions factors: {error}"
        ) from error

@router.get("/{tenant_id}/intermediate-exchanges")
async def get_intermediate_exchanges(
    tenant_id: str,
    emissions_factor_id: int | None = Query(None),
    activity_name: str | None = Query(None),
    reference_product: str | None = Query(None),
    geography: str | None = Query(None),
    source: str | None = Query(None),
) -> List[TenantIntermediateExchangeRead]:
    if not emissions_factor_id and not all(
        (
            bool(activity_name),
            bool(reference_product),
            bool(geography),
            bool(source),
        )
    ):
        raise HTTPException(
            status_code=400,
            detail=(
                "Must provide either emissions_factor_id or "
                "(activity_name, reference_product, geography, source)"
            ),
        )

    tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)
    if emissions_factor_id:
        exchanges = tenant_intermediate_exchanges.get_all_for_emissions_factor_by_id(
            emissions_factor_id
        )
    else:
        exchanges = tenant_intermediate_exchanges.get_all_for_emissions_factor(
            activity_name=activity_name,
            reference_product=reference_product,
            geography=geography,
            source=source,
        )

    return exchanges


class CopyEmissionsFactorRequest(BaseModel):
    activity_name: str
    reference_product: str
    geography: str
    source: str
    new_name: str | None = None


@router.post("/{tenant_id}/copy-emissions-factor")
async def copy_emissions_factor(
    tenant_id: str,
    request: CopyEmissionsFactorRequest,
) -> List[TenantIntermediateExchangeRead]:
    service = EmissionsFactorsService()
    _, exchanges = service.copy_emissions_factor_to_tenant_emissions_factors(
        tenant_id=tenant_id,
        activity_name=request.activity_name,
        reference_product=request.reference_product,
        geography=request.geography,
        source=request.source,
        new_name=request.new_name,
    )

    return exchanges


class CreateIntermediateExchangeRequest(BaseModel):
    parent_emissions_factor: EmissionsFactor
    exchange: Exchange


@router.post("/{tenant_id}/exchange")
async def create_intermediate_exchange(
    tenant_id: str,
    request: CreateIntermediateExchangeRequest,
) -> TenantIntermediateExchangeRead:
    service = EmissionsFactorsService()

    emissions_factor = emissions_factors_repo.get_emissions_factor(
        request.exchange.exchange_emissions_factor.activity_name,
        request.exchange.exchange_emissions_factor.geography,
        request.exchange.exchange_emissions_factor.reference_product_name,
        request.exchange.exchange_emissions_factor.source,
    )

    if not emissions_factor:
        raise HTTPException(status_code=404, detail="Emissions factor not found")

    tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
    tenant_emissions_factor = tenant_emissions_factors.get_emissions_factor(
        request.parent_emissions_factor.activity_name,
        request.parent_emissions_factor.geography,
        request.parent_emissions_factor.reference_product_name,
        request.parent_emissions_factor.source,
    )

    if not tenant_emissions_factor:
        raise HTTPException(status_code=404, detail="Parent Emissions factor not found")

    new_exchange = service.create_intermediate_exchange_emissions_factor(
        tenant_id=tenant_id,
        parent_emissions_factor_id=tenant_emissions_factor.id,
        exchange_name=request.exchange.exchange_name,
        exchange_amount=request.exchange.amount,
        exchange_unit=request.exchange.unit,
        activity_name=request.exchange.exchange_emissions_factor.activity_name,
        reference_product=request.exchange.exchange_emissions_factor.reference_product_name,
        geography=request.exchange.exchange_emissions_factor.geography,
        source=request.exchange.exchange_emissions_factor.source,
    )

    return new_exchange


@router.put("/{tenant_id}/exchange/{exchange_id}")
async def update_intermediate_exchange(
    tenant_id: str,
    exchange_id: str,
    request: Exchange,
) -> TenantIntermediateExchangeRead:
    service = EmissionsFactorsService()

    emissions_factor = emissions_factors_repo.get_emissions_factor(
        request.exchange_emissions_factor.activity_name,
        request.exchange_emissions_factor.geography,
        request.exchange_emissions_factor.reference_product_name,
        request.exchange_emissions_factor.source,
    )

    if not emissions_factor:
        raise HTTPException(status_code=404, detail="Emissions factor not found")

    tenant_intermediate_exchanges = TenantIntermediateExchangeRepo(tenant_id)
    exchange = tenant_intermediate_exchanges.get_by_id(exchange_id)
    if not exchange:
        raise HTTPException(status_code=404, detail="Intermediate exchange not found")

    tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
    tenant_emissions_factor = tenant_emissions_factors.get_emissions_factor(
        request.exchange_emissions_factor.activity_name,
        request.exchange_emissions_factor.geography,
        request.exchange_emissions_factor.reference_product_name,
        request.exchange_emissions_factor.source,
    )

    if not tenant_emissions_factor:
        tenant_emissions_factor, _ = (
            service.copy_emissions_factor_to_tenant_emissions_factors(
                tenant_id=tenant_id,
                activity_name=request.exchange_emissions_factor.activity_name,
                reference_product=request.exchange_emissions_factor.reference_product_name,
                geography=request.exchange_emissions_factor.geography,
                source=request.exchange_emissions_factor.source,
            )
        )

    new_exchange = service.update_intermediate_exchange_emissions_factor(
        tenant_id=tenant_id,
        exchange_id=exchange_id,
        activity_name=tenant_emissions_factor.activity_name,
        reference_product=tenant_emissions_factor.reference_product,
        geography=tenant_emissions_factor.geography,
        source=tenant_emissions_factor.source,
        exchange_name=request.exchange_name,
        exchange_amount=request.amount,
        exchange_unit=request.unit,
    )

    return new_exchange


@router.delete("/{tenant_id}/exchange/{exchange_id}")
async def delete_intermediate_exchange(
    tenant_id: str,
    exchange_id: str,
) -> bool:

    service = EmissionsFactorsService()
    service.delete_intermediate_exchange(
        tenant_id=tenant_id,
        exchange_id=exchange_id,
    )

    return True

@router.put("/{tenant_id}/emissions-factor/{emissions_factor_id}")
async def update_emissions_factor(
    tenant_id: str,
    emissions_factor_id: int,
    request: UpdateEmissionsFactorRequest,
) -> TenantEmissionsFactor:
    tenant_emissions_factors = TenantEmissionsFactorRepo(tenant_id)
    emissions_factor = tenant_emissions_factors.get_emissions_factor_by_id(emissions_factor_id)
    if not emissions_factor:
        raise HTTPException(status_code=404, detail="Emissions factor not found")

    return tenant_emissions_factors.update_emissions_factor(
        emissions_factor_id=emissions_factor_id,
        activity_name=request.activity_name,
        activity_type=emissions_factor.activity_type,
        activity_description=request.activity_description,
        reference_product=request.reference_product_name,
        geography=emissions_factor.geography,
        source=emissions_factor.source,
        unit=emissions_factor.unit,
    )
