from fastapi import APIRouter
from pydantic import BaseModel
from pipelines_v2.packaging.packaging_predictor import (
    PackageComponent,
    PackagingLevel,
    PackagingPredictor,
)

router = APIRouter()


class PackagingPredictionRequest(BaseModel):
    product_category: str
    content_weight: float
    packaging_level: str | None = None

@router.post("/packaging-predictions")
async def predict_packaging_by_category(
    request_body: PackagingPredictionRequest,
) -> list[PackageComponent]:
    package_predictor = PackagingPredictor(
        product_category=request_body.product_category,
        content_weight=request_body.content_weight,
    )

    if not request_body.packaging_level:
        package_components = []
        package_components.extend(
            package_predictor.predict_primary_package_components()
        )
        package_components.extend(
            package_predictor.predict_secondary_package_components()
        )
        return package_components

    return (
        package_predictor.predict_primary_package_components()
        if request_body.packaging_level == PackagingLevel.PRIMARY.value
        else package_predictor.predict_secondary_package_components()
    )
