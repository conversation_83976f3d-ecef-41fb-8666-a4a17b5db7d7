import traceback
from typing import List
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Depends
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.repository import ProductRepo
from databases.tenant.models import (
    Node,
    NodeRead,
    Edge,
    Address,
    Product,
)
from services.process_model_service import ProcessModelService
from services.emissions_factors_service import EmissionsFactorsService
from services.pipeline_service import PipelineService
from services.prediction_override_service import PredictionOverrideService
from pipelines_v2.process_model_walker import PreprocessWalker, MassAllocationPreprocessWalker
from pipelines_v2.graph import Graph
from api.dependencies import get_tenant_service
from api.utils.product_utils import (
    add_default_factory_locale,
    extract_product_factory_location_from_nodes
)
from api.schemas.products import CreateProductRequest
from exceptions import ResourceNotFoundException


router = APIRouter()


@router.post("/{tenant_id}")
async def create_product(
    tenant_id: str,
    request: CreateProductRequest,
) -> Product:
    products = ProductRepo(tenant_id)
    existing_product = products.get_product(request.product_id)
    if existing_product:
        raise HTTPException(
            status_code=400,
            detail=f"Product with id {request.product_id} already exists",
        )

    try:
        product = add_default_factory_locale(request)
        product = products.create_product(
            Product(**product.dict(), manufacturer=product.brand),
        )

        return product
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Error creating product: {error}",
        ) from error

class Location(BaseModel):
    country: str
    city: str | None = None
    address: str | None = None
    zip_code: str | None = None


class EmissionsFactor(BaseModel):
    activity_name: str
    reference_product_name: str
    geography: str
    source: str


class CreateNode(BaseModel):
    id: int
    name: str
    amount: float | None = None
    unit: str = "kg"
    quantity: int
    node_type: str
    location: Location | None = None
    emissions_factor: EmissionsFactor | None = None
    supplier_id: int | None = None

    # Material specific fields
    recycled_content_rate: float | None = None
    disposed_weight_kg: float | None = None
    mass_allocation_per_kg: bool | None = False
    eol_recycling_rate: float | None = 0.0
    eol_landfill_rate: float | None = 0.0
    eol_incineration_rate: float | None = 0.0
    eol_composting_rate: float | None = 0.0

    # Packaging specific fields
    component_name: str | None = None
    packaging_level: str | None = None
    packaging_type: str | None = None
    description: str | None = None

    # Transportation specific fields
    segment_type: str | None = None
    transport_mode: str | None = None

    # Production specific fields
    scrap_rate: float | None = None
    scrap_fate: str | None = None


class CreateEdge(BaseModel):
    from_node_id: int
    to_node_id: int


class CreateProcessModelRequest(BaseModel):
    nodes: List[CreateNode]
    edges: List[CreateEdge]


class ProcessModelResponse(BaseModel):
    nodes: List[NodeRead]
    edges: List[Edge]

class EditNodeRequest(BaseModel):
    id: int
    name: str
    amount: float | None = None
    component: str | None = None
    description: str | None = None
    quantity: int | None = None
    unit: str | None = None
    emissions_factor: EmissionsFactor | None = None

def verify_graph_connectivity(request_nodes: list[CreateNode], edges: list[CreateEdge]):
    """Verify that all nodes are connected in a single graph with no isolated subgraphs."""
    if not request_nodes:
        return

    # Convert request nodes to Node objects for the Graph class
    nodes = [
        Node(id=node.id, product_id="temp", node_type=node.node_type, name=node.name)
        for node in request_nodes
    ]
    # Convert request edges to Edge objects for the Graph class
    edge_list = [
        Edge(from_node_id=edge.from_node_id, to_node_id=edge.to_node_id)
        for edge in edges
    ]

    # Create a graph to use its helper methods
    graph = Graph(nodes, edge_list)

    # Use DFS to find all reachable nodes from the first node
    visited = set()
    stack = [nodes[0]]  # Start with first node

    while stack:
        node = stack.pop()
        if node.id not in visited:
            visited.add(node.id)
            # Add both incoming and outgoing nodes to explore all connections
            neighbors = graph.get_incoming_nodes(node) + graph.get_outgoing_nodes(node)
            stack.extend([n for n in neighbors if n.id not in visited])

    # Get all node IDs in the graph
    all_nodes = {node.id for node in nodes}

    # Check if any nodes were not visited
    unreachable = all_nodes - visited
    if unreachable:
        raise HTTPException(
            status_code=400,
            detail=f"Process model contains disconnected nodes. "
                  f"The following node IDs are not connected "
                  f"to the main graph: {unreachable}"
        )

@router.post("/{tenant_id}/{product_id:path}/process-model")
async def create_process_model(
    tenant_id: str,
    product_id: str,
    request: CreateProcessModelRequest,
    service: ProcessModelService = Depends(get_tenant_service(ProcessModelService)),
    prediction_override_service: PredictionOverrideService = Depends(
        get_tenant_service(PredictionOverrideService)
    ),
):
    # Verify graph connectivity before any processing
    verify_graph_connectivity(request.nodes, request.edges)

    emissions_factors_service = EmissionsFactorsService()
    nodes = []
    for node in request.nodes:
        if node.emissions_factor:
            emissions_factor, _ = (
                emissions_factors_service.copy_emissions_factor_to_tenant_emissions_factors(
                    tenant_id,
                    node.emissions_factor.activity_name,
                    node.emissions_factor.reference_product_name,
                    node.emissions_factor.geography,
                    node.emissions_factor.source,
                )
            )

            prediction_override = prediction_override_service.get_emissions_factor_match_override(
                node.name,
            )

            if not prediction_override:
                prediction_override_service.create_emissions_factor_match_override(
                    node.name,
                    emissions_factor.activity_name,
                    emissions_factor.geography,
                    emissions_factor.reference_product,
                    emissions_factor.source,
                )
        else:
            emissions_factor = None

        if node.location:
            node_location = Address(
                city=node.location.city,
                country=node.location.country,
                address_1=node.location.address,
                postal_code=node.location.zip_code,
            )
        else:
            node_location = None

        node_payload = {
            "id": node.id,
            "product_id": product_id,
            "node_type": node.node_type,
            "name": node.name,
            "amount": node.amount,
            "unit": node.unit,
            "quantity": node.quantity,
            "location": node_location if node_location else None,
            "emissions_factor_id": emissions_factor.id if emissions_factor else None,
            "supplier_id": node.supplier_id if node.supplier_id else None,
            "component_name": node.component_name,
            "description": node.description,
        }
        if node.node_type in [NodeTypes.MATERIAL.value, NodeTypes.PACKAGING.value]:
            node_payload["recycled_content_rate"] = node.recycled_content_rate
            node_payload["disposed_weight_kg"] = node.disposed_weight_kg
            node_payload["mass_allocation_per_kg"] = node.mass_allocation_per_kg
            node_payload["eol_recycling_rate"] = node.eol_recycling_rate
            node_payload["eol_landfill_rate"] = node.eol_landfill_rate
            node_payload["eol_incineration_rate"] = node.eol_incineration_rate
            node_payload["eol_composting_rate"] = node.eol_composting_rate

        if node.node_type == NodeTypes.PACKAGING.value:
            node_payload["packaging_level"] = node.packaging_level
            node_payload["packaging_type"] = node.packaging_type
            node_payload["description"] = node.description

        if node.node_type == NodeTypes.TRANSPORTATION.value:
            node_payload["segment_type"] = node.segment_type or "auto"
            node_payload["transport_mode"] = node.transport_mode

        if node.node_type == NodeTypes.PRODUCTION.value:
            node_payload["scrap_rate"] = node.scrap_rate
            node_payload["scrap_fate"] = node.scrap_fate

        if node.node_type == NodeTypes.EOL.value:
            node_payload["segment_type"] = "auto"

        nodes.append(Node(**node_payload))

    edges = []
    for edge in request.edges:
        edges.append(
            Edge(
                product_id=product_id,
                from_node_id=edge.from_node_id,
                to_node_id=edge.to_node_id,
            )
        )

    preprocess_walker = PreprocessWalker(
        tenant_id,
        product_id,
        nodes,
        edges,
    )
    nodes, edges = preprocess_walker.walk()

    # Run mass allocation preprocessing after initial preprocessing
    mass_allocation_walker = MassAllocationPreprocessWalker(
        tenant_id,
        product_id,
        nodes,
        edges,
    )
    nodes, edges = mass_allocation_walker.walk()

    graph = Graph(nodes, edges)
    nodes = graph.get_nodes_topologically_sorted()

    node_id_map = {}

    for node in nodes:
        created_node = service.create_node(
            Node(**node.dict(exclude={"id"})),
            node.location if node.location else None,
        )
        node_id_map[node.id] = created_node.id

    process_model_nodes, _ = service.get_process_model_for_product(product_id)
    for edge in edges:
        process_model_from_node_id = node_id_map[edge.from_node_id]
        process_model_to_node_id = node_id_map[edge.to_node_id]

        service.create_edge(
            process_model_from_node_id,
            process_model_to_node_id,
        )

    products = ProductRepo(tenant_id)
    products.aggregate_total_weight(product_id)

    #get product factory location
    product_factory_location = extract_product_factory_location_from_nodes(process_model_nodes)
    if product_factory_location:
        products.update_product_factory_location(
            product_id,
            product_factory_location.city,
            product_factory_location.country
        )

    return ProcessModelResponse(nodes=process_model_nodes, edges=edges)


@router.put("/{tenant_id}/{product_id:path}/nodes")
async def edit_node(
    tenant_id: str,
    product_id: str,
    request: List[EditNodeRequest],
    service: ProcessModelService = Depends(get_tenant_service(ProcessModelService)),
    prediction_override_service: PredictionOverrideService = Depends(
        get_tenant_service(PredictionOverrideService)
    ),
) -> List[NodeRead]:
    try:
        emissions_factors_service = EmissionsFactorsService()
        updated_nodes = []

        for node in request:
            node_payload = {"name": node.name}

            if node.amount is not None:
                node_payload["amount"] = node.amount
                node_payload["unit"] = node.unit

            if node.component is not None:
                node_payload["component_name"] = node.component

            if node.description is not None:
                node_payload["description"] = node.description

            if node.quantity is not None:
                node_payload["quantity"] = node.quantity

            if node.emissions_factor:
                emissions_factor, _ = (
                    emissions_factors_service.copy_emissions_factor_to_tenant_emissions_factors(
                        tenant_id,
                        node.emissions_factor.activity_name,
                        node.emissions_factor.reference_product_name,
                        node.emissions_factor.geography,
                        node.emissions_factor.source,
                    )
                )

                prediction_override = (
                    prediction_override_service.get_emissions_factor_match_override(
                        node.name,
                    )
                )

                if prediction_override:
                    prediction_override_service.update_emissions_factor_match_override(
                        query=node.name,
                        activity_name=emissions_factor.activity_name,
                        geography=emissions_factor.geography,
                        reference_product_name=emissions_factor.reference_product,
                        source=emissions_factor.source,
                    )
                else:
                    prediction_override_service.create_emissions_factor_match_override(
                        query=node.name,
                        activity_name=emissions_factor.activity_name,
                        geography=emissions_factor.geography,
                        reference_product_name=emissions_factor.reference_product,
                        source=emissions_factor.source,
                    )

                node_payload["emissions_factor_id"] = emissions_factor.id

            updated_node = service.update_node(
                Node(
                    id=node.id,
                    **node_payload
                )
            )
            updated_nodes.append(updated_node)

        # update product weight
        products = ProductRepo(tenant_id)
        products.aggregate_total_weight(product_id)
        products.update_product_lca_output(product_id, None)

        return updated_nodes
    except ResourceNotFoundException as error:
        raise HTTPException(
            status_code=404, detail=str(error)
        ) from error
    except Exception as error:
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {error}"
        ) from error


@router.get("/{tenant_id}/{product_id:path}/process-model")
async def get_process_model(
    product_id: str,
    service: ProcessModelService = Depends(get_tenant_service(ProcessModelService)),
):
    try:
        nodes, edges = service.get_process_model_for_product(product_id)
    except Exception as error:
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {error}"
        ) from error

    return ProcessModelResponse(nodes=nodes, edges=edges)


@router.delete("/{tenant_id}/{product_id:path}/process-model")
async def delete_process_model(
    tenant_id: str,
    product_id: str,
    service: ProcessModelService = Depends(get_tenant_service(ProcessModelService)),
):
    try:
        nodes, edges = service.delete_process_model_for_product(product_id)
        products = ProductRepo(tenant_id)
        products.update_product_lca_output(product_id, None)
    except Exception as error:
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {error}"
        ) from error

    return ProcessModelResponse(nodes=nodes, edges=edges)


@router.get("/{tenant_id}/{product_id:path}/process-model/walk")
async def walk_process_model(
    product_id: str,
    service: PipelineService = Depends(get_tenant_service(PipelineService)),
):
    try:
        aggregation = service.walk(product_id)
        return aggregation
    except ResourceNotFoundException as error:
        raise HTTPException(
            status_code=404, detail=f"Product with ID '{product_id}' not found."
        ) from error
    except Exception as error:
        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Internal Server Error: {error}"
        ) from error
