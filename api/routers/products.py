import traceback
from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import FileResponse
from databases.tenant.models import (
    Product as ProductSQLModel,
    Node,
    Address,
)
from databases.tenant.repository import (
    ProductRepo,
    SupplierRepo,
    ProductAttributesRepo,
)
from databases.tenant.constants.product import ProductStatus, ProductType
import databases.tenant.repository.product as product_repo
from api.schemas.products import (
    CreateProductRequest,
    Product,
    Products,
    ProductLCAInfo,
    UpdateTagsRequest,
)
from api.utils.product_utils import add_default_image_url, add_default_factory_locale
from services.process_model_service import ProcessModelService
from services.lca_report_service import LCAReportService
from exceptions import ErrorMessages

DEFAULT_FUNCTIONAL_UNIT_VOLUME = 1.0
DEFAULT_FUNCTIONAL_TYPE = ""

router = APIRouter()


@router.post("/{tenant_id}")
async def create_product(
    tenant_id: str,
    product: CreateProductRequest,
) -> ProductSQLModel:
    products = ProductRepo(tenant_id)
    product_attributes = ProductAttributesRepo(tenant_id)
    existing_product = products.get_product(product.product_id)
    if existing_product:
        raise HTTPException(
            status_code=400,
            detail=ErrorMessages.PRODUCT_WITH_PRODUCT_ID_ALREADY_EXISTS.value.format(
                product_id=product.product_id,
            ),
        )

    try:
        product = add_default_factory_locale(product)
        created_product = products.create_product(
            ProductSQLModel(
                **product.dict(exclude={"tags": True}),
                tags=",".join(product.tags) if product.tags else None,
                manufacturer=product.brand or None,
            ),
        )

        # Associate backed up product attributes to the new product
        if product.old_product_id:
            product_attributes.update_product_id(
                product_id=product.old_product_id,
                new_product_id=created_product.product_id
            )

        return created_product
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Error creating product: {error}",
        ) from error


@router.delete("/{tenant_id}/{product_id:path}")
async def delete_product(
    tenant_id: str,
    product_id: str,
) -> None:
    products = ProductRepo(tenant_id)
    existing_product = products.get_product(product_id)
    if not existing_product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id
            ),
        )

    try:
        clones = products.get_product_clones(product_id)
        products.delete_product(product_id)
        for clone in clones:
            products.delete_product(clone.product_id)

        return {"message": "Product deleted successfully"}
    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting product: {error}",
        ) from error


@router.put("/{tenant_id}/backup-product/{product_id:path}")
async def rename_product(
    tenant_id: str,
    product_id: str,
) -> ProductSQLModel:
    products = ProductRepo(tenant_id)
    product = products.get_product(product_id)
    if not product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id,
            ),
        )
    try:
        new_product_id = f"{product_id}_backup"
        new_product_name = f"{product.product_name}_backup"
        updated_product = products.rename_product(
            product_id, new_product_id, new_product_name
        )

        return products.update_product_status(
            updated_product.product_id, ProductStatus.TRANSIENT
        )
    except Exception as error:
        raise HTTPException(
            status_code=500, detail=f"Error backing up product: {error}"
        ) from error


@router.put("/{tenant_id}/restore-product/{product_id:path}")
async def restore_product(
    tenant_id: str,
    product_id: str,
) -> ProductSQLModel:
    products = ProductRepo(tenant_id)
    product = products.get_product(product_id)
    if not product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id,
            ),
        )
    try:
        new_product_id = product_id.replace("_backup", "")
        new_product_name = product.product_name.replace("_backup", "")
        updated_product = products.rename_product(
            product_id, new_product_id, new_product_name
        )

        return products.update_product_status(
            updated_product.product_id, ProductStatus.ACTIVE
        )
    except Exception as error:
        raise HTTPException(
            status_code=500, detail=f"Error restoring product: {error}"
        ) from error


@router.get("/{tenant_id}")
async def get_products(
    tenant_id: str,
    product_type: str | None = Query(None),
) -> Products:
    internal_products = ProductRepo(tenant_id).get_products(product_type)
    # Convert internal product objects to pydantic Product objects
    products = []
    for internal_product in internal_products:
        if internal_product.status == ProductStatus.TRANSIENT:
            continue

        internal_product = add_default_image_url(internal_product)
        products.append(
            Product(
                product_name=internal_product.product_name,
                product_type=internal_product.product_type,
                product_id=internal_product.product_id,
                version=internal_product.version,
                supplier_id=internal_product.supplier_id,
                country_of_use=internal_product.country_of_use,
                brand=internal_product.brand,
                category=internal_product.primary_category,
                image_url=internal_product.product_image,
                cloned_from_product_id=internal_product.cloned_from_product_id,
                annual_sales_volume_units=internal_product.annual_sales_volume_units,
                tags=internal_product.tags,
                created_at=internal_product.created_at,
                updated_at=internal_product.updated_at
            )
        )

    return Products(products=products)


@router.get("/{tenant_id}/{product_id:path}")
async def get_product_details(
    tenant_id: str,
    product_id: str,
) -> ProductLCAInfo:
    products = ProductRepo(tenant_id)
    product = products.get_product(product_id)

    if not product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id,
            ),
        )

    if product.status not in [ProductStatus.INACTIVE, ProductStatus.TRANSIENT]:
        product = add_default_image_url(product)

    return ProductLCAInfo(
        product=product,
    )


@router.post("/{tenant_id}/{product_id:path}/supplier/{supplier_id}")
async def map_supplier_to_product(
    tenant_id: str,
    product_id: str,
    supplier_id: int,
) -> ProductSQLModel:
    try:
        products = ProductRepo(tenant_id)
        product = products.get_product(product_id)
        if not product:
            raise HTTPException(
                status_code=404,
                detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                    product_id=product_id,
                ),
            )

        supplier = SupplierRepo(tenant_id).get_supplier(supplier_id)
        if not supplier:
            raise HTTPException(
                status_code=404,
                detail=ErrorMessages.SUPPLIER_NOT_FOUND_BY_ID.value.format(
                    supplier_id=supplier_id
                ),
            )

        product_repo.update_product_supplier_id(
            tenant_id, product.product_id, supplier_id
        )

        products.update_product_lca_output(product.product_id, None)
        updated_product = product_repo.fetch_product_by_name(tenant_id, product.product_name)

        return Product(
            product_name=updated_product.product_name,
            product_id=updated_product.product_id,
            supplier_id=updated_product.supplier_id,
            country_of_use=updated_product.country_of_use,
            brand=updated_product.brand,
            category=updated_product.primary_category,
            image_url=updated_product.product_image,
            cloned_from_product_id=updated_product.cloned_from_product_id,
            annual_sales_volume_units=updated_product.annual_sales_volume_units,
        )

    except HTTPException as http_exception:
        raise http_exception

    except Exception as error:
        raise HTTPException(
            status_code=500, detail=f"Error mapping supplier to product: {error}"
        ) from error


@router.post("/{tenant_id}/{product_id:path}/clone-product")
async def clone_product(
    tenant_id: str,
    product_id: str,
) -> ProductSQLModel:
    products = ProductRepo(tenant_id)
    product = products.get_product(product_id)

    if not product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id,
            ),
        )

    existing_clone = products.get_product_clones(product.product_id)

    if existing_clone:
        raise HTTPException(
            status_code=400,
            detail=ErrorMessages.CLONE_FOR_PRODUCT_ALREADY_EXISTS.value,
        )

    if product.cloned_from_product_id:
        raise HTTPException(
            status_code=400,
            detail=ErrorMessages.CANNOT_CLONE_A_CLONE.value,
        )

    cloned_product_name = f"{product.product_name} (COPY)"
    product_clone = {
        key: value
        for key, value in product.dict().items()
        if key not in ["product_id", "cloned_from_product_id", "product_name"]
    }

    cloned_product = ProductSQLModel(
        product_id=f"{product.product_id}-CLONE-1",
        cloned_from_product_id=product.product_id,
        product_name=cloned_product_name,
        **product_clone,
    )

    process_model_service = ProcessModelService(tenant_id)
    try:
        nodes, edges = process_model_service.get_process_model_for_product(product.product_id)
        created_cloned_product = products.create_product(cloned_product)

        old_id_to_new_id = {}
        for node in nodes:
            new_node = process_model_service.create_node(
                Node(
                    **node.dict(
                        exclude={
                            "id": True,
                            "emissions_factor": True,
                            "location": True,
                            "product_id": True,
                            "supplier": True,
                        },
                    ),
                    supplier_id=node.supplier.id if node.supplier else None,
                    product_id=created_cloned_product.product_id,
                    emissions_factor_id=node.emissions_factor.id if node.emissions_factor else None,
                ),
                Address(
                    city=node.location.city,
                    country=node.location.country,
                ) if node.location else None,
            )

            old_id_to_new_id[node.id] = new_node.id

        for edge in edges:
            process_model_service.create_edge(
                from_node_id=old_id_to_new_id[edge.from_node_id],
                to_node_id=old_id_to_new_id[edge.to_node_id],
            )

        return created_cloned_product
    except Exception as error:
        products.delete_product(cloned_product.product_id)

        raise HTTPException(
            status_code=500,
            detail=f"Error cloning product: {error}",
        ) from error


@router.post("/{tenant_id}/{product_id:path}/activate")
async def activate_product(
    tenant_id: str,
    product_id: str,
) -> ProductSQLModel:
    products = ProductRepo(tenant_id)
    product = products.get_product(product_id)
    if not product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id
            ),
        )

    if product.status == ProductStatus.ACTIVE:
        return product

    return products.update_product_status(
        product_id,
        ProductStatus.ACTIVE,
    )


@router.post("/{tenant_id}/{product_id:path}/download-lca-report")
async def download_product_lca_report(
    tenant_id: str,
    product_id: str,
) -> FileResponse:
    products = ProductRepo(tenant_id)
    product = products.get_product(product_id)
    if not product:
        raise HTTPException(
            status_code=404,
            detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                product_id=product_id
            ),
        )

    if product.product_type != ProductType.PRODUCT.value:
        raise HTTPException(
            status_code=400,
            detail=f"LCA report is not available for products of type '{product.product_type}'.",
        )

    try:
        lca_report_service = LCAReportService(tenant_id)
        file_path = lca_report_service.create_lca_report(product_id)

        media_type = (
            "application/pdf"
            if file_path.endswith(".pdf")
            else "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )

        return FileResponse(
            file_path,
            media_type=media_type,
        )
    except HTTPException as http_exception:
        raise http_exception

    except Exception as error:
        traceback.print_exc()
        raise HTTPException(
            status_code=500,
            detail=f"Error downloading product LCA report: {error}",
        ) from error


@router.put("/{tenant_id}/{product_id:path}/tags")
async def update_product_tags(
    tenant_id: str,
    product_id: str,
    update_tags_request: UpdateTagsRequest,
) -> ProductSQLModel:
    products = ProductRepo(tenant_id)
    try:
        product = products.get_product(product_id)
        if not product:
            raise HTTPException(
                status_code=404,
                detail=ErrorMessages.PRODUCT_NOT_FOUND_BY_ID.value.format(
                    product_id=product_id
                ),
            )

        updated_product = products.update_product_tags(
            product.product_id, update_tags_request.tags
        )
        if not updated_product:
            raise HTTPException(
                status_code=500,
                detail="Failed to update product tags",
            )

        return updated_product

    except HTTPException as http_exception:
        raise http_exception

    except Exception as error:
        raise HTTPException(
            status_code=500,
            detail=f"Error updating product tags: {error}",
        ) from error
