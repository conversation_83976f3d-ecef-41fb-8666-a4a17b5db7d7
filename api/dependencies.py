from typing import Any, Callable
from utils.partitioned_cache import PartitionedCache, DiskCache
from utils.logger import logger_instance, Logger

logger = logger_instance.get_logger()
partitioned_cache = PartitionedCache(DiskCache)

def get_partitioned_cache() -> PartitionedCache:
    return partitioned_cache

def get_logger() -> Logger:
    return logger

def get_tenant_service(service: Callable[[str], Any]):
    def get_service_wrapper(tenant_id: str):
        return service(tenant_id)
    return get_service_wrapper
