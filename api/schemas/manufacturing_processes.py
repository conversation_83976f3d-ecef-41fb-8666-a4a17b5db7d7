from typing import List
from pydantic import BaseModel

class EmissionsFactor(BaseModel):
    activity_name: str
    geography: str
    reference_product: str
    kg_co2e: float | None = None
    source: str | None = None

class ManufacturingProcess(BaseModel):
    process_name: str
    geography_iso3: str | None = "WLD"
    electricity_kwh: float = 0
    water_liters: float = 0
    amount_of_product_kg: float = 1000
    emissions_factor: EmissionsFactor | None = None

class AddManufacturingRequest(BaseModel):
    processes: List[ManufacturingProcess]
