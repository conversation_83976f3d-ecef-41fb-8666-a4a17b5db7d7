from pydantic import BaseModel


class EmissionsFactorPayload(BaseModel):
    ingredient_name: str
    activity_name: str
    geography: str
    reference_product: str
    source: str

class SourceOfProcurementResponse(BaseModel):
    ingredient_name: str
    hscode: str
    product_category: str
    manufacturing_country: str
    country_code: str
    country: str
    country_capital: str | None = None
    latitude: float | None = None
    longitude: float | None = None
    locally_procured: bool = False
