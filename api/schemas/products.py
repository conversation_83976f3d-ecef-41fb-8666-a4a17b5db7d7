from datetime import datetime
from typing import List
from pydantic import BaseModel
from databases.tenant.models.product import CONST as PRODUCT_CONST
from databases.tenant.models import Product as ProductSQLModel
from services.emissions_factors_service import Exchange


class CreateProductRequest(BaseModel):
    product_name: str
    product_id: str
    product_type: str = "product"
    brand: str | None = None
    primary_category: str | None = None
    uses_per_package: float | None = None
    country_of_use: str | None = None
    functional_unit: str | None = None
    factory_country: str | None = None
    factory_city: str | None = None
    supplier_id: int | None = None
    product_image: str | None = None
    annual_sales_volume_units: int | None = None
    factory_locale: dict | None = PRODUCT_CONST.DEFAULTS.FactoryLocale()
    tags: List[str] | None = None
    old_product_id: str | None = None

class Product(BaseModel):
    product_name: str
    product_id: str
    product_type: str = "product"
    version: str
    brand: str | None = None
    country_of_use: str | None = None
    category: str | None = None
    supplier_id: int | None = None
    image_url: str | None = None
    total_emissions: float | None = None
    cloned_from_product_id: str | None = None
    annual_sales_volume_units: int | None = None
    tags: str | None = None
    created_at: datetime
    updated_at: datetime

class Products(BaseModel):
    products: List[Product]

class ProductLCAInfo(BaseModel):
    product: ProductSQLModel

class CreateAddress(BaseModel):
    country: str
    state_or_province: str | None = None
    city: str | None = None
    address_1: str | None = None
    latitude: float | None = None
    longitude: float | None = None

class CreateSupplier(BaseModel):
    supplier_name: str | None = None
    tax_registry_number: str | None = None
    primary_contact_name: str | None = None
    primary_contact_email: str | None = None
    primary_address: CreateAddress
    website: str | None = None

class EmissionsFactor(BaseModel):
    activity_name: str
    activity_type: str
    reference_product: str
    geography: str
    source: str
    unit: str = "kg"

class CreateProductIngredientRequest(BaseModel):
    ingredient_name: str
    weight: float
    unit: str
    wastage_rate: int | None = None
    proprietary_name: str | None = None
    cas_number: str | None = None
    supplier: CreateSupplier | None = None
    emissions_factor: EmissionsFactor
    exchanges: List[Exchange] | None = None
    recycled_content: int = 0

class CreateProductIngredientsRequest(BaseModel):
    ingredients: List[CreateProductIngredientRequest]

class ProductIngredientWeight(BaseModel):
    ingredient_name: str
    weight: float
    unit: str

class UpdateProductIngredientWeights(BaseModel):
    ingredient_weights: List[ProductIngredientWeight]

class CreatePackagingComponentRequest(BaseModel):
    packaging_material: str
    packaging_level: str
    packaging_item: str
    weight_grams: float
    country_of_origin: str | None = None
    city_of_origin: str | None = None
    origin_country_code: str | None = None
    destination_country: str | None = None
    destination_country_code: str | None = None
    destination_city: str | None = None
    supplier_id: int | None = None
    supplier_name: str | None = None
    supplier_origin_latitude: float | None = None
    supplier_origin_longitude: float | None = None
    destination_latitude: float | None = None
    destination_longitude: float | None = None
    recycled_content: int = 0
    recycled_material: bool = False

class UpdateTagsRequest(BaseModel):
    tags: List[str]
