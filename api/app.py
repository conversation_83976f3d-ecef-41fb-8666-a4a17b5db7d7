import asyncio
import pathlib
from datetime import datetime
import uuid
from fastapi.middleware.cors import CORSMiddleware
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.exceptions import RequestValidationError, ResponseValidationError
from fastapi.responses import J<PERSON>NResponse, PlainTextResponse
from filelock import FileLock
import sentry_sdk
from sentry_sdk.integrations.loguru import LoguruIntegration
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.integrations.sqlalchemy import SqlalchemyIntegration
from starlette.responses import Response
from starlette.middleware.base import BaseHTTPMiddleware
from config import config
from api.routers import (
    raw_materials_router,
    emissions_factors_router,
    products_router,
    suppliers_router,
    product_category_router,
    ingredients_router,
    product_attributes_router,
    products_v2_router,
    consumer_use_router,
    nodes_router,
    product_packaging_router,
    manufacturing_processes_router,
)
from api.utils.app_utils import SetupStatus, read_stream
from exceptions import ResourceNotFoundException
from utils.logger import logger_instance

logger = logger_instance.get_logger()
setup_lock = FileLock("/tmp/setup.lock")
setup_status = SetupStatus()

# Main FastAPI implementation module

def before_send(event, hint):
    if 'exc_info' in hint:
        _, exc_value, _ = hint['exc_info']
        if isinstance(exc_value, HTTPException) and exc_value.status_code in [404, 409]:
            return None
    return event

sentry_sdk.init(
    dsn=config.SENTRY_DSN,
    integrations=[
        LoguruIntegration(),
        FastApiIntegration(),
        StarletteIntegration(),
        SqlalchemyIntegration()
    ],
    send_default_pii=True,
    traces_sample_rate=1.0,
    profiles_sample_rate=1.0,
    before_send=before_send
)

async def run_setup():
    # Run the setup script asynchronously
    setup_lock.acquire()
    logger.info("Setup Server :: running background setup")
    start_time = datetime.now()
    command = [
        "python",
        str(pathlib.Path(__file__).parent.parent / "cli.py"),
        "--setup-server"
    ]

    if config.TESTING_MODE:
        command.append(f"--tenant-ids={config.TEST_TENANTS}")

    process = await asyncio.create_subprocess_exec(
        *command,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.PIPE,
    )

    await asyncio.gather(
        read_stream(process.stdout, "STDOUT"),
        read_stream(process.stderr, "STDERR")
    )

    await process.wait()
    setup_status.update(running=False, returncode=process.returncode)
    if process.returncode == 0:
        logger.info(
            f"Setup Server :: finished background setup | Elapsed :: {datetime.now() - start_time}"
        )
    else:
        logger.exception(
            f"Setup Server :: Error running setup script | Elapsed :: {datetime.now() - start_time}"
        )


class SetupStatusMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if setup_status.is_running():
            return Response(status_code=503)

        if setup_status.is_successful():
            return await call_next(request)

        return Response(content="Setup failed", status_code=500)


app = FastAPI()


@app.on_event("startup")
async def startup_event():
    if config.BACKGROUND_SEEDING:
        asyncio.create_task(run_setup())

if config.BACKGROUND_SEEDING:
    app.add_middleware(SetupStatusMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(products_v2_router, prefix="/v2/products")
app.include_router(consumer_use_router, prefix="/consumer-use")
app.include_router(nodes_router, prefix="/nodes")
app.include_router(products_router, prefix="/products")
app.include_router(product_category_router, prefix="/product-categories")
app.include_router(raw_materials_router, prefix="/raw-materials")
app.include_router(emissions_factors_router, prefix="/emissions-factors")
app.include_router(ingredients_router, prefix="/ingredients")
app.include_router(product_attributes_router, prefix="/product-attributes")
app.include_router(product_packaging_router, prefix="/product-packaging")
app.include_router(manufacturing_processes_router, prefix="/manufacturing-processes")
app.include_router(suppliers_router)


def get_url(request: Request) -> str:
    url = (
        f"{request.url.path}?{request.query_params}"
        if request.query_params
        else request.url.path
    )
    return url


@app.exception_handler(ResourceNotFoundException)
async def handle_resource_not_found_exception(
    request: Request, exc: ResourceNotFoundException
) -> PlainTextResponse:
    url = get_url(request)
    logger.warning(f"Resource not found: {exc.message} for url: {url}")
    return PlainTextResponse(status_code=404, content=exc.message)


# Exception handlers to log more information about these exceptions
@app.exception_handler(ResponseValidationError)
async def handle_response_validation_error(
    request: Request, exc: ResponseValidationError
) -> PlainTextResponse:
    errors = exc.errors()
    url = get_url(request)
    logger.warning(f"Response validation errors: {errors} for url: {url}")
    return PlainTextResponse(
        status_code=500,
        content="Validation of response failed with ResponseValidationError",
    )


@app.exception_handler(RequestValidationError)
async def handle_validation_error(
    request: Request, exc: RequestValidationError
) -> PlainTextResponse:
    errors = exc.errors()
    url = get_url(request)
    logger.warning(f"Request validation errors: {errors} for url: {url}")
    return PlainTextResponse(
        status_code=422,
        content="Validation of response failed with ResponseValidationError",
    )

@app.exception_handler(HTTPException)
async def global_http_exception_handler(request: Request, exc: HTTPException):
    # Use a request ID to correlate logs and errors
    request_id = str(uuid.uuid4())
    request_info = await _get_request_extra_info(request, request_id)

    # Log details internally
    logger.exception(
        f"Error occurred for URL: {request.url}\n"
        f"Exception: {exc.detail}\n"
        f"Request: {request_info}\n"
    )

    # Return a concise, safe message to the client:
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": exc.detail,
            "error": "Internal server error",
            "request_id": request_id
        }
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # Use a request ID to correlate logs and errors
    request_id = str(uuid.uuid4())
    request_info = await _get_request_extra_info(request, request_id)

    # Log details internally
    logger.exception(
        f"Error occurred for URL: {request.url}\n"
        f"Exception: {exc}\n"
        f"Request: {request_info}\n"
    )

    # Return a concise, safe message to the client:
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "request_id": request_id
        }
    )


async def _get_request_extra_info(request: Request, request_id: str) -> dict:
    """
    Safely extracts and formats request information for logging purposes.

    Args:
        request (Request): The FastAPI request object
        request_id (str): Unique identifier for the request

    Returns:
        dict: Formatted request information including method, URL, headers, body, etc.
    """
    return {
        "request_id": request_id,
        "url": str(request.url),
        "method": request.method,
        "headers": dict(request.headers),
        "query_params": dict(request.query_params),
        "client_host": request.client.host if request.client else None,
        "path_params": dict(request.path_params),
    }
