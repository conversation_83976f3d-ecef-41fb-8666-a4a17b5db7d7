from utils.logger import logger_instance

logger = logger_instance.get_logger()


async def read_stream(stream, label):
    while True:
        line = await stream.readline()
        if line:
            logger.info(f"{label}: {line.decode().strip()}")
        else:
            break


class SetupStatus:
    def __init__(self):
        self.running = True
        self.returncode = None

    def update(self, running, returncode=None):
        self.running = running
        self.returncode = returncode

    def is_running(self):
        return self.running

    def is_successful(self):
        return self.returncode == 0
