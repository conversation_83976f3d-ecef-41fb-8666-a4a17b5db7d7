from typing import List
from databases.shared.constants.node_type import NodeTypes
from databases.tenant.models import NodeRead
from databases.tenant.models.product import (
    Product as ProductSQLModel,
    CONST as PRODUCT_CONST,
)
from api.schemas.products import CreateProductRequest
from utils.geocoding import get_country_info


def add_default_image_url(product: ProductSQLModel) -> ProductSQLModel:
    if not product.product_image:
        new_product = ProductSQLModel.from_orm(product)
        new_product.product_image = PRODUCT_CONST.DEFAULTS.product_image
        return new_product
    return product

def extract_product_factory_location_from_nodes(nodes: List[NodeRead]):
    # Find the last product assembly node with bundle node type
    product_assembly_nodes = [
        node for node in nodes if node.node_type == NodeTypes.BUNDLE.value
        and node.name == "Product Assembly" and node.location and node.location.country is not None
    ]
    if product_assembly_nodes:
        return product_assembly_nodes[-1].location

    # Find the last production node that has location
    production_nodes = [
        node for node in nodes if node.node_type == NodeTypes.PRODUCTION.value
        and node.location and node.location.country is not None
    ]
    if production_nodes:
        return production_nodes[-1].location

    return None

def add_default_factory_locale(
    product_request: CreateProductRequest,
) -> CreateProductRequest:
    if product_request.factory_country:
        country_info = get_country_info(product_request.factory_country)
        if not country_info:
            raise Exception(f"Country not found: {product_request.factory_country}.")

        product_request.factory_locale = PRODUCT_CONST.DEFAULTS.FactoryLocale(
            city=(
                product_request.factory_city
                if product_request.factory_city
                else None
            ),
            country=country_info.name,
            code=country_info.alpha_3_code,
        )

    return product_request
