# pylint: disable=too-many-return-statements
import re

def match_product_ingredient_to_geography(ingredient_name: str) -> str:
    if re.search(r"water", ingredient_name, re.IGNORECASE):
        return "United Kingdom"
    if re.search(r"agua", ingredient_name, re.IGNORECASE):
        return "United Kingdom"
    if re.search(r"chloride", ingredient_name, re.IGNORECASE):
        return "United Kingdom"
    if re.search(r"fragrance", ingredient_name, re.IGNORECASE):
        return "France"
    if re.search(r"perfume", ingredient_name, re.IGNORECASE):
        return "France"
    if re.search(r"ase", ingredient_name, re.IGNORECASE):
        return "Denmark"
    if re.search(r"acid", ingredient_name, re.IGNORECASE):
        return "China"
    if re.search(r"yol", ingredient_name, re.IGNORECASE):
        return "United States"
    if re.search(r"Bagasse Pulp", ingredient_name, re.IGNORECASE):
        return "Brazil"
    if re.search(r"Bamboo pulp", ingredient_name, re.IGNORECASE):
        return "Thailand"
    if re.search(r"paper pulp", ingredient_name, re.IGNORECASE):
        return "United States"
    if re.search(r"wood pulp", ingredient_name, re.IGNORECASE):
        return "United States"
    return "Germany"
