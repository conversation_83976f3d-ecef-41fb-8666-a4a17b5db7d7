"""Initial revision

Revision ID: d15018bbfdc0
Revises: 
Create Date: 2024-01-30 14:50:46.631322

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = 'd15018bbfdc0'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('huggingface_versions',
    sa.Column('repo_id', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.Column('filename', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.Column('fingerprint', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.PrimaryKeyConstraint('repo_id', 'filename')
    )
    op.create_index(op.f('ix_huggingface_versions_filename'), 'huggingface_versions', ['filename'], unique=False)
    op.create_index(op.f('ix_huggingface_versions_repo_id'), 'huggingface_versions', ['repo_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_huggingface_versions_repo_id'), table_name='huggingface_versions')
    op.drop_index(op.f('ix_huggingface_versions_filename'), table_name='huggingface_versions')
    op.drop_table('huggingface_versions')
    # ### end Alembic commands ###
