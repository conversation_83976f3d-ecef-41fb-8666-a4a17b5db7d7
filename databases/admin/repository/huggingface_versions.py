from typing import Dict, List, Any
from sqlmodel import Session, select
from huggingface_hub import list_files_info
from databases.admin.models.huggingface_versions import HuggingfaceVersions
from databases.engine import engine_pool
from utils.logger import logger_instance
from utils.partitioned_cache import PartitionedCache, DiskCache

partitioned_cache = PartitionedCache(DiskCache)

logger = logger_instance.get_logger()


def get_partitioned_cache() -> PartitionedCache:
    return partitioned_cache


def serialize_repo_file(repo_file) -> Dict[str, Any]:
    return {
        "rfilename": repo_file.rfilename,
        "last_commit_date": repo_file.lastCommit.get("date"),
    }


def get_cached_files_info(repo_id: str) -> List[Dict[str, Any]]:
    _partitioned_cache = get_partitioned_cache()
    files_info = _partitioned_cache.get_cache("hf_files_info").get(repo_id)
    if not files_info:
        files_info = [
            serialize_repo_file(file)
            for file in list_files_info(repo_id, expand=True, repo_type="dataset")
        ]
        _partitioned_cache.get_cache("hf_files_info").set(repo_id, files_info)

    return files_info


def get_last_modified_date(
    repo_id: str,
    filename: str,
) -> str:
    """
    Gets the last modified date of the repo file.
    """
    try:
        files_info = get_cached_files_info(repo_id)
        file_info = next(filter(lambda x: x["rfilename"] == filename, files_info), None)
        if file_info:
            last_modified_date = file_info["last_commit_date"]
            return last_modified_date

        logger.info(f"File {filename} cache info not found for {repo_id}")
        return None
    except Exception:
        logger.exception(
            f"Failed to get last modified date for {filename} in {repo_id}"
        )
        return None


def get_latest_version_from_db(
    repo_id: str,
    filename: str,
) -> str:
    """
    Gets the latest version of the file in the database.
    """
    engine = engine_pool.get_admin_engine()

    with Session(engine) as session:
        statement = select(HuggingfaceVersions).where(
            HuggingfaceVersions.repo_id == repo_id,
            HuggingfaceVersions.filename == filename,
        )
        result = session.exec(statement).first()

        if not result:
            return None

    return result


def check_if_file_is_same_version(
    repo_id: str,
    filename: str,
    fingerprint: str,
) -> bool:
    """
    Checks if the file is the same version as the one in the database.
    """
    version = get_latest_version_from_db(repo_id, filename)
    if not version:
        return False

    return version.fingerprint == fingerprint


def update_latest_version(
    repo_id: str,
    filename: str,
    fingerprint: str,
) -> None:
    """
    Replaces the version of the file in the database.
    """
    engine = engine_pool.get_admin_engine()

    with Session(engine) as session:
        existing_version = get_latest_version_from_db(repo_id, filename)

        if existing_version:
            existing_version.fingerprint = fingerprint
            session.add(existing_version)
            session.commit()
            return

        new_record = HuggingfaceVersions(
            repo_id=repo_id, filename=filename, fingerprint=fingerprint
        )
        session.add(new_record)
        session.commit()
        return
