from typing import List
from sqlmodel import Session, select, text
from sqlalchemy.dialects.mysql import dialect
from databases.engine import engine_pool
from databases.shared.models import EmissionsFactors
from databases.common.emissions_factor_query import build_emissions_factor_search_query
from exceptions import DatabaseError
from utils.logger import logger_instance


class EmissionFactorRepo:
    def __init__(self):
        self.engine = engine_pool.get_shared_engine()

    def get_emissions_factor(
        self,
        activity_name: str,
        geography: str,
        reference_product: str,
        source: str,
    ) -> EmissionsFactors | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(EmissionsFactors)
                    .where(EmissionsFactors.activity_name == activity_name)
                    .where(EmissionsFactors.geography == geography)
                    .where(EmissionsFactors.reference_product == reference_product)
                    .where(EmissionsFactors.source == source)
                )

                # Add debug logging for the SQL query
                logger = logger_instance.get_logger()
                logger.debug(statement.compile(
                        dialect=dialect(), compile_kwargs={"literal_binds": True}
                    )
                )

                result = session.exec(statement).one_or_none()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_curated_emissions_factor(self, raw_material: str) -> EmissionsFactors | None:
        with Session(self.engine) as session:
            try:
                statement = select(EmissionsFactors).where(
                    text(
                        f"raw_material REGEXP '(^|,){raw_material}($|,)' "
                        f"OR raw_material = '{raw_material}'"
                    )
                )

                result = session.exec(statement).first()
                if not result:
                    return None

                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_sources(self, carbon_only: bool | None = False) -> List[str]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(EmissionsFactors.source)
                    .distinct()
                )
                if not carbon_only:
                    statement = statement.filter(EmissionsFactors.source.startswith("Ecoinvent"))

                sources = session.exec(statement).all()
                return sources
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def search_emissions_factors(
        self,
        activity_name: str,
        geography: str | None = None,
        unit: str | None = None,
        source: str | None = None,
        paging: int | None = 200,
        carbon_only: bool | None = False,
    ) -> List[EmissionsFactors]:
        with Session(self.engine) as session:
            try:
                statement = build_emissions_factor_search_query(
                    session=session,
                    model=EmissionsFactors,
                    activity_name=activity_name,
                    geography=geography,
                    unit=unit,
                    source=source,
                    paging=paging,
                    carbon_only=carbon_only
                )

                result = session.exec(statement).all()
                return [r[0] for r in result]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

emissions_factors = EmissionFactorRepo()
