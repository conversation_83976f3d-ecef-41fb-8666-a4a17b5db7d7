from typing import List, Any
from sqlmodel import Session


def query(query_str: str, params: dict, engine: Any) -> List[Any] | None:
    try:
        with Session(engine) as session:
            result = session.execute(query_str, params=params)
            return result.all()
    except Exception as error:
        raise error

# pylint: disable=all
from .consumer_use import *
from .country import *
from .domestic_production_data import *
from .emissions_factors import emissions_factors
from .intermediate_exchange import intermediate_exchanges
from .raw_material import raw_material_repo
from .manufacturing_process import manufacturing_processes
from .waste_disposal_method_emission import *
from .product_category import *
from .product_category import product_categories
from .sea_port import *
from .wits_product_data import *
from .wits_trade_data import *
from .packaging_prediction import *
from .node_type import node_types
from .pcr import PCRRepository
