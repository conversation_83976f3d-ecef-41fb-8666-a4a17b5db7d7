from sqlmodel import Session, select
from databases.shared.models import PCR, PCRRule
from databases.engine import engine_pool


class PCRRepository:
    def _get_engine(self):
        return engine_pool.get_shared_engine()

    def get_pcr(self, pcr_id: int):
        with Session(self._get_engine()) as session:
            return session.get(PCR, pcr_id)

    def get_pcr_rules_by_pcr_name(self, pcr_name: str):
        with Session(self._get_engine()) as session:
            statement = (
                select(PCRRule)
                .join(PCR, PCRRule.pcr_id == PCR.id)
                .where(PCR.name == pcr_name)
                .order_by(PCRRule.sequence_number)
            )
            return session.exec(statement).all()

    def get_all_pcrs(self):
        """Get all PCRs from the repository."""
        with Session(self._get_engine()) as session:
            statement = select(PCR)
            return session.exec(statement).all()
