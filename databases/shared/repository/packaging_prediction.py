from typing import List
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from databases.engine import engine_pool
from databases.shared.models import PackagingPrediction, ProductCategory
from exceptions import DatabaseError


class PackagingPredictionRepo:
    def __init__(self) -> None:
        self.engine = engine_pool.get_shared_engine()

    def get_packaging_by_category(
        self, product_category: str, packaging_level: str | None = None
    ) -> List[PackagingPrediction] | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(PackagingPrediction)
                    .options(selectinload(PackagingPrediction.product_category))
                    .join(PackagingPrediction.product_category)
                    .where(ProductCategory.name == product_category)
                )

                if packaging_level:
                    statement = statement.where(
                        PackagingPrediction.packaging_level == packaging_level
                    )

                results = session.exec(statement).all()
                return results
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error


package_prediction_repo = PackagingPredictionRepo()
