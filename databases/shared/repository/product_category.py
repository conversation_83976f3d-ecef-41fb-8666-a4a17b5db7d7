from typing import List
from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import ProductCategory
from exceptions import DatabaseError

class ProductCategoryRepo:
    def __init__(self) -> None:
        self.engine = engine_pool.get_shared_engine()

    def get_product_category_by_name(self, category_name: str) -> ProductCategory | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(ProductCategory)
                    .where(ProductCategory.name == category_name)
                )
                result = session.exec(statement).one_or_none()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product_category(self, category_id: int) -> ProductCategory | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(ProductCategory)
                    .where(ProductCategory.id == category_id)
                )
                result = session.exec(statement).one_or_none()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product_categories(self) -> List[ProductCategory]:
        with Session(self.engine) as session:
            try:
                statement = select(ProductCategory)
                result = session.exec(statement).all()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

product_categories = ProductCategoryRepo()
