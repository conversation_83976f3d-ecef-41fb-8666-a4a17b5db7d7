from databases.engine import engine_pool
from utils.logger import logger_instance
from . import query

logger = logger_instance.get_logger()

def fetch_trade_data(hscode, trade_flow, country):
    engine = engine_pool.get_shared_engine()
    sql_query = """
    SELECT
        country.name AS country_name,
        country.code AS country_code,
        wits_trade_data.trade_value,
        wits_trade_data.qty AS trade_qty,
        wits_trade_data.hsc,
        wits_product_data.name AS category_name
    FROM
        wits_trade_data
    INNER JOIN
        country ON country.id = wits_trade_data.partner_id
    INNER JOIN
        wits_product_data ON wits_product_data.hsc = wits_trade_data.hsc
    WHERE
        wits_trade_data.hsc = :hscode
        AND wits_trade_data.reporter_id = :country
        AND wits_trade_data.trade_flow = :trade_flow
    ORDER BY
        wits_trade_data.trade_value DESC
    LIMIT 6;
    """
    param = {"hscode": hscode, "trade_flow": trade_flow, "country": country}
    try:
        trade_data = query(sql_query, param, engine)
        if not trade_data:
            return None
        return trade_data
    except Exception as error:
        logger.exception(
            f"""
            SQL::Error fetching trade data for:
            hscode={hscode}
            trade_flow={trade_flow}
            country={country}
            Exception: {error}
            """,
        )
        raise error
