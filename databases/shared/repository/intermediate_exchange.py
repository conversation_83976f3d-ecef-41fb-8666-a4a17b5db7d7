from typing import List
from sqlalchemy.orm import aliased, selectinload
from sqlalchemy.dialects.mysql import dialect
from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import (
    IntermediateExchange,
    IntermediateExchangeRead,
    EmissionsFactors,
)
from exceptions import DatabaseError
from utils.logger import logger_instance

class IntermediateExchangeRepo:
    def __init__(self):
        self.engine = engine_pool.get_shared_engine()

    def get_all_for_emissions_factor(
        self,
        activity_name: str,
        reference_product: str,
        geography: str,
        source: str,
    ) -> List[IntermediateExchangeRead]:
        with Session(self.engine) as session:
            try:
                parent_emissions_factor = aliased(EmissionsFactors)
                statement = (
                    select(IntermediateExchange)
                    .options(
                        selectinload(IntermediateExchange.exchange_emissions_factor),
                        selectinload(IntermediateExchange.parent_emissions_factor)
                    )
                    .join(parent_emissions_factor, IntermediateExchange.parent_emissions_factor)
                    .where(parent_emissions_factor.activity_name == activity_name)
                    .where(parent_emissions_factor.geography == geography)
                    .where(parent_emissions_factor.reference_product == reference_product)
                    .where(parent_emissions_factor.source == source)
                )

                result = session.exec(statement).all()
                logger = logger_instance.get_logger()
                logger.debug(
                    statement.compile(
                        dialect=dialect(), compile_kwargs={"literal_binds": True}
                    )
                )

                return [
                    IntermediateExchangeRead(
                        **ie.dict(),
                        parent_emissions_factor=ie.parent_emissions_factor.dict(),
                        exchange_emissions_factor=ie.exchange_emissions_factor.dict(),
                    ) for ie in result
                ]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

intermediate_exchanges = IntermediateExchangeRepo()
