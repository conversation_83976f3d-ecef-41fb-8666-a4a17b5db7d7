from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import RawMaterial
from exceptions import DatabaseError


class RawMaterialRepo:
    def __init__(self) -> None:
        self.engine = engine_pool.get_shared_engine()

    def get_raw_material(self, name: str) -> RawMaterial | None:
        with Session(self.engine) as session:
            try:
                result = session.exec(
                    select(RawMaterial).where(RawMaterial.name == name),
                ).first()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_raw_materials(
        self,
        is_packaging: bool | None=None,
        is_textile: bool | None=None,
    ) -> list[RawMaterial]:
        with Session(self.engine) as session:
            try:
                results = session.exec(select(RawMaterial).where(
                    (
                        (RawMaterial.is_packaging == is_packaging)
                        if is_packaging is not None
                        else True
                    ),
                    (
                        (RawMaterial.is_textile == is_textile)
                        if is_textile is not None
                        else True
                    )
                )).all()
                return results
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

raw_material_repo = RawMaterialRepo()
