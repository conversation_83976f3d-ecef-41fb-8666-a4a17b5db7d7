from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import Country
from utils.logger import logger_instance
from . import query


logger = logger_instance.get_logger()

def fetch_countries() -> Country:
    engine = engine_pool.get_shared_engine()
    try:
        with Session(engine) as session:
            countries = session.query(Country).all()
            return countries

    except Exception as error:
        logger.exception(f"SQL::Error fetching countries {error}")
        raise error

def fetch_country_id_by_name(country_name: str) -> int | None:
    engine = engine_pool.get_shared_engine()
    country = query(
        "SELECT id FROM country WHERE name = :country_name OR code = :country_name;",
        {"country_name": country_name},
        engine=engine,
    )

    if not country:
        return None

    return country[0][0]

def fetch_country_by_name(country_name: str) -> Country | None:
    engine = engine_pool.get_shared_engine()
    country = query(
        "SELECT * FROM country WHERE name = :country_name OR code = :country_name;",
        {"country_name": country_name},
        engine=engine,
    )

    if not country:
        return None

    return Country(**country[0])
