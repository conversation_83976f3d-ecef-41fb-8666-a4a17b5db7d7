from typing import List
from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import NodeType
from exceptions import DatabaseError


class NodeTypeRepo:

    def __init__(self):
        self.engine = engine_pool.get_shared_engine()

    def get_all(self) -> List[NodeType]:
        with Session(self.engine) as session:
            try:
                statement = select(NodeType)
                records = session.exec(statement).all()
                return records
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_by_name(self, name: str) -> NodeType | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(NodeType)
                  .where(NodeType.name == name)
                )
                node_type = session.exec(statement).one_or_none()

                return node_type
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

node_types = NodeTypeRepo()
