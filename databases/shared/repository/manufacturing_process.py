from typing import List
from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import ManufacturingProcess
from exceptions import DatabaseError

class ManufacturingProcessRepo:
    def __init__(self) -> None:
        self.engine = engine_pool.get_shared_engine()

    def get_all(self) -> List[ManufacturingProcess]:
        with Session(self.engine) as session:
            try:
                statement = select(ManufacturingProcess)
                result = session.exec(statement).all()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

manufacturing_processes = ManufacturingProcessRepo()
