from databases.engine import engine_pool
from utils.logger import logger_instance
from . import query


logger = logger_instance.get_logger()

def fetch_consumer_usage(product_category: int, country: str):
    engine = engine_pool.get_shared_engine()
    sql_query = """
    SELECT product_category.name AS product_category,
    JSON_ARRAYAGG(JSON_OBJECT(
        'resource', consumer_use.resource,
        'consumer_use', consumer_use.consumer_use,
        'stream', consumer_use.stream,
        'amount', consumer_use.amount,
        'unit', consumer_use.unit,
        'source', consumer_use.source
    )) AS consumer_usage
    FROM   consumer_use
        INNER JOIN product_category
                ON product_category.id =
                    consumer_use.product_category_id
    WHERE  consumer_use.product_category_id = :product_category
        AND consumer_use.country = :country 
    GROUP BY product_category.name, consumer_use.country
    """
    param = {"country": country, "product_category": product_category}
    try:
        consumer_usage = query(sql_query, param, engine)
        if not consumer_usage:
            return None
        return consumer_usage[0]
    except Exception as error:
        logger.exception(
            f"""
            SQL::Error fetching consumer usage for:
            country={country}
            product_category={product_category}
            """,
        )
        raise error
