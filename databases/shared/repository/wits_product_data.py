from databases.engine import engine_pool
from utils.logger import logger_instance
from . import query


logger = logger_instance.get_logger()

def fetch_hscode_product_category(hscode) -> str | None:
    engine = engine_pool.get_shared_engine()
    sql_query = "SELECT name FROM wits_product_data WHERE hsc = :hscode LIMIT 1;"
    param = {"hscode": hscode}
    try:
        wits_product_hscode = query(sql_query, param, engine)
        if not wits_product_hscode:
            return None

        return wits_product_hscode[0]["name"]
    except Exception as error:
        logger.exception("SQL::Error fetching hscode name")
        raise error
