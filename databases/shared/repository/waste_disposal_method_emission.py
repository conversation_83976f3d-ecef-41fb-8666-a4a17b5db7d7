from typing import List
from sqlmodel import Session, select
from sqlalchemy.orm import aliased
from databases.engine import engine_pool
from databases.shared.models import (
    ProductCategory,
    WasteDisposalMethodEmission,
)
from exceptions import DatabaseError


class WasteDisposalMethodEmissionRepo:
    def __init__(self) -> None:
        self.engine = engine_pool.get_shared_engine()

    def get_landfill_by_country(self, country: str) -> WasteDisposalMethodEmission:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(WasteDisposalMethodEmission)
                    .where(WasteDisposalMethodEmission.waste_disposal_method == "Landfilling")
                    .where(WasteDisposalMethodEmission.country == country)
                    .where(WasteDisposalMethodEmission.packaging_material.is_(None))
                )
                result = session.exec(statement).first()

                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_by_packaging_material(
        self,
        packaging_material: str,
        country: str,
    ) -> List[WasteDisposalMethodEmission]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(WasteDisposalMethodEmission)
                    .where(WasteDisposalMethodEmission.packaging_material == packaging_material)
                    .where(WasteDisposalMethodEmission.country == country)
                )
                result = session.exec(statement).all()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_by_product_category(
        self,
        product_category: str,
        country: str
    ) -> List[WasteDisposalMethodEmission]:
        with Session(self.engine) as session:
            try:
                product_category_ = aliased(ProductCategory, name="product_category")
                statement = (
                    select(WasteDisposalMethodEmission)
                    .join(
                        product_category_,
                        WasteDisposalMethodEmission.product_category_id == product_category_.id
                    )
                    .where(product_category_.name == product_category)
                    .where(WasteDisposalMethodEmission.country == country)
                )
                result = session.exec(statement).all()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error
