from typing import List
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from sqlalchemy.dialects.mysql import dialect
from databases.engine import engine_pool
from databases.shared.models import EmissionsFactorValue
from databases.shared.models.emissions_factor_value import EmissionFactorValueRW
from exceptions import DatabaseError
from utils.logger import logger_instance


class EmissionFactorValueRepo:
    def __init__(self):
        self.engine = engine_pool.get_shared_engine()

    def get_emissions_factor_values(self, emissions_factor_id: int) -> List[EmissionFactorValueRW]:
        """
        Get detailed information about emissions factor values including LCIA methods
        and impact categories.

        Args:
            emissions_factor_id: ID of the emissions factor to fetch details for

        Returns:
            List of EmissionFactorValueRW objects containing LCIA method, impact category details
            and amount for each value
        """
        with Session(self.engine) as session:
            try:
                statement = (
                    select(EmissionsFactorValue)
                    .options(
                        selectinload(EmissionsFactorValue.impact_indicator),
                    )
                    .where(
                        EmissionsFactorValue.emissions_factor_id == emissions_factor_id
                    )
                )
                logger = logger_instance.get_logger()
                logger.debug(
                    statement.compile(
                        dialect=dialect(), compile_kwargs={"literal_binds": True}
                    )
                )
                values = session.exec(statement).all()

                return [
                    EmissionFactorValueRW(
                        lcia_method=value.impact_indicator.lcia_method,
                        impact_category_name=value.impact_indicator.category,
                        impact_category_indicator=value.impact_indicator.indicator,
                        impact_category_unit=value.impact_indicator.unit,
                        amount=value.amount,
                    )
                    for value in values
                ]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

emission_factor_value = EmissionFactorValueRepo()
