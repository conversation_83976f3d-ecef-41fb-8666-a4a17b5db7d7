from databases.engine import engine_pool
from utils.logger import logger_instance
from . import query


logger = logger_instance.get_logger()

def fetch_domestic_production_data(hscode, country):
    engine = engine_pool.get_shared_engine()
    sql_query = """
    SELECT
        domestic_production_data.name AS category_name,
        domestic_production_data.production_value_usd,
        domestic_production_data.hsc
    FROM
        domestic_production_data
    WHERE
        domestic_production_data.hsc = :hscode
        AND domestic_production_data.country = :country
    ORDER BY domestic_production_data.year DESC
    LIMIT 1;
    """
    param = {"hscode": int(hscode), "country": country}
    try:
        domestic_production_data = query(sql_query, param, engine)
        if not domestic_production_data:
            return None

        return domestic_production_data
    except Exception as error:
        logger.exception(
            f"SQL::Error fetching domestic production data for hscode={hscode}"
        )
        raise error
