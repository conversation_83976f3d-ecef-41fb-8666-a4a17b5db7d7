from databases.engine import engine_pool
from databases.shared.models import SeaPort
from utils.logger import logger_instance
from . import query


logger = logger_instance.get_logger()

def fetch_closest_sea_port(
    latitude: float,
    longitude: float,
) -> SeaPort | None:
    engine = engine_pool.get_shared_engine()
    sea_port = query(
        """
        SELECT
            *,
            (
                6371 *
                ACOS(
                    COS(RADIANS(:latitude)) * COS(RADIANS(latitude)) *
                    COS(RADIANS(:longitude - longitude)) +
                    SIN(RADIANS(:latitude)) * SIN(RADIANS(latitude))
                )
            ) AS distance
        FROM
            sea_port
        ORDER BY
            distance
        LIMIT 1;
        """,
        {"latitude": latitude, "longitude": longitude},
        engine=engine,
    )

    if not sea_port:
        return None

    return SeaPort(**sea_port[0])
