from sqlmodel import (
    SQLModel,
    Field,
    PrimaryKeyConstraint,
)

class DomesticProductionData(SQLModel, table=True):
    __tablename__ = "domestic_production_data"
    __table_args__ = (
        PrimaryKeyConstraint(
            "hsc",
            "year",
            "country",
            name="domestic_production_data_composite_primary_key",
        ),
    )
    hsc: int = Field(nullable=False)
    year: int = Field(nullable=False)
    country: str = Field(max_length=10, nullable=False)
    name: str = Field(max_length=500, nullable=False)
    production_value_usd: float = Field(nullable=False)
    """production_value_usd: total value in $ thousand"""
