from sqlmodel import (
    SQLModel,
    Field,
    Column,
    Integer,
    ForeignKey,
)


class PCRRule(SQLModel, table=True):
    id: int = Field(default=None, primary_key=True)
    pcr_id: int = Field(
        nullable=False,
        sa_column=Column(
            Integer,
            ForeignKey(
                "pcr.id",
                ondelete="CASCADE",
            ),
            key="fk_pcr_id",
        ),
        index=True,
    )
    segment: str
    condition: str
    sequence_number: int
