from sqlmodel import (
    SQLModel,
    Field,
    Column,
    Integer,
    ForeignKey,
    PrimaryKeyConstraint,
    Relationship,
)
from .product_category import ProductCategory


class PackagingPrediction(SQLModel, table=True):
    __tablename__ = "packaging_prediction"
    __table_args__ = (
        PrimaryKeyConstraint(
            "product_category_id",
            "packaging_level",
            "packaging_material",
            "packaging_item",
            name="package_prediction_composite_primary_key",
        ),
    )
    product_category_id: int = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "product_category.id",
                ondelete="CASCADE",
            ),
        )
    )
    product_category: ProductCategory = Relationship()
    packaging_level: str = Field(max_length=15, nullable=False)
    packaging_material: str = Field(max_length=100, nullable=False)
    packaging_item: str = Field(max_length=100, nullable=False)
    weight_ratio_percentage: float = Field(nullable=False)
    recyclable: bool = Field(default=False)
    number_of_packages: int = Field(nullable=False, default=1)
