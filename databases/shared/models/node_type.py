from pydantic import validator
from sqlmodel import (
    SQLModel,
    Field,
)
from databases.shared.constants.node_type import NodeTypes


class NodeType(SQLModel, table=True):
    __tablename__ = "node_type"
    id: int = Field(primary_key=True)
    name: str = Field(nullable=False, unique=True)
    min_inputs: int = Field(nullable=False)
    max_inputs: int | None = Field(nullable=True)
    min_outputs: int = Field(nullable=False)
    max_outputs: int | None = Field(nullable=True)

    class Config:
        arbitrary_types_allowed = True

    @validator("name")
    def validate_name(cls, v):
        if v not in NodeTypes.__members__.values():
            raise ValueError(f"Invalid node type: {v}")
        return v
