from sqlmodel import (
    SQLModel,
    Field,
    Enum,
    Column,
    String,
    Foreign<PERSON>ey,
    BigInteger,
)


# pylint: disable=too-many-ancestors
class TradeFlow(str, Enum):
    IMPORT = "IMPORT"
    EXPORT = "EXPORT"


class WitsTradeData(SQLModel, table=True):
    __tablename__ = "wits_trade_data"
    id: int = Field(
        sa_column=Column(BigInteger(), primary_key=True, autoincrement=True)
    )
    hsc: str = Field(
        sa_column=Column(
            String(10),
            ForeignKey(
                "wits_product_data.hsc",
                ondelete="CASCADE",
            ),
            index=True,
            nullable=False,
        ),
    )
    reporter_id: int = Field(nullable=False, index=True)
    partner_id: int = Field(nullable=False, index=True)
    trade_flow: TradeFlow = Field(nullable=False, index=True)
    trade_value: float = Field(nullable=False)
    qty: float = Field(nullable=True)
    qty_unit: str = Field(max_length=10, nullable=True)
    year: int = Field(nullable=False, index=True)
