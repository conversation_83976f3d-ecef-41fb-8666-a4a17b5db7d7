from sqlmodel import SQLModel, Field, Column, Integer, ForeignKey

class ConsumerUse(SQLModel, table=True):
    __tablename__ = "consumer_use"
    country: str = Field(primary_key=True, max_length=10)
    product_category_id: int = Field(
        sa_column=Column(
            Integer,
            ForeignKey(
                "product_category.id",
                ondelete="CASCADE",
            ),
            primary_key=True,
            index=True,
        ),
    )
    resource: str = Field(primary_key=True, max_length=300)
    consumer_use: str = Field(primary_key=True, max_length=300, nullable=False)
    stream: str = Field(max_length=10, nullable=False)  # Input / Output
    unit: str = Field(max_length=20, nullable=False)
    amount: float
    source: str = Field(max_length=255)
