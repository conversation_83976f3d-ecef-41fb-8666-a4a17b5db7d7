from sqlmodel import SQLModel, Field, UniqueConstraint

class EmissionsFactors(SQLModel, table=True):
    __tablename__ = "emissions_factors"
    __table_args__ = (
        UniqueConstraint(
            "activity_name",
            "reference_product",
            "geography",
            "source",
            name="uq_emissions_factors_composite"
        ),
    )
    id: int = Field(primary_key=True)
    activity_name: str = Field(max_length=120)
    geography: str = Field(max_length=50)
    source: str = Field(max_length=120)
    activity_type: str = Field(max_length=50, nullable=False)
    reference_product: str = Field(max_length=120, nullable=False)
    reference_product_amount: float = Field(nullable=False)
    unit: str = Field(max_length=20, nullable=False)
    activity_description: str = Field(max_length=1000, nullable=False)
    raw_material: str | None = Field(max_length=120, nullable=True, index=True)
    """raw_material: name of the raw material (used for curated emissions factors)"""
