from pydantic import BaseModel
from sqlmodel import (
    SQLModel,
    Field,
    Column,
    Integer,
    ForeignKey,
    Relationship,
)

from .emissions_factors import EmissionsFactors
from .impact_indicator import ImpactIndicator


class EmissionsFactorValue(SQLModel, table=True):
    __tablename__ = "emissions_factor_value"
    emissions_factor_id: int = Field(
        nullable=False,
        sa_column=Column(
            Integer,
            ForeignKey(
                "emissions_factors.id",
                ondelete="CASCADE",
            ),
            key="fk_emissions_factor_id",
            primary_key=True,
        ),
        index=True,
    )
    emissions_factor: EmissionsFactors = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[EmissionsFactorValue.emissions_factor_id]",
        }
    )
    impact_indicator_id: int = Field(
        nullable=False,
        sa_column=Column(
            Integer,
            ForeignKey("impact_indicator.id", ondelete="CASCADE"),
            key="fk_impact_indicator_id",
            primary_key=True,
        ),
        index=True,
    )
    impact_indicator: ImpactIndicator = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[EmissionsFactorValue.impact_indicator_id]",
        }
    )
    amount: float


class EmissionFactorValueRW(BaseModel):
    lcia_method: str
    impact_category_name: str
    impact_category_indicator: str
    impact_category_unit: str
    amount: float
