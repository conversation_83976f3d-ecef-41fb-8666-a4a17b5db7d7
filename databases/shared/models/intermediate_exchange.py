from typing import Optional
from pydantic import BaseModel
from sqlmodel import (
    SQLModel,
    Field,
    Column,
    ForeignKey,
    Integer,
    Relationship
)
from .emissions_factors import EmissionsFactors


class IntermediateExchange(SQLModel, table=True):
    __tablename__ = "intermediate_exchange"
    id: int = Field(primary_key=True)
    exchange_name: str = Field(max_length=120)
    amount: float
    input_stream: bool
    unit: str = Field(max_length=64)
    exchange_emissions_factor_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "emissions_factors.id",
                ondelete="CASCADE",
            ),
            nullable=True
        )
    )
    parent_emissions_factor_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "emissions_factors.id",
                ondelete="CASCADE",
            ),
            nullable=True,
        )
    )
    exchange_emissions_factor: Optional[EmissionsFactors] = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[IntermediateExchange.exchange_emissions_factor_id]",
        }
    )
    parent_emissions_factor: Optional[EmissionsFactors] = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[IntermediateExchange.parent_emissions_factor_id]",
        }
    )

    class Config:
        orm_mode = True

class IntermediateExchangeRead(BaseModel):
    id: int = Field(primary_key=True)
    exchange_name: str = Field(max_length=120)
    amount: float
    input_stream: bool
    unit: str = Field(max_length=64)
    exchange_emissions_factor: Optional[EmissionsFactors]
    parent_emissions_factor: Optional[EmissionsFactors]
