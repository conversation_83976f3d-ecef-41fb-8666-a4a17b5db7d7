from typing import Optional
from sqlmodel import Field, SQLModel

class RawMaterial(SQLModel, table=True):
    __tablename__ = "raw_material"
    name: str = Field(primary_key=True, max_length=255)
    is_packaging: bool = Field(default=False)
    is_textile: bool = Field(default=False)
    description: Optional[str] = Field(default=None, nullable=True)
    cas_number: Optional[str] = Field(max_length=12, nullable=True)
    # Will be implemented in CAR-446
    emission_factor_id: Optional[int] = Field(nullable=True)
