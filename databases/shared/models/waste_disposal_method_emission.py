from typing import Optional
from sqlmodel import SQLModel, Field, Column, Integer, ForeignKey, Relationship
from .product_category import ProductCategory

class WasteDisposalMethodEmission(SQLModel, table=True):
    __tablename__ = "waste_disposal_method_emission"
    id: int = Field(primary_key=True)
    co2e_ef: float
    packaging_material: str | None = Field(nullable=True, index=True)
    product_category_id: Optional[int] = Field(
        nullable=True,
        sa_column=Column(
            Integer,
            ForeignKey(
                "product_category.id",
                ondelete="CASCADE",
            ),
        ),
        index=True,
    )
    waste_disposal_method: str = Field(nullable=False, index=True)
    country: str = Field(max_length=10)
    rate: float
    source: str = Field(nullable=True, max_length=500)  # You can adjust the max_length as needed
    # Relationships
    product_category: ProductCategory = Relationship()
