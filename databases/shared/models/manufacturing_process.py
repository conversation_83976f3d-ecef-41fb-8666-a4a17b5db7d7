from typing import Optional
from sqlmodel import (
    Field,
    SQLModel,
)

class ManufacturingProcess(SQLModel, table=True):
    __tablename__ = "manufacturing_process"
    process_name: str = Field(
        max_length=255,
        primary_key=True,
    )
    geography_iso3: str = Field(primary_key=True, max_length=3, default="WLD")
    electricity_kwh: float = Field(default=0)
    water_liters: float = Field(default=0)
    co2e_kg: Optional[float]
    amount_of_product_kg: float = Field(default=1000)
