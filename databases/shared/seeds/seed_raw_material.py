from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import RawMaterial
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance

logger = logger_instance.get_logger()

def seed_raw_material(engine, dataset: dict):
    with Session(engine) as session:
        raw_materials_to_populate = [
            RawMaterial(
                name=raw_material.get("Raw Material Name"),
                is_packaging=raw_material.get("Is Packaging Material"),
                is_textile=raw_material.get("Is Textile"),
                description=raw_material.get("Description"),
                cas_number=raw_material.get("CAS Number"),
            )
            for raw_material in dataset
        ]

        try:
            session.add_all(raw_materials_to_populate)
            session.commit()
            logger.success("Populated raw materials")
        except Exception as error:
            logger.exception(f"Error populating raw materials: {error}")
            session.rollback()

raw_material_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=RawMaterial.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="raw_materials.csv",
    seeding_function=seed_raw_material,
)
