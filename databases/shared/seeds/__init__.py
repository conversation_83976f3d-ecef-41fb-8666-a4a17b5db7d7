# pylint: disable=import-self
from config import config
from .seed_country import country_data_loader
from .seed_sea_ports import sea_ports_data_loader
from .seed_product_category import product_category_data_loader
from .seed_emissions_factors import emissions_factors_data_loader
from .seed_raw_material import raw_material_data_loader
from .seed_manufacturing_processes import manufacturing_process_data_loader
from .seed_wits_product_data import wits_product_data_data_loader
from .seed_package_prediction import package_prediction_data_loader
from .seed_waste_disposal_method_emission import (
    packaging_material_disposal_emissions_data_loader,
    product_category_disposal_emissions_data_loader,
)
from .seed_consumer_use import consumer_use_data_loader
from .seed_wits_trade_data import seed_wits_trade_data
from .seed_domestic_production_data import seed_domestic_production_data
from .seed_domestic_production_data_us import us_domestic_production_data_loader
from .seed_node_type import node_types_data_loader
from .seed_pcr import pcr_data_loader


def seed_all():
    pcr_data_loader.seed_data()
    country_data_loader.seed_data()
    node_types_data_loader.seed_data()
    sea_ports_data_loader.seed_data()
    product_category_data_loader.seed_data()
    emissions_factors_data_loader.seed_data()
    raw_material_data_loader.seed_data()
    manufacturing_process_data_loader.seed_data()
    wits_product_data_data_loader.seed_data()
    package_prediction_data_loader.seed_data()
    packaging_material_disposal_emissions_data_loader.seed_data()
    product_category_disposal_emissions_data_loader.seed_data()
    consumer_use_data_loader.seed_data()
    seed_domestic_production_data()
    us_domestic_production_data_loader.seed_data()
    seed_wits_trade_data()
