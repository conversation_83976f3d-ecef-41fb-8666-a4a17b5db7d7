import unicodedata
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import SeaPort
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance


logger = logger_instance.get_logger()

def unicode_to_ascii(s: str) -> str:
    return unicodedata \
        .normalize("NFKD", s) \
        .encode("ASCII", "ignore") \
        .decode("utf-8")

def seed_sea_ports(engine, dataset: dict):
    ports = []
    for port in dataset:
        city, country = [
            unicode_to_ascii(x.strip())
            for x in port["location"].split(",")
        ][:2]

        if not city:
            continue

        sea_port = SeaPort(
            city=city,
            country=country,
            latitude=port["latitude"],
            longitude=port["longitude"],
        )

        ports.append(sea_port)

    with Session(engine) as session:
        try:
            session.add_all(ports)
            session.commit()
            logger.success("Populated sea ports")
        except Exception as error:
            logger.exception(f"Error populating sea ports: {error}")
            session.rollback()

sea_ports_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=SeaPort.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="sea_ports.csv",
    seeding_function=seed_sea_ports,
)
