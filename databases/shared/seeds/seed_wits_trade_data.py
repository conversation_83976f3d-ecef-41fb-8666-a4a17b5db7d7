from sqlmodel import Session, select
from huggingface_hub import hf_hub_download
from config import config
from databases.engine import engine_pool
from databases.shared.models import WitsTradeData
from utils.logger import logger_instance

logger = logger_instance.get_logger()


# pylint: disable=line-too-long
def seed_wits_trade_data():
    filename = "wits_trade_data_2021_v2.csv"
    csv_headers = [
        "hsc",
        "trade_flow",
        "trade_value",
        "qty",
        "qty_unit",
        "year",
        "reporter_id",
        "partner_id",
    ]

    if config.TESTING_MODE:
        filename = "wits_trade_data_v2.csv"
        csv_headers = [
            "hsc",
            "trade_flow",
            "trade_value",
            "qty",
            "qty_unit",
            "year",
            "reporter_id",
            "partner_id",
        ]

    hf_hub_download(
        repo_id="CarbonBright/database-seed-data",
        filename=filename,
        repo_type="dataset",
        local_dir="./storage/cache",
    )

    engine = engine_pool.get_shared_engine()

    if config.FORCE_RESEED_TRADE_DATA:
        with Session(engine) as session:
            session.execute("TRUNCATE TABLE wits_trade_data")
            session.commit()
            logger.info("Deleted WITS Trade Data")

    with Session(engine) as session:
        result = session.execute(select(WitsTradeData).limit(1)).scalar_one_or_none()
        logger.info(f"Wits Trade Data: {result}")
        if result:
            logger.info("WITS Trade Data already populated")
            return

    with Session(engine) as session:
        try:
            statement = f"""
            LOAD DATA LOCAL INFILE './storage/cache/{filename}' 
            INTO TABLE wits_trade_data 
            FIELDS TERMINATED BY ',' 
            ENCLOSED BY '"' 
            LINES TERMINATED BY '\n' 
            IGNORE 1 LINES 
            ({', '.join(csv_headers)});
            """
            session.execute(statement)
            session.commit()
            logger.success("Populated WITS Trade Data")
            return
        except Exception as error:
            session.rollback()
            logger.exception(f"Error populating WITS Trade Data: {error}")
            return
