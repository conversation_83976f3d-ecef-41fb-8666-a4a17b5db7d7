import re
from sqlmodel import Session, select
from huggingface_hub import hf_hub_download
from databases.engine import engine_pool
from databases.shared.models import DomesticProductionData
from config import config
from utils.logger import logger_instance


AVG_POUND_USD_CONVERSION_RATE_2022 = 1.2369

logger = logger_instance.get_logger()


def match_hsc(text):
    pattern = r'"(\d+)\s+\(CN\s+(.*?)\),\s+(.*?)"'
    match = re.search(pattern, text)
    if not match:
        return None

    cn_codes = match.group(2)
    product_name = match.group(3)
    return {"hsc": cn_codes.split("+"), "product_name": product_name}


def match_value(text):
    pattern = r'Value\s+(?:£000\'s|\'s|\£000\'s),\s*"(.*?)"'
    match = re.search(pattern, text)

    if not match:
        return None

    value = match.group(1).replace(",", "")  # Remove commas from value
    return {"value": value}


def reset_product_info():
    return {"hsc": []}


def parse_production_data(product_info, match):
    return [
        DomesticProductionData(
            hsc=int(hsc) if len(hsc) >= 6 else int(hsc + "0" * (6 - len(str(hsc)))),
            name=product_info["name"],
            production_value_usd=(
                float(match["value"]) * AVG_POUND_USD_CONVERSION_RATE_2022
            ),
            year=2022,
            country="GBR",
        )
        for hsc in product_info["hsc"]
    ]


def parse_file(file):
    product_info = {"hsc": []}
    parsed_data = []

    for line in file:
        if product_info["hsc"]:
            match = match_value(line)
            if match:
                parsed_data.extend(parse_production_data(product_info, match))
                product_info = reset_product_info()
                continue

        match = match_hsc(line)
        if match:
            product_info["hsc"] = [hsc.strip() for hsc in match["hsc"]]
            product_info["name"] = match["product_name"]

    return parsed_data


def seed_domestic_production_data():
    uk_prod_data_to_populate = []
    REPO_ID = "CarbonBright/database-seed-data"
    FILES = [
        "uk_domestic_prod_data_2022_div_20.csv",
        "uk_domestic_prod_data_2022_div_21.csv",
    ]

    try:
        engine = engine_pool.get_shared_engine()

        if config.FORCE_RESEED_TRADE_DATA:
            with Session(engine) as session:
                session.execute("DELETE FROM domestic_production_data WHERE country='GBR'")
                session.commit()
                logger.info("Deleted UK Domestic Production Data")

        with Session(engine) as session:
            result = session.execute(
                select(DomesticProductionData)
                .where(DomesticProductionData.country == "GBR")
                .limit(1)
            ).scalar_one_or_none()
            if result:
                logger.info("UK Domestic Production Data already populated")
                return

        files = [
            hf_hub_download(
                repo_id=REPO_ID,
                filename=file,
                repo_type="dataset",
                local_dir="./storage/cache",
            )
            for file in FILES
        ]

        for file in files:
            with open(file, "r", encoding="utf-8") as file:
                uk_prod_data_to_populate.extend(parse_file(file))

        with Session(engine) as session:
            try:
                session.add_all(uk_prod_data_to_populate)
                session.commit()
                logger.info("Populated Uk Production Data")
                return
            except Exception:
                session.rollback()
                logger.exception("SQL::Error populating Uk Production Data")
                return

    except Exception:
        logger.exception("Error extracting UK Production data from csv")
        return
