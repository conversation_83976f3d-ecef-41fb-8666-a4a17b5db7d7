from timeit import default_timer as timer
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import NodeType
from databases.shared.constants.node_type import NodeTypes
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance

logger = logger_instance.get_logger()

def seed_node_type(engine, dataset: dict):
    node_types_to_populate = [
        NodeType(
            name=NodeTypes(process["Name"].lower()).value,
            min_inputs=process["Min Input"],
            max_inputs=process["Max Input"],
            min_outputs=process["Min Output"],
            max_outputs=process["Max Output"],
        )
        for process in dataset
    ]

    with Session(engine) as session:
        try:
            start_timer = timer()
            session.add_all(node_types_to_populate)
            session.commit()
            duration = timer() - start_timer

            logger.success("Populated Node Types")
            logger.info(f"Duration: {duration:.2f}s")
        except Exception as error:
            logger.exception(f"Error populating node types: {error}")
            session.rollback()

node_types_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=NodeType.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="node_types_v2.csv",
    seeding_function=seed_node_type,
)
