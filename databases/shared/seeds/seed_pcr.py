from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import PCR, PCRRule
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance

logger = logger_instance.get_logger()


def seed_pcr(engine, dataset: dict):
    with Session(engine) as session:
        for pcr_data in dataset:
            pcr = PCR(
                name=pcr_data["name"],
                description=pcr_data["description"],
            )
            session.add(pcr)
            session.flush()
            pcr_id = pcr.id
            rules_to_populate = []
            for rule in pcr_data["rules"]:
                rules_to_populate.append(
                    PCRRule(
                        pcr_id=pcr_id,
                        segment=rule["segment"],
                        condition=rule["condition"],
                        sequence_number=rule["sequence_number"],
                    )
                )
            session.add_all(rules_to_populate)
        session.commit()

        logger.success("Populated PCR")

pcr_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=PCR.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="pcr.json",
    seeding_function=seed_pcr,
)
