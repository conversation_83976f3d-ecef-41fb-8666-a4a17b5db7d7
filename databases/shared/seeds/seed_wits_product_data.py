import unicodedata
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import WitsProductData
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance


logger = logger_instance.get_logger()

def unicode_to_ascii(s: str) -> str:
    return unicodedata \
        .normalize("NFKD", s) \
        .encode("ASCII", "ignore") \
        .decode("utf-8")

def seed_wits_product_data(engine, dataset: dict):
    products_to_populate = [
        WitsProductData(
            name=unicode_to_ascii(product["Name"].replace(f'{product["value"]} -- ', "")),
            hsc=product["value"],
        )
        for product in dataset
    ]

    with Session(engine) as session:
        try:
            session.add_all(products_to_populate)
            session.commit()
            logger.success("Populated WITS HS6 Product Codes")
        except Exception as error:
            logger.exception(f"Error populating WITS HS6 Product Codes: {error}")
            session.rollback()

wits_product_data_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=WitsProductData.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="hs6productlist.json",
    seeding_function=seed_wits_product_data,
)
