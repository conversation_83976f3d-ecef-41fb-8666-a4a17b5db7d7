from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import (
    ConsumerUse,
    ProductCategory,
)
from databases.utils.data_loader import DataLoader
from pipelines_v2.utils.unit import UnitType
from utils.logger import logger_instance


logger = logger_instance.get_logger()

def seed_consumer_use(engine, dataset: dict):
    with Session(engine) as session:
        consumer_uses_to_populate = []
        for consumer_use in dataset:
            for category_name in consumer_use["categories"]:
                category = session.exec(
                    select(ProductCategory)
                    .where(ProductCategory.name == category_name)
                ).one_or_none()

                if not category:
                    raise ValueError(f"Product category '{category_name}' not found.")

                try:
                    UnitType.get_by_abbrev(consumer_use["unit"])
                except Exception:
                    logger.warning(f"Invalid unit '{consumer_use['unit']}', skipping entry.")
                    continue

                consumer_uses_to_populate.append(
                    ConsumerUse(
                        country=consumer_use["country"],
                        product_category_id=category.id,
                        resource=consumer_use["resource"],
                        consumer_use=consumer_use["consumer_use"],
                        stream=consumer_use["stream"],
                        unit=consumer_use["unit"],
                        amount=consumer_use["amount"],
                        source=consumer_use["source"]
                    ),
                )

        try:
            session.add_all(consumer_uses_to_populate)
            session.commit()
            logger.success("Populated consumer uses")
        except Exception as error:
            logger.exception(f"Error populating consumer uses: {error}")
            session.rollback()

consumer_use_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=ConsumerUse.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="consumer_use.json",
    seeding_function=seed_consumer_use,
)
