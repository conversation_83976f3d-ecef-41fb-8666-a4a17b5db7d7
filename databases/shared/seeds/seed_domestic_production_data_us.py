from sqlmodel import Session, select
import pandas as pd
from databases.engine import engine_pool
from databases.shared.models import DomesticProductionData
from databases.utils.data_loader import DataLoader
from config import config
from utils.logger import logger_instance


logger = logger_instance.get_logger()

DOMESTIC_CONSUMPTION_FACTOR = 5.0


def seed_us_domestic_production_data(engine, dataset: dict):
    dataframe = pd.DataFrame(dataset)
    dataframe["Total Value"] = (
        dataframe["Total Value"].str.replace(",", "").astype(float)
    )
    result = (
        dataframe.groupby(["HS Code", "HS Code Description"])["Total Value"]
        .sum()
        .reset_index()
    )
    domestic_production_data_to_populate = [
        DomesticProductionData(
            hsc=row["HS Code"],
            name=row["HS Code Description"],
            production_value_usd=(
                ((row["Total Value"] / 1000) * DOMESTIC_CONSUMPTION_FACTOR) / 100
            ),
            year=2023,
            country="USA",
        )
        for _, row in result.iterrows()
    ]

    try:
        if config.FORCE_RESEED_TRADE_DATA:
            with Session(engine) as session:
                session.execute("DELETE FROM domestic_production_data WHERE country='USA'")
                session.commit()
                logger.info("Deleted USA Domestic Production Data")

        with Session(engine) as session:
            result = session.execute(
                select(DomesticProductionData)
                .where(DomesticProductionData.country == "USA")
                .limit(1)
            ).scalar_one_or_none()
            if result:
                logger.info("USA Domestic Production Data already populated")
                return

            session.add_all(domestic_production_data_to_populate)
            session.commit()
            logger.success("Populated USA Domestic Production Data")
    except Exception as error:
        logger.exception(f"Error populating USA Domestic Production Data: {error}")
        session.rollback()


us_domestic_production_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=DomesticProductionData.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="us_export_data_2023.csv",
    seeding_function=seed_us_domestic_production_data,
    truncate_table=False,
)
