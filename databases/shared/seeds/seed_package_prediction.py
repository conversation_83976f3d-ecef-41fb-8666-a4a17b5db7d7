from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import PackagingPrediction, ProductCategory
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance

logger = logger_instance.get_logger()


def seed_package_prediction(engine, dataset: dict):
    with Session(engine) as session:
        product_categories = session.exec(select(ProductCategory)).all()
        package_predictions_to_populate = []
        for package_prediction in dataset:
            product_category_id = next(
                (
                    pc.id
                    for pc in product_categories
                    if pc.name == package_prediction.get("Product Category")
                ),
                None,
            )
            package_predictions_to_populate.append(
                PackagingPrediction(
                    product_category_id=product_category_id,
                    packaging_level=package_prediction.get("Packaging Level"),
                    packaging_material=package_prediction.get("Packaging Material"),
                    packaging_item=package_prediction.get("Packaging Item"),
                    weight_ratio_percentage=package_prediction.get("Weight Ratio (%)"),
                    recyclable=package_prediction.get("Recyclable", False),
                    number_of_packages=package_prediction.get("Number of packages"),
                )
            )

        try:
            session.add_all(package_predictions_to_populate)
            session.commit()
            logger.success("Populated package predictions")
        except Exception as error:
            logger.exception(f"Error populating package predictions: {error}")
            session.rollback()


package_prediction_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=PackagingPrediction.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="package_prediction_v2.csv",
    seeding_function=seed_package_prediction,
)
