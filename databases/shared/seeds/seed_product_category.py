from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import ProductCategory
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance
from exceptions import DatabaseError


logger = logger_instance.get_logger()

def seed_product_category(engine, dataset: dict):
    categories = [
        ProductCategory(
            name=category["Category"].strip(),
            is_supported=category["Is Supported"] or False
        )
        for category in dataset
    ]

    with Session(engine) as session:
        try:
            session.add_all(categories)
            session.commit()
            logger.success("Populated product categories")
        except Exception as error:
            session.rollback()
            raise DatabaseError("Error Seeding Shared Database", error) from error

product_category_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=ProductCategory.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="product_categories.csv",
    seeding_function=seed_product_category,
)
