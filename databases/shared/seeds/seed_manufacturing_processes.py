from timeit import default_timer as timer
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import ManufacturingProcess
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance

logger = logger_instance.get_logger()

def seed_manufacturing_processes(engine, dataset: dict):
    manufacturing_processes_to_populate = [
        ManufacturingProcess(
            process_name=process["Process Name"],
            electricity_kwh=process["Electricity (kWh)"],
            water_liters=process["Water (L)"],
            co2e_kg=process["CO2e (kg)"],
            amount_of_product_kg=process["Amount Of Product (kg)"] or 1000,
        )
        for process in dataset
    ]

    with Session(engine) as session:
        try:
            start_timer = timer()
            session.add_all(manufacturing_processes_to_populate)
            session.commit()
            duration = timer() - start_timer

            logger.success("Populated Manufacturing Processes")
            logger.info(f"Duration: {duration:.2f}s")
        except Exception as error:
            logger.exception(f"Error populating manufacturing processes: {error}")
            session.rollback()

manufacturing_process_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=ManufacturingProcess.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="manufacturing_processes.csv",
    seeding_function=seed_manufacturing_processes,
)
