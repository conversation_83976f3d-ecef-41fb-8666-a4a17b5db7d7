import unicodedata
from sqlmodel import Session
from databases.engine import engine_pool
from databases.shared.models import Country
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance


logger = logger_instance.get_logger()

def unicode_to_ascii(s: str) -> str:
    return unicodedata \
        .normalize("NFKD", s) \
        .encode("ASCII", "ignore") \
        .decode("utf-8")

def seed_country(engine, dataset: dict):
    countries_to_populate = [
        Country(
            id=country["id"],
            code=country["code"],
            name=unicode_to_ascii(country["name"]),
            area_km2=country["area_km2"],
        )
        for country in dataset
    ]

    with Session(engine) as session:
        try:
            session.add_all(countries_to_populate)
            session.commit()
            logger.success("Populated country")
        except Exception as error:
            logger.exception(f"Error populating country: {error}")
            session.rollback()

country_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=Country.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="countries.json",
    seeding_function=seed_country,
)
