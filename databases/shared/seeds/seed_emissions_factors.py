from timeit import default_timer as timer
import json
from tqdm import tqdm
import pandas as pd
from sqlmodel import select, Session
from huggingface_hub import hf_hub_download
from databases.engine import engine_pool
from databases.shared.models import (
  EmissionsFactors,
  IntermediateExchange,
  EmissionsFactorValue,
  ImpactIndicator
)
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance

logger = logger_instance.get_logger()

def seed_emissions_factors(engine, dataset: dict):
    emissions_factors_to_populate = [
        EmissionsFactors(
            activity_name=emission_factor["Activity Name"],
            geography=emission_factor["Geography"],
            source=emission_factor["Source"],
            reference_product=emission_factor["Reference Product Name"],
            activity_type=emission_factor["Sector"],
            reference_product_amount=emission_factor["Reference Product Amount"],
            unit=emission_factor["Unit"],
            activity_description=emission_factor.get("Description") or "",
            raw_material=emission_factor["Raw Material"],
        )
        for emission_factor in dataset
        if emission_factor["Geography"]
    ]

    with Session(engine) as session:
        try:
            start_timer = timer()
            session.bulk_save_objects(emissions_factors_to_populate)
            session.commit()
            duration = timer() - start_timer
            logger.success("Populated emissions factors")
            logger.info(f"Duration: {duration:.2f}s")
        except Exception as error:
            session.rollback()
            raise error

    with Session(engine) as session:
        try:
            impact_indicator_to_id = {
                (
                    indicator.lcia_method,
                    indicator.category,
                    indicator.indicator
                ): indicator.id
                for indicator in session.exec(select(ImpactIndicator)).all()
            }

            emissions_factors = session.exec(
                select(EmissionsFactors)
            ).all()

            custom_emissions_factors = [
                emissions_factor
                for emissions_factor in emissions_factors
                if emissions_factor.source != "Ecoinvent 3.11"
            ]

            # Create impact indicator for carbon-only emissions factors if it doesn't exist
            if custom_emissions_factors:

                impact_indicator_id = impact_indicator_to_id.get(
                    (
                        "ReCiPe 2016 v1.03, midpoint (H)",
                        "climate change",
                        "global warming potential (GWP100)"
                    )
                )

                if not impact_indicator_id:
                    impact_indicator = ImpactIndicator(
                        lcia_method="ReCiPe 2016 v1.03, midpoint (H)",
                        category="climate change",
                        indicator="global warming potential (GWP100)",
                        unit="kg CO2-Eq",
                        description=""
                    )
                    session.add(impact_indicator)
                    session.flush()
                    impact_indicator_id = impact_indicator.id
                    impact_indicator_to_id[
                        (
                            "ReCiPe 2016 v1.03, midpoint (H)",
                            "climate change",
                            "global warming potential (GWP100)"
                        )
                    ] = impact_indicator_id

            # Add carbon-only emissions factor values
            for custom_emission_factor in tqdm(custom_emissions_factors):
                dataset_entry = next(
                    (
                        entry
                        for entry in dataset
                        if (
                            entry["Activity Name"] == custom_emission_factor.activity_name
                            and entry["Reference Product Name"] == (
                                custom_emission_factor.reference_product
                            )
                            and entry["Geography"] == custom_emission_factor.geography
                            and entry["Source"] == custom_emission_factor.source
                        )
                    ),
                    None
                )

                if not dataset_entry:
                    continue

                emissions_factor_value = EmissionsFactorValue(
                    emissions_factor_id=custom_emission_factor.id,
                    impact_indicator_id=impact_indicator_id,
                    amount=dataset_entry["Emissions Factor (kg CO2e)"],
                )
                session.add(emissions_factor_value)
            session.commit()
        except Exception as error:
            logger.exception(f"Error populating emissions factor: {error}")
            session.rollback()

    with Session(engine) as session:
        try:
            emissions_factors = session.exec(
                select(EmissionsFactors)
            ).all()
            activity_unique_key_to_id = {
                (
                    f"{activity.activity_name}-"
                    f"{activity.reference_product}-"
                    f"{activity.geography}"
                ): activity.id
                for activity in emissions_factors
            }
        except Exception as error:
            logger.exception(f"Error populating emissions factor: {error}")
            session.rollback()
            raise error

    activities_filepath = hf_hub_download(
        "CarbonBright/emissions-factors-activities-overview",
        "output_with_indicators.json",
        repo_type="dataset"
    )

    with open(activities_filepath, "r", encoding="utf-8") as json_file:
        activities = json.load(json_file)

    activity_name_lookup_filepath = hf_hub_download(
        "CarbonBright/emissions-factors-activities-overview",
        "FilenameToActivityLookup_v2.csv",
        repo_type="dataset"
    )

    activity_name_lookup = (
        pd.read_csv(activity_name_lookup_filepath, sep=";")
        .set_index('Filename')
        .to_dict(orient="index")
    )

    with Session(engine) as session:
        try:
            impact_indicator_to_id = {
                (
                    indicator.lcia_method,
                    indicator.category,
                    indicator.indicator
                ): indicator.id
                for indicator in session.exec(select(ImpactIndicator)).all()
            }

            intermediate_exchanges = []
            emissions_factor_values = []
            for filename, ecospold_value in tqdm(activities.items()):
                filepath = filename + ".spold"
                activity_key = activity_name_lookup[filepath]

                parent_emissions_factor_id = activity_unique_key_to_id.get(
                    (
                        f"{activity_key['ActivityName']}-"
                        f"{activity_key['ReferenceProduct']}-"
                        f"{activity_key['Location']}"
                    )
                )
                if not parent_emissions_factor_id:
                    continue

                for exchange in ecospold_value["exchanges"]:
                    exchange_emissions_factor_id = activity_unique_key_to_id.get(
                        (
                            f"{exchange['linked_activity']['activity_name']}-"
                            f"{exchange['exchange_name']}-"
                            f"{exchange['linked_activity']['geography']}"
                        )
                    )
                    if not exchange_emissions_factor_id:
                        continue

                    intermediate_exchange = IntermediateExchange(
                        exchange_name=exchange["exchange_name"],
                        amount=float(exchange["amount"]),
                        input_stream=(float(exchange["amount"]) >= 0),
                        unit=exchange["unit"],
                        exchange_emissions_factor_id=exchange_emissions_factor_id,
                        parent_emissions_factor_id=parent_emissions_factor_id,
                    )
                    intermediate_exchanges.append(intermediate_exchange)

                for impact_factor in ecospold_value["impact_factors"]:
                    impact_indicator_id = impact_indicator_to_id.get(
                        (
                            impact_factor["method"],
                            impact_factor["category"],
                            impact_factor["indicator"]
                        )
                    )
                    if not impact_indicator_id:
                        impact_indicator = ImpactIndicator(
                            lcia_method=impact_factor["method"],
                            category=impact_factor["category"],
                            indicator=impact_factor["indicator"],
                            unit=impact_factor["unit"],
                            description=""
                        )

                        session.add(impact_indicator)
                        session.flush()
                        impact_indicator_id = impact_indicator.id
                        impact_indicator_to_id[
                            (
                                impact_factor["method"],
                                impact_factor["category"],
                                impact_factor["indicator"]
                            )
                        ] = impact_indicator_id

                    emissions_factor_value = EmissionsFactorValue(
                        emissions_factor_id=parent_emissions_factor_id,
                        impact_indicator_id=impact_indicator_id,
                        amount=impact_factor["amount"],
                        unit=impact_factor["unit"],
                    )
                    emissions_factor_values.append(emissions_factor_value)

            start_timer = timer()
            session.bulk_save_objects(intermediate_exchanges)
            session.bulk_save_objects(emissions_factor_values)
            session.commit()
            duration = timer() - start_timer
            logger.success("Populated intermediate exchanges")
            logger.info(f"Duration: {duration:.2f}s")
        except Exception as error:
            logger.exception(f"Error populating emissions factor: {error}")
            session.rollback()

emissions_factors_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=EmissionsFactors.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="emissions_factors_v3.csv",
    seeding_function=seed_emissions_factors,
)
