#pylint: disable=cyclic-import
from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.shared.models import (
    WasteDisposalMethodEmission,
    ProductCategory,
)
from databases.utils.data_loader import DataLoader
from utils.logger import logger_instance


logger = logger_instance.get_logger()

#pylint: disable=too-many-locals
def seed_packaging_material_disposal_emissions(engine, dataset: dict):
    normalized_data = []
    for entry in dataset:
        material = entry["packaging_material"]
        for region, methods in entry["regions"].items():
            if not methods:
                continue

            for method, values in methods.items():
                emissions_factor = values.get("ef", 0)
                rate = values.get("rate", 0)
                source = values.get("source")

                normalized_data.append({
                    "packaging_material": material,
                    "country": region,
                    "method": method,
                    "ef": emissions_factor,
                    "rate": rate,
                    "source": source
                })

    with Session(engine) as session:
        packaging_material_emissions_to_populate = []

        for packaging_material_emission in normalized_data:
            if "ef" in packaging_material_emission:
                packaging_material_emissions_to_populate.append(
                    WasteDisposalMethodEmission(
                        co2e_ef=packaging_material_emission["ef"],
                        packaging_material=packaging_material_emission["packaging_material"],
                        waste_disposal_method=packaging_material_emission["method"],
                        country=packaging_material_emission["country"],
                        rate=packaging_material_emission["rate"],
                        source=packaging_material_emission["source"]
                    )
                )


        with Session(engine) as session:
            try:
                session.add_all(packaging_material_emissions_to_populate)
                session.commit()
                logger.success("Populated packaging material emissions")
            except Exception as error:
                logger.exception(f"Error populating packaging material emissions: {error}")
                session.rollback()

packaging_material_disposal_emissions_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=WasteDisposalMethodEmission.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="packaging_disposal_emissions.json",
    seeding_function=seed_packaging_material_disposal_emissions,
    delete_from_table_query_statements=[
        (
            f"DELETE FROM {WasteDisposalMethodEmission.__tablename__} "
            "WHERE product_category_id IS NULL;"
        )
    ],
)

#pylint: disable=too-many-locals
def seed_product_category_disposal_emissions(engine, dataset: dict):
    normalized_data = []
    for entry in dataset:
        product_category = entry["product_category"]
        for region, methods in entry["regions"].items() :
            for method, values in methods.items():
                emissions_factor = 0 if values is None or "EF" not in values else values["EF"]
                rate = 0 if values is None or "Rate" not in values else values["Rate"]
                source = "" if values is None or "Source" not in values else values["Source"]

                normalized_data.append({
                    "product_category": product_category,
                    "country": region,
                    "method": method,
                    "ef": emissions_factor,
                    "rate": rate,
                    "source": source
                })

    with Session(engine) as session:
        product_categories = session.exec(select(ProductCategory)).all()
        product_category_emissions_to_populate = []

        for product_category_emission in normalized_data:
            product_category_id = next(
                (
                    pm.id
                    for pm in product_categories
                    if pm.name == product_category_emission["product_category"]
                ),
                None
            )

            if (
                "ef" in product_category_emission
                and product_category_id
            ):
                product_category_emissions_to_populate.append(
                    WasteDisposalMethodEmission(
                        co2e_ef=product_category_emission["ef"],
                        product_category_id=product_category_id,
                        waste_disposal_method=product_category_emission["method"],
                        country=product_category_emission["country"],
                        rate=product_category_emission["rate"],
                        source=product_category_emission["source"]
                    )
                )


        with Session(engine) as session:
            try:
                session.add_all(product_category_emissions_to_populate)
                session.commit()
                logger.success("Populated product category emissions")
            except Exception as error:
                logger.exception(f"Error populating product category emissions: {error}")
                session.rollback()

product_category_disposal_emissions_data_loader = DataLoader(
    engine=engine_pool.get_shared_engine(),
    table_name=WasteDisposalMethodEmission.__tablename__,
    repo_id="CarbonBright/database-seed-data",
    filename="product_category_disposal_emissions_v2.json",
    seeding_function=seed_product_category_disposal_emissions,
    delete_from_table_query_statements=[
        (
            f"DELETE FROM {WasteDisposalMethodEmission.__tablename__} "
            "WHERE product_category_id IS NOT NULL;"
        )
    ],
)
