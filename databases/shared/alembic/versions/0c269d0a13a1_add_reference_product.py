"""add reference product

Revision ID: 0c269d0a13a1
Revises: 52fc96c72581
Create Date: 2024-04-15 22:38:22.988859

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '0c269d0a13a1'
down_revision: Union[str, None] = '52fc96c72581'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('temp_emissions_factors',
        sa.Column('activity_name', sa.String(length=120), nullable=False),
        sa.Column('geography', sa.String(length=50), nullable=False),
        sa.Column('source', sa.String(length=120), nullable=False),
        sa.Column('activity_type', sa.String(length=50), nullable=False),
        sa.Column('reference_product', sa.String(length=120), nullable=False),
        sa.Column('reference_product_amount', sa.Integer(), nullable=False),
        sa.Column('kg_co2e', sa.Float(), nullable=False),
        sa.Column('unit', sa.String(length=20), nullable=False),
        sa.Column('activity_description', sa.String(length=1000), nullable=False),
        sa.PrimaryKeyConstraint('activity_name', 'geography', 'source', 'activity_type', 'reference_product')
    )

    op.execute("""
        INSERT INTO temp_emissions_factors (activity_name, geography, source, activity_type, reference_product, reference_product_amount, kg_co2e, unit, activity_description)
        SELECT activity_name, geography, source, activity_type, 'default_product', 1, kg_co2e, unit, activity_description FROM emissions_factors
    """)

    op.rename_table('emissions_factors', 'old_emissions_factors')
    op.rename_table('temp_emissions_factors', 'emissions_factors')
    op.drop_table('old_emissions_factors')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('temp_emissions_factors',
        sa.Column('activity_name', sa.String(length=255), nullable=False),
        sa.Column('geography', sa.String(length=50), nullable=False),
        sa.Column('source', sa.String(length=255), nullable=False),
        sa.Column('activity_type', sa.String(length=50), nullable=False),
        sa.Column('kg_co2e', sa.Float(), nullable=False),
        sa.Column('unit', sa.String(length=20), nullable=False),
        sa.Column('activity_description', sa.String(length=1000), nullable=False),
        sa.PrimaryKeyConstraint('activity_name', 'geography', 'source', 'activity_type')
    )

    op.execute("""
        INSERT INTO temp_emissions_factors (activity_name, geography, source, activity_type, kg_co2e, unit, activity_description)
        SELECT activity_name, geography, source, activity_type, kg_co2e, unit, activity_description FROM emissions_factors
    """)

    op.drop_table('emissions_factors')
    op.rename_table('temp_emissions_factors', 'emissions_factors')


    # ### end Alembic commands ###
