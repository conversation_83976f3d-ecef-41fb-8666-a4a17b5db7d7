"""add ie table

Revision ID: a7d58e17f817
Revises: a50c9a2a86f2
Create Date: 2024-09-25 23:34:59.590603

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a7d58e17f817'
down_revision: Union[str, None] = 'a50c9a2a86f2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('intermediate_exchange',
    sa.Column('exchange_emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('parent_emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('exchange_name', sqlmodel.sql.sqltypes.AutoString(length=120), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('input_stream', sa.Boolean(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.ForeignKeyConstraint(['exchange_emissions_factor_id'], ['emissions_factors.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['parent_emissions_factor_id'], ['emissions_factors.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('intermediate_exchange')
    # ### end Alembic commands ###
