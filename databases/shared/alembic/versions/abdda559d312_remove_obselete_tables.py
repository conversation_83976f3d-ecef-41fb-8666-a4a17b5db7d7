"""remove obselete tables

Revision ID: abdda559d312
Revises: 0e8707b0a26f
Create Date: 2025-02-01 21:58:41.154962

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'abdda559d312'
down_revision: Union[str, None] = '0e8707b0a26f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('code', table_name='country')
    op.drop_table('chem_ingredients')
    op.drop_table('emission_factor')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('emission_factor',
    sa.Column('country', mysql.VARCHAR(length=10), nullable=False),
    sa.Column('resource', mysql.VARCHAR(length=300), nullable=False),
    sa.Column('emission_factor', mysql.FLOAT(), nullable=False),
    sa.Column('unit', mysql.VARCHAR(length=20), nullable=False),
    sa.Column('source', mysql.VARCHAR(length=255), nullable=False),
    sa.PrimaryKeyConstraint('country', 'resource'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('chem_ingredients',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('synonyms', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('cas', mysql.VARCHAR(length=20), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
