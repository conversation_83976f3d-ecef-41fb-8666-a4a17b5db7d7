"""Initial revision

Revision ID: 5b652821fcff
Revises: 
Create Date: 2024-02-09 01:10:20.013801

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '5b652821fcff'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chem_ingredients',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('synonyms', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('cas', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('country',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('code', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('number', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('country_capital',
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('city', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('latitude', sa.Float(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('country'),
    sa.UniqueConstraint('country')
    )
    op.create_table('domestic_production_data',
    sa.Column('hsc', sa.Integer(), nullable=False),
    sa.Column('year', sa.Integer(), nullable=False),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('production_value_pound', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('hsc', 'year', 'country', name='domestic_production_data_composite_primary_key')
    )
    op.create_table('emission_factor',
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('resource', sqlmodel.sql.sqltypes.AutoString(length=300), nullable=False),
    sa.Column('emission_factor', sa.Float(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('country', 'resource')
    )
    op.create_table('packaging_material',
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('category_level', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['packaging_material.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('product_category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('raw_material',
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('is_packaging', sa.Boolean(), nullable=False),
    sa.Column('is_textile', sa.Boolean(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('cas_number', sqlmodel.sql.sqltypes.AutoString(length=12), nullable=True),
    sa.Column('emission_factor_id', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('name')
    )
    op.create_table('sea_port',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('city', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('latitude', sa.Float(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('waste_disposal_method',
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True),
    sa.Column('category_level', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['waste_disposal_method.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('wits_product_data',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('hsc', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wits_product_data_hsc'), 'wits_product_data', ['hsc'], unique=False)
    op.create_table('consumer_use',
    sa.Column('product_category_id', sa.Integer(), nullable=False),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('resource', sqlmodel.sql.sqltypes.AutoString(length=300), nullable=False),
    sa.Column('consumer_use', sqlmodel.sql.sqltypes.AutoString(length=300), nullable=False),
    sa.Column('stream', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.ForeignKeyConstraint(['product_category_id'], ['product_category.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_category_id', 'country', 'resource', 'consumer_use')
    )
    op.create_index(op.f('ix_consumer_use_product_category_id'), 'consumer_use', ['product_category_id'], unique=False)
    op.create_table('packaging_material_disposal_method_emission',
    sa.Column('packaging_material_id', sa.Integer(), nullable=True),
    sa.Column('waste_disposal_method_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('co2e_ef', sa.Float(), nullable=False),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('rate', sa.Float(), nullable=False),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.ForeignKeyConstraint(['packaging_material_id'], ['packaging_material.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['waste_disposal_method_id'], ['waste_disposal_method.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('wits_trade_data',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('reporter_id', sa.Integer(), nullable=True),
    sa.Column('partner_id', sa.Integer(), nullable=True),
    sa.Column('reporter', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('partner', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('hsc', sa.Integer(), nullable=False),
    sa.Column('trade_flow', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('trade_value', sa.Float(), nullable=False),
    sa.Column('qty', sa.Float(), nullable=True),
    sa.Column('qty_unit', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=True),
    sa.Column('year', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['hsc'], ['wits_product_data.hsc'], ),
    sa.ForeignKeyConstraint(['partner_id'], ['country.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['reporter_id'], ['country.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('wits_trade_data')
    op.drop_table('packaging_material_disposal_method_emission')
    op.drop_index(op.f('ix_consumer_use_product_category_id'), table_name='consumer_use')
    op.drop_table('consumer_use')
    op.drop_index(op.f('ix_wits_product_data_hsc'), table_name='wits_product_data')
    op.drop_table('wits_product_data')
    op.drop_table('waste_disposal_method')
    op.drop_table('sea_port')
    op.drop_table('raw_material')
    op.drop_table('product_category')
    op.drop_table('packaging_material')
    op.drop_table('emission_factor')
    op.drop_table('domestic_production_data')
    op.drop_table('country_capital')
    op.drop_table('country')
    op.drop_table('chem_ingredients')
    # ### end Alembic commands ###
