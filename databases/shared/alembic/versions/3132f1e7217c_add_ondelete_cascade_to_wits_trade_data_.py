"""add ondelete cascade to wits_trade_data and package_prediction

Revision ID: 3132f1e7217c
Revises: 843f53467026
Create Date: 2024-05-14 11:51:32.974945

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '3132f1e7217c'
down_revision: Union[str, None] = '843f53467026'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('packaging_prediction_ibfk_1', 'packaging_prediction', type_='foreignkey')
    op.create_foreign_key(None, 'packaging_prediction', 'product_category', ['product_category_id'], ['id'], ondelete='CASCADE')
    op.drop_constraint('wits_trade_data_ibfk_1', 'wits_trade_data', type_='foreignkey')
    op.create_foreign_key(None, 'wits_trade_data', 'wits_product_data', ['hsc'], ['hsc'], ondelete='CASCADE')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'wits_trade_data', type_='foreignkey')
    op.create_foreign_key('wits_trade_data_ibfk_1', 'wits_trade_data', 'wits_product_data', ['hsc'], ['hsc'])
    op.drop_constraint(None, 'packaging_prediction', type_='foreignkey')
    op.create_foreign_key('packaging_prediction_ibfk_1', 'packaging_prediction', 'product_category', ['product_category_id'], ['id'])
    # ### end Alembic commands ###
