"""create emissions factors and activity match table

Revision ID: 7ec160938bfd
Revises: cfbd9a92013c
Create Date: 2024-04-02 16:34:33.583008

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '7ec160938bfd'
down_revision: Union[str, None] = 'cfbd9a92013c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('emissions_factors',
    sa.Column('activity_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('geography', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('activity_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('kg_co2e', sa.Float(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('activity_description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=False),
    sa.PrimaryKeyConstraint('activity_name', 'geography', 'source', 'activity_type')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('emissions_factors')
    # ### end Alembic commands ###
