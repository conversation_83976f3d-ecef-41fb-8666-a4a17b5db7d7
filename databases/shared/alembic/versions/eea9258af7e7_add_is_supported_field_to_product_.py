"""add is_supported field to product_category

Revision ID: eea9258af7e7
Revises: 03f66e0aac7a
Create Date: 2024-05-23 13:33:30.068926

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'eea9258af7e7'
down_revision: Union[str, None] = '03f66e0aac7a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_category', sa.Column('is_supported', sa.<PERSON>(), nullable=False))
    op.create_index(op.f('ix_product_category_is_supported'), 'product_category', ['is_supported'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_product_category_is_supported'), table_name='product_category')
    op.drop_column('product_category', 'is_supported')
    # ### end Alembic commands ###
