"""add node type table

Revision ID: e94294a884b8
Revises: a7d58e17f817
Create Date: 2024-10-17 18:25:31.970216

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'e94294a884b8'
down_revision: Union[str, None] = 'a7d58e17f817'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('node_type',
    sa.Column('name', sa.Enum('MATERIAL', 'TRANSPORTATION', 'PRODUCTION', 'USE', 'EOL', name='nodetypes'), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('min_inputs', sa.Integer(), nullable=False),
    sa.Column('max_inputs', sa.Integer(), nullable=True),
    sa.Column('min_outputs', sa.Integer(), nullable=False),
    sa.Column('max_outputs', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('node_type')
    # ### end Alembic commands ###
