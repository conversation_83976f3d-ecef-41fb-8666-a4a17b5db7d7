"""change column production_value_pound to production_value_usd

Revision ID: 7ab636e83651
Revises: 06a22843d72c
Create Date: 2024-04-16 22:19:59.572106

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '7ab636e83651'
down_revision: Union[str, None] = '06a22843d72c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('domestic_production_data', sa.Column('production_value_usd', sa.Float(), nullable=False))
    op.drop_column('domestic_production_data', 'production_value_pound')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('domestic_production_data', sa.Column('production_value_pound', mysql.FLOAT(), nullable=False))
    op.drop_column('domestic_production_data', 'production_value_usd')
    # ### end Alembic commands ###
