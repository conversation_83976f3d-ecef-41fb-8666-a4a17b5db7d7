"""add fulltext index for emissions_factors.activity_name

Revision ID: 343b8cb96a96
Revises: 7684f8feaeb7
Create Date: 2025-04-18 17:07:55.546053

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '343b8cb96a96'
down_revision: Union[str, None] = '7684f8feaeb7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE emissions_factors ADD FULLTEXT INDEX `fulltext`(activity_name);")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    raise ValueError("Downgrade not supported for this migration.")
    # ### end Alembic commands ###
