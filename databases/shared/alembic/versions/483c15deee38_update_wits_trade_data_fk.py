"""update_wits_trade_data_fk

Revision ID: 483c15deee38
Revises: eea9258af7e7
Create Date: 2024-06-03 15:56:12.880680

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '483c15deee38'
down_revision: Union[str, None] = 'eea9258af7e7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('country', 'code',
               existing_type=mysql.VARCHAR(length=10),
               type_=sqlmodel.sql.sqltypes.AutoString(length=3),
               existing_nullable=False)
    op.create_unique_constraint(None, 'country', ['code'])
    op.alter_column('wits_trade_data', 'reporter',
               existing_type=mysql.VARCHAR(length=3),
               nullable=True)
    op.alter_column('wits_trade_data', 'partner',
               existing_type=mysql.VARCHAR(length=3),
               nullable=True)
    op.drop_constraint('wits_trade_data_ibfk_3', 'wits_trade_data', type_='foreignkey')
    op.drop_constraint('wits_trade_data_ibfk_2', 'wits_trade_data', type_='foreignkey')
    op.create_foreign_key(None, 'wits_trade_data', 'country', ['partner'], ['code'], ondelete='CASCADE')
    op.create_foreign_key(None, 'wits_trade_data', 'country', ['reporter'], ['code'], ondelete='CASCADE')
    op.drop_column('wits_trade_data', 'reporter_id')
    op.drop_column('wits_trade_data', 'partner_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('wits_trade_data', sa.Column('partner_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('wits_trade_data', sa.Column('reporter_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'wits_trade_data', type_='foreignkey')
    op.drop_constraint(None, 'wits_trade_data', type_='foreignkey')
    op.create_foreign_key('wits_trade_data_ibfk_2', 'wits_trade_data', 'country', ['partner_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('wits_trade_data_ibfk_3', 'wits_trade_data', 'country', ['reporter_id'], ['id'], ondelete='CASCADE')
    op.alter_column('wits_trade_data', 'partner',
               existing_type=mysql.VARCHAR(length=3),
               nullable=False)
    op.alter_column('wits_trade_data', 'reporter',
               existing_type=mysql.VARCHAR(length=3),
               nullable=False)
    op.drop_constraint(None, 'country', type_='unique')
    # ### end Alembic commands ###
