"""added indexes for wits_trade_data

Revision ID: 28635079db8e
Revises: 5b652821fcff
Create Date: 2024-02-19 18:39:34.612365

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '28635079db8e'
down_revision: Union[str, None] = '5b652821fcff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_wits_trade_data_partner'), 'wits_trade_data', ['partner'], unique=False)
    op.create_index(op.f('ix_wits_trade_data_reporter'), 'wits_trade_data', ['reporter'], unique=False)
    op.create_index(op.f('ix_wits_trade_data_trade_flow'), 'wits_trade_data', ['trade_flow'], unique=False)
    op.create_index(op.f('ix_wits_trade_data_year'), 'wits_trade_data', ['year'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_wits_trade_data_year'), table_name='wits_trade_data')
    op.drop_index(op.f('ix_wits_trade_data_trade_flow'), table_name='wits_trade_data')
    op.drop_index(op.f('ix_wits_trade_data_reporter'), table_name='wits_trade_data')
    op.drop_index(op.f('ix_wits_trade_data_partner'), table_name='wits_trade_data')
    # ### end Alembic commands ###
