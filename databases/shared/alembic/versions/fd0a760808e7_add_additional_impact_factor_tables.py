"""add additional impact factor tables

Revision ID: fd0a760808e7
Revises: f455fae9dd58
Create Date: 2024-12-10 11:15:51.489173

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'fd0a760808e7'
down_revision: Union[str, None] = 'f455fae9dd58'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('impact_category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('indicator', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', 'indicator', name='uq_impact_category')
    )
    op.create_table('lcia_method',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', name='uq_lcia_method_name')
    )
    op.create_table('emissions_factor_value',
    sa.Column('emissions_factor_id', sa.Integer(), nullable=False),
    sa.Column('impact_category_id', sa.Integer(), nullable=False),
    sa.Column('lcia_method_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['emissions_factor_id'], ['emissions_factors.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['impact_category_id'], ['impact_category.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['lcia_method_id'], ['lcia_method.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('emissions_factor_id', 'impact_category_id', 'lcia_method_id')
    )
    op.drop_column('emissions_factors', 'kg_co2e')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('emissions_factors', sa.Column('kg_co2e', mysql.FLOAT(), nullable=False))
    op.drop_table('emissions_factor_value')
    op.drop_table('lcia_method')
    op.drop_table('impact_category')
    # ### end Alembic commands ###
