"""added packaging_prediction table

Revision ID: 06a22843d72c
Revises: 52fc96c72581
Create Date: 2024-04-09 18:15:46.331850

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '06a22843d72c'
down_revision: Union[str, None] = '0c269d0a13a1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('packaging_prediction',
    sa.Column('product_category_id', sa.Integer(), nullable=False),
    sa.Column('packaging_level', sqlmodel.sql.sqltypes.AutoString(length=15), nullable=False),
    sa.Column('packaging_material', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('packaging_item', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('weight_ratio_percentage', sa.Float(), nullable=False),
    sa.Column('recyclable', sa.Boolean(), nullable=False),
    sa.Column('number_of_packages', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['product_category_id'], ['product_category.id'], ),
    sa.PrimaryKeyConstraint('product_category_id', 'packaging_level', 'packaging_material', 'packaging_item', name='package_prediction_composite_primary_key')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('packaging_prediction')
    # ### end Alembic commands ###
