"""added area_km2 to country

Revision ID: c63544a5a4c5
Revises: d0780c64114c
Create Date: 2024-07-11 22:01:46.634100

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c63544a5a4c5'
down_revision: Union[str, None] = 'd0780c64114c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('area_km2', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('country', 'area_km2')
    # ### end Alembic commands ###
