"""add_product_category_id_to_waste_disposal_method_emissions

Revision ID: cfbd9a92013c
Revises: 28635079db8e
Create Date: 2024-03-13 21:56:48.769172

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'cfbd9a92013c'
down_revision: Union[str, None] = '28635079db8e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('waste_disposal_method_emission',
    sa.Column('packaging_material_id', sa.Integer(), nullable=True),
    sa.Column('product_category_id', sa.Integer(), nullable=True),
    sa.Column('waste_disposal_method_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('co2e_ef', sa.Float(), nullable=False),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('rate', sa.Float(), nullable=False),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.ForeignKeyConstraint(['packaging_material_id'], ['packaging_material.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_category_id'], ['product_category.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['waste_disposal_method_id'], ['waste_disposal_method.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('packaging_material_disposal_method_emission')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('packaging_material_disposal_method_emission',
    sa.Column('packaging_material_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('waste_disposal_method_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('co2e_ef', mysql.FLOAT(), nullable=False),
    sa.Column('country', mysql.VARCHAR(length=10), nullable=False),
    sa.Column('rate', mysql.FLOAT(), nullable=False),
    sa.Column('source', mysql.VARCHAR(length=500), nullable=True),
    sa.ForeignKeyConstraint(['packaging_material_id'], ['packaging_material.id'], name='packaging_material_disposal_method_emission_ibfk_1', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['waste_disposal_method_id'], ['waste_disposal_method.id'], name='packaging_material_disposal_method_emission_ibfk_2', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.drop_table('waste_disposal_method_emission')
    # ### end Alembic commands ###
