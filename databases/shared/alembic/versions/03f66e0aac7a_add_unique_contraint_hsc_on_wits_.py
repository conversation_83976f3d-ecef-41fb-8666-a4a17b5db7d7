"""add unique contraint hsc on wits_product_data

Revision ID: 03f66e0aac7a
Revises: 3132f1e7217c
Create Date: 2024-05-14 18:30:04.046482

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '03f66e0aac7a'
down_revision: Union[str, None] = '3132f1e7217c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'wits_product_data', ['hsc'])
    op.drop_index('ix_wits_product_data_hsc', table_name='wits_product_data')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_wits_product_data_hsc', 'wits_product_data', ['hsc'], unique=False)
    op.drop_constraint(None, 'wits_product_data', type_='unique')
    # ### end Alembic commands ###
