"""remove country_capital table

Revision ID: 82de378a54d4
Revises: c63544a5a4c5
Create Date: 2024-07-15 22:39:42.112914

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '82de378a54d4'
down_revision: Union[str, None] = 'c63544a5a4c5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('country', table_name='country_capital')
    op.drop_table('country_capital')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('country_capital',
    sa.Column('country', mysql.VARCHAR(length=50), nullable=False),
    sa.Column('city', mysql.VARCHAR(length=50), nullable=False),
    sa.Column('latitude', mysql.FLOAT(), nullable=False),
    sa.Column('longitude', mysql.FLOAT(), nullable=False),
    sa.PrimaryKeyConstraint('country'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('country', 'country_capital', ['country'], unique=False)
    # ### end Alembic commands ###
