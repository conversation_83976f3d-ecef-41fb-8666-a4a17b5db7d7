"""cleanup waste disposal emissions

Revision ID: fbe374b307ff
Revises: 82de378a54d4
Create Date: 2024-07-19 11:40:15.801563

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'fbe374b307ff'
down_revision: Union[str, None] = '82de378a54d4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('waste_disposal_method_emission', sa.Column('packaging_material', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    op.add_column('waste_disposal_method_emission', sa.Column('waste_disposal_method', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.create_index(op.f('ix_waste_disposal_method_emission_packaging_material'), 'waste_disposal_method_emission', ['packaging_material'], unique=False)
    op.create_index(op.f('ix_waste_disposal_method_emission_waste_disposal_method'), 'waste_disposal_method_emission', ['waste_disposal_method'], unique=False)
    op.drop_constraint('waste_disposal_method_emission_ibfk_1', 'waste_disposal_method_emission', type_='foreignkey')
    op.drop_constraint('waste_disposal_method_emission_ibfk_3', 'waste_disposal_method_emission', type_='foreignkey')
    op.drop_column('waste_disposal_method_emission', 'waste_disposal_method_id')
    op.drop_column('waste_disposal_method_emission', 'packaging_material_id')
    op.drop_index('name', table_name='waste_disposal_method')
    op.drop_table('waste_disposal_method')
    op.drop_index('name', table_name='packaging_material')
    op.drop_table('packaging_material')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('packaging_material',
    sa.Column('parent_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('category_level', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['packaging_material.id'], name='packaging_material_ibfk_1', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('name', 'packaging_material', ['name'], unique=False)
    op.create_table('waste_disposal_method',
    sa.Column('parent_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('category_level', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['waste_disposal_method.id'], name='waste_disposal_method_ibfk_1', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('name', 'waste_disposal_method', ['name'], unique=False)

    op.add_column('waste_disposal_method_emission', sa.Column('packaging_material_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('waste_disposal_method_emission', sa.Column('waste_disposal_method_id', mysql.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('waste_disposal_method_emission_ibfk_3', 'waste_disposal_method_emission', 'waste_disposal_method', ['waste_disposal_method_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('waste_disposal_method_emission_ibfk_1', 'waste_disposal_method_emission', 'packaging_material', ['packaging_material_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_waste_disposal_method_emission_waste_disposal_method'), table_name='waste_disposal_method_emission')
    op.drop_index(op.f('ix_waste_disposal_method_emission_packaging_material'), table_name='waste_disposal_method_emission')
    op.drop_column('waste_disposal_method_emission', 'waste_disposal_method')
    op.drop_column('waste_disposal_method_emission', 'packaging_material')
    op.create_table('packaging_material',
    sa.Column('parent_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('category_level', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['parent_id'], ['packaging_material.id'], name='packaging_material_ibfk_1', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
