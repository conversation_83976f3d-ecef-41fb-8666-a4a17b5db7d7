"""add manufacturing process table

Revision ID: d5cf698eaf45
Revises: 483c15deee38
Create Date: 2024-06-06 13:17:44.153641

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'd5cf698eaf45'
down_revision: Union[str, None] = '483c15deee38'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('manufacturing_process',
    sa.Column('process_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('geography_iso3', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=False),
    sa.Column('electricity_kwh', sa.Float(), nullable=False),
    sa.Column('water_liters', sa.Float(), nullable=False),
    sa.Column('co2e_kg', sa.Float(), nullable=True),
    sa.Column('amount_of_product_kg', sa.Float(), nullable=False),
    sa.PrimaryKeyConstraint('process_name', 'geography_iso3')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('manufacturing_process')
    # ### end Alembic commands ###
