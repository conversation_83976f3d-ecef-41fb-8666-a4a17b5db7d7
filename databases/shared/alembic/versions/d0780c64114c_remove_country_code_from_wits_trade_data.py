"""remove country_code from wits_trade_data

Revision ID: d0780c64114c
Revises: d5cf698eaf45
Create Date: 2024-07-02 21:01:43.340581

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = "d0780c64114c"
down_revision: Union[str, None] = "d5cf698eaf45"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.drop_constraint(
            "wits_trade_data_ibfk_5", "wits_trade_data", type_="foreignkey"
        )
    except Exception as e:
        print(f"Error dropping foreign key constraint: {e}")
        pass
    try:
        op.drop_constraint(
            "wits_trade_data_ibfk_6", "wits_trade_data", type_="foreignkey"
        )
    except Exception as e:
        print(f"Error dropping foreign key constraint: {e}")
        pass
    try:
        op.drop_index("ix_wits_trade_data_partner", table_name="wits_trade_data")
    except Exception as e:
        print(f"Error dropping index: {e}")
        pass
    try:
        op.drop_index("ix_wits_trade_data_reporter", table_name="wits_trade_data")
    except Exception as e:
        print(f"Error dropping index: {e}")
        pass
    try:
        op.add_column(
            "wits_trade_data", sa.Column("reporter_id", sa.Integer(), nullable=False)
        )
    except Exception as e:
        print(f"Error adding column: {e}")
        pass
    try:
        op.add_column(
            "wits_trade_data", sa.Column("partner_id", sa.Integer(), nullable=False)
        )
    except Exception as e:
        print(f"Error adding column: {e}")
        pass
    try:
        op.create_index(
            op.f("ix_wits_trade_data_partner_id"),
            "wits_trade_data",
            ["partner_id"],
            unique=False,
        )
    except Exception as e:
        print(f"Error creating index: {e}")
        pass
    try:
        op.create_index(
            op.f("ix_wits_trade_data_reporter_id"),
            "wits_trade_data",
            ["reporter_id"],
            unique=False,
        )
    except Exception as e:
        print(f"Error creating index: {e}")
        pass
    try:
        op.drop_column("wits_trade_data", "reporter")
    except Exception as e:
        print(f"Error dropping column: {e}")
        pass
    try:
        op.drop_column("wits_trade_data", "partner")
    except Exception as e:
        print(f"Error dropping column: {e}")
        pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "wits_trade_data", sa.Column("partner", mysql.VARCHAR(length=3), nullable=True)
    )
    op.add_column(
        "wits_trade_data", sa.Column("reporter", mysql.VARCHAR(length=3), nullable=True)
    )
    op.create_foreign_key(
        "wits_trade_data_ibfk_6",
        "wits_trade_data",
        "country",
        ["reporter"],
        ["code"],
        ondelete="CASCADE",
    )
    op.create_foreign_key(
        "wits_trade_data_ibfk_5",
        "wits_trade_data",
        "country",
        ["partner"],
        ["code"],
        ondelete="CASCADE",
    )
    op.drop_index(op.f("ix_wits_trade_data_reporter_id"), table_name="wits_trade_data")
    op.drop_index(op.f("ix_wits_trade_data_partner_id"), table_name="wits_trade_data")
    op.create_index(
        "ix_wits_trade_data_reporter", "wits_trade_data", ["reporter"], unique=False
    )
    op.create_index(
        "ix_wits_trade_data_partner", "wits_trade_data", ["partner"], unique=False
    )
    op.drop_column("wits_trade_data", "partner_id")
    op.drop_column("wits_trade_data", "reporter_id")
    # ### end Alembic commands ###
