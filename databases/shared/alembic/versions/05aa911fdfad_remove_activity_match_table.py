"""remove activity match table

Revision ID: 05aa911fdfad
Revises: fbe374b307ff
Create Date: 2024-09-16 16:57:47.099592

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '05aa911fdfad'
down_revision: Union[str, None] = 'fbe374b307ff'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_activity_match_matched_activity_name', table_name='activity_match')
    op.drop_index('ix_activity_match_matched_activity_source', table_name='activity_match')
    op.drop_table('activity_match')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_match',
    sa.Column('reference_product', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('activity_type', mysql.VARCHAR(length=50), nullable=False),
    sa.Column('matched_activity_name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('matched_activity_source', mysql.VARCHAR(length=255), nullable=False),
    sa.PrimaryKeyConstraint('reference_product', 'activity_type'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_activity_match_matched_activity_source', 'activity_match', ['matched_activity_source'], unique=False)
    op.create_index('ix_activity_match_matched_activity_name', 'activity_match', ['matched_activity_name'], unique=False)
    # ### end Alembic commands ###
