"""add impact indicator table

Revision ID: 0e8707b0a26f
Revises: fd0a760808e7
Create Date: 2025-01-13 11:34:04.089911

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlmodel import Session
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect
from databases.engine import engine_pool

# revision identifiers, used by Alembic.
revision: str = '0e8707b0a26f'
down_revision: Union[str, None] = 'fd0a760808e7'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create impact_indicator table first
    op.create_table('impact_indicator',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('lcia_method', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column('category', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column('indicator', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('lcia_method', 'category', 'indicator', name='uq_impact_indicator')
    )

    # Create temporary table with new structure
    op.create_table('emissions_factor_value_new',
        sa.Column('emissions_factor_id', sa.Integer(), nullable=False),
        sa.Column('impact_indicator_id', sa.Integer(), nullable=False),
        sa.Column('amount', sa.Float(), nullable=False),
        sa.PrimaryKeyConstraint('emissions_factor_id', 'impact_indicator_id'),
        sa.ForeignKeyConstraint(
            ['impact_indicator_id'], ['impact_indicator.id'],
            name='fk_efv_impact_indicator',
            ondelete='CASCADE',
            onupdate='CASCADE'
        ),
        sa.ForeignKeyConstraint(
            ['emissions_factor_id'], ['emissions_factors.id'],
            name='fk_efv_emissions_factor',
            ondelete='CASCADE',
            onupdate='CASCADE'
        )
    )

    # Copy data from old table to new table (with appropriate joins to map old to new structure)
    op.execute("""
        INSERT INTO emissions_factor_value_new (
            emissions_factor_id,
            impact_indicator_id,
            amount
        )
        SELECT 
            efv.emissions_factor_id,
            ii.id as impact_indicator_id,
            efv.amount
        FROM emissions_factor_value efv
        JOIN lcia_method lm ON efv.lcia_method_id = lm.id
        JOIN impact_category ic ON efv.impact_category_id = ic.id
        JOIN impact_indicator ii ON 
            ii.lcia_method = lm.name AND 
            ii.category = ic.name AND 
            ii.indicator = ic.indicator
    """)

    # Drop old table and rename new table
    op.drop_table('emissions_factor_value')
    op.execute('ALTER TABLE emissions_factor_value_new RENAME TO emissions_factor_value')

    # Drop old tables that are no longer needed
    op.drop_table('impact_category')
    op.drop_table('lcia_method')

    engine = engine_pool.get_admin_engine()
    with Session(engine) as session:
        session.execute('DELETE FROM huggingface_versions WHERE repo_id = "CarbonBright/database-seed-data" AND filename = "emissions_factors_v3.csv"')
        session.commit()


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('emissions_factor_value', sa.Column('lcia_method_id', mysql.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('emissions_factor_value', sa.Column('impact_category_id', mysql.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'emissions_factor_value', type_='foreignkey')
    op.create_foreign_key('emissions_factor_value_ibfk_3', 'emissions_factor_value', 'lcia_method', ['lcia_method_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('emissions_factor_value_ibfk_2', 'emissions_factor_value', 'impact_category', ['impact_category_id'], ['id'], ondelete='CASCADE')
    op.drop_column('emissions_factor_value', 'impact_indicator_id')
    op.create_table('lcia_method',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_lcia_method_name', 'lcia_method', ['name'], unique=False)
    op.create_table('impact_category',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('indicator', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('unit', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_impact_category', 'impact_category', ['name', 'indicator'], unique=False)
    op.drop_table('impact_indicator')
    # ### end Alembic commands ###
