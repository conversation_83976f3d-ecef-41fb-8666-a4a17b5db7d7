"""update ef table index and primary keys

Revision ID: a50c9a2a86f2
Revises: 05aa911fdfad
Create Date: 2024-09-25 17:58:14.972918

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a50c9a2a86f2'
down_revision: Union[str, None] = '05aa911fdfad'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""
        CREATE TABLE emissions_factors_temp AS 
        SELECT * FROM emissions_factors;
    """)

    op.drop_table('emissions_factors')

    op.create_table(
        'emissions_factors',
        sa.Column('id', sa.Integer(), primary_key=True, nullable=False, autoincrement=True),
        sa.Column('activity_name', sa.String(length=120), nullable=False),
        sa.Column('activity_type', sa.String(length=120), nullable=False),
        sa.Column('geography', sa.String(length=120), nullable=False),
        sa.Column('reference_product', sa.String(length=120), nullable=False),
        sa.Column('source', sa.String(length=120), nullable=False),
        sa.Column('raw_material', sa.String(length=120), nullable=True),
        sa.Column('reference_product_amount', sa.Float(), nullable=False),
        sa.Column('kg_co2e', sa.Float(), nullable=False),
        sa.Column('unit', sa.String(length=64), nullable=False),
        sa.Column('activity_description', sa.Text(), nullable=True),
        sa.UniqueConstraint('activity_name', 'geography', 'reference_product', 'source', name='uq_emissions_factors_composite')
    )
    op.create_index(op.f('ix_emissions_factors_raw_material'), 'emissions_factors', ['raw_material'], unique=False)

    op.execute("""
        INSERT INTO emissions_factors (activity_name, activity_type, geography, reference_product, source, 
            raw_material, reference_product_amount, kg_co2e, unit, activity_description)
        SELECT activity_name, activity_type, geography, reference_product, source, 
            raw_material, reference_product_amount, kg_co2e, unit, activity_description
        FROM emissions_factors_temp;
    """)

    op.execute("DROP TABLE emissions_factors_temp;")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('emissions_factors')

    op.create_table(
        'emissions_factors',
        sa.Column('activity_name', sa.String(length=120), nullable=False),
        sa.Column('activity_type', sa.String(length=50), nullable=False),
        sa.Column('geography', sa.String(length=50), nullable=False),
        sa.Column('reference_product', sa.String(length=120), nullable=False),
        sa.Column('source', sa.String(length=120), nullable=False),
        sa.Column('raw_material', sa.String(length=255), nullable=True),
        sa.Column('reference_product_amount', sa.Float(), nullable=False),
        sa.Column('kg_co2e', sa.Float(), nullable=False),
        sa.Column('unit', sa.String(length=20), nullable=False),
        sa.Column('activity_description', sa.String(length=1000), nullable=True),
        sa.PrimaryKeyConstraint('activity_name', 'activity_type', 'geography', 'reference_product', 'source'),
    )
    op.create_index(op.f('ix_emissions_factors_raw_material'), 'emissions_factors', ['raw_material'], unique=False)
    # ### end Alembic commands ###
