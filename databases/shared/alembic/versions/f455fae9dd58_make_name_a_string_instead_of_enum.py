"""make name a string instead of enum

Revision ID: f455fae9dd58
Revises: e94294a884b8
Create Date: 2024-10-31 19:38:08.245604

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'f455fae9dd58'
down_revision: Union[str, None] = 'e94294a884b8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('node_type', 'name',
               existing_type=mysql.ENUM('MATERIAL', 'TRANSPORTATION', 'PRODUCTION', 'USE', 'EOL'),
               type_=sqlmodel.sql.sqltypes.AutoString(),
               existing_nullable=False)
    op.create_unique_constraint(None, 'node_type', ['name'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'node_type', type_='unique')
    op.alter_column('node_type', 'name',
               existing_type=sqlmodel.sql.sqltypes.AutoString(),
               type_=mysql.ENUM('MATERIAL', 'TRANSPORTATION', 'PRODUCTION', 'USE', 'EOL'),
               existing_nullable=False)
    # ### end Alembic commands ###
