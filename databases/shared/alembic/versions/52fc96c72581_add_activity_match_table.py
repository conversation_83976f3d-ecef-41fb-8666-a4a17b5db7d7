"""add activity match table

Revision ID: 52fc96c72581
Revises: 7ec160938bfd
Create Date: 2024-04-02 19:26:11.704987

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '52fc96c72581'
down_revision: Union[str, None] = '7ec160938bfd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('activity_match',
    sa.Column('reference_product', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('activity_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('matched_activity_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('matched_activity_source', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('reference_product', 'activity_type')
    )
    op.create_index(op.f('ix_activity_match_matched_activity_name'), 'activity_match', ['matched_activity_name'], unique=False)
    op.create_index(op.f('ix_activity_match_matched_activity_source'), 'activity_match', ['matched_activity_source'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_activity_match_matched_activity_source'), table_name='activity_match')
    op.drop_index(op.f('ix_activity_match_matched_activity_name'), table_name='activity_match')
    op.drop_table('activity_match')
    # ### end Alembic commands ###
