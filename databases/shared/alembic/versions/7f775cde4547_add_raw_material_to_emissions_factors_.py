"""add raw_material to emissions_factors table

Revision ID: 7f775cde4547
Revises: 7ab636e83651
Create Date: 2024-05-03 18:21:09.304541

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '7f775cde4547'
down_revision: Union[str, None] = '7ab636e83651'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('emissions_factors', sa.Column('raw_material', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.create_index(op.f('ix_emissions_factors_raw_material'), 'emissions_factors', ['raw_material'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_emissions_factors_raw_material'), table_name='emissions_factors')
    op.drop_column('emissions_factors', 'raw_material')
    # ### end Alembic commands ###
