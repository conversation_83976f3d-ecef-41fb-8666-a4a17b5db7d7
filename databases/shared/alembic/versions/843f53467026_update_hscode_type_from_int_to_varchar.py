"""update hscode type from int to varchar

Revision ID: 843f53467026
Revises: 7f775cde4547
Create Date: 2024-05-08 17:15:16.187193

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '843f53467026'
down_revision: Union[str, None] = '7f775cde4547'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop foreign key constraint referencing hsc column
    op.drop_constraint('wits_trade_data_ibfk_1', 'wits_trade_data', type_='foreignkey')
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('wits_product_data', 'hsc',
               existing_type=mysql.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=10),
               existing_nullable=False)
    op.alter_column('wits_trade_data', 'hsc',
               existing_type=mysql.INTEGER(),
               type_=sqlmodel.sql.sqltypes.AutoString(length=10),
               existing_nullable=False)
    op.create_foreign_key('wits_trade_data_ibfk_1', 'wits_trade_data', 'wits_product_data', ['hsc'], ['hsc'])
    op.create_index(op.f('ix_wits_trade_data_hsc'), 'wits_trade_data', ['hsc'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('wits_trade_data_ibfk_1', 'wits_trade_data', type_='foreignkey')
    op.drop_index(op.f('ix_wits_trade_data_hsc'), table_name='wits_trade_data')
    op.alter_column('wits_trade_data', 'hsc',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=10),
               type_=mysql.INTEGER(),
               existing_nullable=False)
    op.alter_column('wits_product_data', 'hsc',
               existing_type=sqlmodel.sql.sqltypes.AutoString(length=10),
               type_=mysql.INTEGER(),
               existing_nullable=False)
    op.create_foreign_key('wits_trade_data_ibfk_1', 'wits_trade_data', 'wits_product_data', ['hsc'], ['hsc'])
    # ### end Alembic commands ###
