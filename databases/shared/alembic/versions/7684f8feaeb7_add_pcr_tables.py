"""add pcr tables

Revision ID: 7684f8feaeb7
Revises: abdda559d312
Create Date: 2025-02-05 10:07:26.722535

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '7684f8feaeb7'
down_revision: Union[str, None] = 'abdda559d312'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('pcr',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Primary<PERSON>eyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('pcrrule',
    sa.Column('pcr_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('segment', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('condition', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('sequence_number', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['pcr_id'], ['pcr.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('pcrrule')
    op.drop_table('pcr')
    # ### end Alembic commands ###
