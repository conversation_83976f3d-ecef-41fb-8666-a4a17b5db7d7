"""add fields to product

Revision ID: 877dcfc462d5
Revises: 778c71fff889
Create Date: 2024-11-12 20:01:04.720521

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '877dcfc462d5'
down_revision: Union[str, None] = '778c71fff889'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('product_type', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.add_column('product', sa.Column('version', sqlmodel.sql.sqltypes.AutoString(), nullable=False))
    op.alter_column('product', 'brand',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True)
    op.alter_column('product', 'country_of_use',
               existing_type=mysql.VARCHAR(length=255),
               nullable=True)
    op.drop_index('product_name_composite_unique_key', table_name='product')
    op.drop_column('product', 'source')

    op.add_column('node', sa.Column('component_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.create_foreign_key('node_component_id_fkey', 'node', 'product', ['component_id'], ['product_id'], ondelete='SET NULL', onupdate='CASCADE')
    op.add_column('node', sa.Column('component_version', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('source', mysql.VARCHAR(length=255), nullable=False))
    op.create_index('product_name_composite_unique_key', 'product', ['product_name', 'brand', 'source'], unique=False)
    op.alter_column('product', 'country_of_use',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False)
    op.alter_column('product', 'brand',
               existing_type=mysql.VARCHAR(length=255),
               nullable=False)
    op.drop_column('product', 'version')
    op.drop_column('product', 'product_type')
    op.drop_constraint('node_component_id_fkey', 'node', type_='foreignkey')
    op.drop_column('node', 'component_id')
    op.drop_column('node', 'component_version')
    # ### end Alembic commands ###
