"""remove node unique constraint

Revision ID: 0ada90263fad
Revises: be0ad716d912
Create Date: 2025-02-05 21:04:09.002508

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '0ada90263fad'
down_revision: Union[str, None] = 'be0ad716d912'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    op.drop_constraint('fk_node_product', 'node', type_='foreignkey')

    op.drop_constraint('uix_product_id_name', 'node', type_='unique')

    op.create_foreign_key(
        'fk_node_product',
        'node',
        'product',
        ['product_id'],
        ['product_id'],
        onupdate='CASCADE',
        ondelete='CASCADE'
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    raise ValueError("Downgrade not supported for this migration.")
    # ### end Alembic commands ###
