"""remove old dbs

Revision ID: be0ad716d912
Revises: 8c464d372e27
Create Date: 2025-02-01 21:15:28.929159

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'be0ad716d912'
down_revision: Union[str, None] = '8c464d372e27'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('product_ingredient')
    op.drop_table('product_manufacturing')
    op.drop_index('name', table_name='ingredient')
    op.drop_table('ingredient')
    op.drop_index('ix_ingredient_suppliers_ingredient_name', table_name='ingredient_suppliers')
    op.drop_table('ingredient_suppliers')
    op.drop_index('ix_reuse_resource_name', table_name='reuse_resource')
    op.drop_index('uq_reuse_resource', table_name='reuse_resource')
    op.drop_table('reuse_resource')
    op.drop_table('product_transport')
    op.drop_table('product_packaging')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'product_attributes', type_='foreignkey')
    op.create_foreign_key('fk_product_attributes_attribute_def', 'product_attributes', 'product_attribute_def', ['attribute_def_id'], ['id'], onupdate='CASCADE', ondelete='CASCADE')
    op.create_table('product_packaging',
    sa.Column('product_id', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('supplier_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('packaging_level', mysql.VARCHAR(length=15), nullable=False),
    sa.Column('packaging_material', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('packaging_item', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('alternate_name', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('weight_grams', mysql.FLOAT(), nullable=False),
    sa.Column('recycled_material', mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
    sa.Column('recycled_content_rate', mysql.FLOAT(), nullable=False),
    sa.Column('recyclable', mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
    sa.Column('loss_factor', mysql.FLOAT(), nullable=False),
    sa.Column('number_of_units', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('reusable_packaging', mysql.TINYINT(display_width=1), autoincrement=False, nullable=False),
    sa.Column('number_of_reuses', mysql.FLOAT(), nullable=False),
    sa.Column('origin_address_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('destination_address_id', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['destination_address_id'], ['address.id'], name='fk_product_packaging_destination_address', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['origin_address_id'], ['address.id'], name='fk_product_packaging_origin_address', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], name='fk_product_packaging_product', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['supplier_id'], ['supplier.id'], name='fk_product_packaging_supplier', onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('product_transport',
    sa.Column('product_id', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('segment_type', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('transport_mode', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('segment_name', mysql.VARCHAR(length=100), nullable=False),
    sa.Column('distance_km', mysql.FLOAT(), nullable=True),
    sa.Column('origin_address_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('destination_address_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['destination_address_id'], ['address.id'], name='fk_product_transport_destination_address', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['origin_address_id'], ['address.id'], name='fk_product_transport_origin_address', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], name='fk_product_transport_product', onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_id', 'segment_type', 'transport_mode'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_table('reuse_resource',
    sa.Column('product_packaging_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('product_id', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('amount', mysql.FLOAT(), nullable=False),
    sa.Column('unit', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('emissions_kg_co2e_per_g', mysql.FLOAT(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], name='fk_reuse_resource_product', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_packaging_id'], ['product_packaging.id'], name='fk_reuse_resource_product_packaging', onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_reuse_resource', 'reuse_resource', ['name', 'product_packaging_id', 'product_id'], unique=False)
    op.create_index('ix_reuse_resource_name', 'reuse_resource', ['name'], unique=False)
    op.create_table('ingredient_suppliers',
    sa.Column('product_id', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('supplier_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('origin_location', mysql.JSON(), nullable=True),
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('ingredient_name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('percentage_sourced', mysql.FLOAT(), nullable=False),
    sa.Column('supplier_name', mysql.VARCHAR(length=200), nullable=True),
    sa.Column('wastage_rate', mysql.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], name='fk_ingredient_suppliers_product', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['supplier_id'], ['supplier.id'], name='fk_ingredient_suppliers_supplier', onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('ix_ingredient_suppliers_ingredient_name', 'ingredient_suppliers', ['ingredient_name'], unique=False)
    op.create_table('ingredient',
    sa.Column('attributes', mysql.JSON(), nullable=True),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('chem_name', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('proprietary_name', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('hsc', mysql.VARCHAR(length=1000), nullable=True),
    sa.Column('ean', mysql.VARCHAR(length=15), nullable=True),
    sa.Column('cas', mysql.VARCHAR(length=13), nullable=True),
    sa.Column('ingredient_type', mysql.VARCHAR(length=100), nullable=True),
    sa.Column('flags', mysql.VARCHAR(length=150), nullable=False),
    sa.Column('ef_kgco2e', mysql.FLOAT(), nullable=True),
    sa.Column('ef_source', mysql.VARCHAR(length=500), nullable=True),
    sa.Column('synonyms', mysql.VARCHAR(length=1000), nullable=True),
    sa.Column('keywords', mysql.VARCHAR(length=255), nullable=True),
    sa.Column('status', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('name'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('name', 'ingredient', ['name'], unique=False)
    op.create_table('product_manufacturing',
    sa.Column('product_id', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('tenant_emissions_factor_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('manufacturing_process', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('electricity_kwh', mysql.FLOAT(), nullable=False),
    sa.Column('water_liters', mysql.FLOAT(), nullable=False),
    sa.Column('amount_of_product_kg', mysql.FLOAT(), nullable=False),
    sa.Column('geography_iso3', mysql.VARCHAR(length=3), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], name='fk_product_manufacturing_product', onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tenant_emissions_factor_id'], ['tenant_emissions_factor.id'], name='fk_product_manufacturing_emissions_factor', onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('product_id', 'manufacturing_process'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uix_product_id_name', 'node', ['product_id', 'name', 'component_name'], unique=False)
    op.create_index('node_component_id_fkey', 'node', ['component_id'], unique=False)
    op.create_index('ix_node_node_type', 'node', ['node_type'], unique=False)
    op.create_table('product_ingredient',
    sa.Column('product_id', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('emissions_factor_id', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('ingredient_name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('weight', mysql.FLOAT(), nullable=False),
    sa.Column('unit', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('loss_factor', mysql.FLOAT(), nullable=False),
    sa.Column('disposed_weight', mysql.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('recycled_content_rate', mysql.FLOAT(), nullable=False),
    sa.ForeignKeyConstraint(['emissions_factor_id'], ['tenant_emissions_factor.id'], name='fk_product_ingredient_emissions_factor', onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], name='fk_product_ingredient_product', onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_id', 'ingredient_name'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    # ### end Alembic commands ###
