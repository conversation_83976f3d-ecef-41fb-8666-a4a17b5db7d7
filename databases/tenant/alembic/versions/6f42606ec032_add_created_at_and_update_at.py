"""add created at and update at

Revision ID: 6f42606ec032
Revises: 601e74e20530
Create Date: 2025-01-10 10:46:17.361654

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '6f42606ec032'
down_revision: Union[str, None] = '601e74e20530'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('node', sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('node', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('edge', sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('edge', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('product', sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('product', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('tenant_emissions_factor', sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('tenant_emissions_factor', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('tenant_emissions_factor_value', sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('tenant_emissions_factor_value', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('tenant_intermediate_exchange', sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    op.add_column('tenant_intermediate_exchange', sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tenant_intermediate_exchange', 'updated_at')
    op.drop_column('tenant_intermediate_exchange', 'created_at')
    op.drop_column('tenant_emissions_factor_value', 'updated_at')
    op.drop_column('tenant_emissions_factor_value', 'created_at')
    op.drop_column('tenant_emissions_factor', 'updated_at')
    op.drop_column('tenant_emissions_factor', 'created_at')
    op.drop_column('product', 'updated_at')
    op.drop_column('product', 'created_at')
    op.drop_column('edge', 'updated_at')
    op.drop_column('edge', 'created_at')
    op.drop_column('node', 'updated_at')
    op.drop_column('node', 'created_at')
    # ### end Alembic commands ###
