"""add tenant impact indicator

Revision ID: 116dd5b4990f
Revises: 6f42606ec032
Create Date: 2025-01-13 12:49:06.790518

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql
from databases.engine import engine_pool
from sqlmodel import Session
# revision identifiers, used by Alembic.
revision: str = '116dd5b4990f'
down_revision: Union[str, None] = '6f42606ec032'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tenant_impact_indicator',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('lcia_method', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('category', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('indicator', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('lcia_method', 'category', 'indicator', name='uq_tenant_impact_indicator')
    )

    op.create_table('tenant_emissions_factor_value_new',
        sa.Column('emissions_factor_id', sa.Integer(), nullable=False),
        sa.Column('impact_indicator_id', sa.Integer(), nullable=False),
        sa.Column('amount', sa.Float(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('emissions_factor_id', 'impact_indicator_id'),
        sa.ForeignKeyConstraint(['impact_indicator_id'], ['tenant_impact_indicator.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['emissions_factor_id'], ['tenant_emissions_factor.id'], ondelete='CASCADE')
    )

    # Restructure old table

    connection = op.get_bind()
    engine = engine_pool.get_shared_engine()
    result = connection.execute(sa.text("SELECT * FROM tenant_emissions_factor_value"))
    for row in result:
        lcia_method_id = row['lcia_method_id']
        lcia_method = connection.execute(sa.text("SELECT * FROM tenant_lcia_method WHERE id = :lcia_method_id"), {'lcia_method_id': lcia_method_id}).fetchone()

        impact_category_id = row['impact_category_id']
        impact_category = connection.execute(sa.text("SELECT * FROM tenant_impact_category WHERE id = :impact_category_id"), {'impact_category_id': impact_category_id}).fetchone()
        impact_indicator = connection.execute(
            sa.text("SELECT * FROM tenant_impact_indicator WHERE lcia_method = :lcia_method AND category = :category"),
            {
                'lcia_method': lcia_method['name'],
                'category': impact_category['name'],
            }
        ).fetchone()

        if not impact_indicator:
            with Session(engine) as session:
                shared_impact_indicator = session.execute(
                    sa.text("SELECT * FROM impact_indicator WHERE category = :category AND lcia_method = :lcia_method"),
                    {
                        'lcia_method': lcia_method['name'],
                        'category': impact_category['name'],
                    }
                ).first()

            connection.execute(
                sa.text("INSERT INTO tenant_impact_indicator (lcia_method, category, indicator, unit, description) VALUES (:lcia_method, :category, :indicator, :unit, :description)"),
                {
                    'lcia_method': shared_impact_indicator['lcia_method'],
                    'category': shared_impact_indicator['category'],
                    'indicator': shared_impact_indicator['indicator'],
                    'unit': shared_impact_indicator['unit'],
                    'description': shared_impact_indicator['description'],
                }
            )

            impact_indicator = connection.execute(
                sa.text("SELECT * FROM tenant_impact_indicator WHERE lcia_method = :lcia_method AND category = :category AND indicator = :indicator"),
                {
                    'lcia_method': shared_impact_indicator['lcia_method'],
                    'category': shared_impact_indicator['category'],
                    'indicator': shared_impact_indicator['indicator'],
                }
            ).fetchone()
        
        connection.execute(
            sa.text("INSERT INTO tenant_emissions_factor_value_new (emissions_factor_id, impact_indicator_id, amount, created_at, updated_at) VALUES (:emissions_factor_id, :impact_indicator_id, :amount, :created_at, :updated_at)"),
            {
                'emissions_factor_id': row['emissions_factor_id'],
                'impact_indicator_id': impact_indicator['id'],
                'amount': row['amount'],
                'created_at': row['created_at'],
                'updated_at': row['updated_at'],
            }
        )

    op.drop_table('tenant_emissions_factor_value')
    op.execute('ALTER TABLE tenant_emissions_factor_value_new RENAME TO tenant_emissions_factor_value')
    
    op.drop_index('uq_tenant_impact_category', table_name='tenant_impact_category')
    op.drop_table('tenant_impact_category')
    op.drop_index('uq_tenant_lcia_method_name', table_name='tenant_lcia_method')
    op.drop_table('tenant_lcia_method')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenant_emissions_factor_value', sa.Column('impact_category_id', mysql.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('tenant_emissions_factor_value', sa.Column('lcia_method_id', mysql.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'tenant_emissions_factor_value', type_='foreignkey')
    op.create_foreign_key('tenant_emissions_factor_value_ibfk_2', 'tenant_emissions_factor_value', 'tenant_impact_category', ['impact_category_id'], ['id'], ondelete='CASCADE')
    op.create_foreign_key('tenant_emissions_factor_value_ibfk_3', 'tenant_emissions_factor_value', 'tenant_lcia_method', ['lcia_method_id'], ['id'], ondelete='CASCADE')
    op.drop_column('tenant_emissions_factor_value', 'impact_indicator_id')
    op.create_table('tenant_lcia_method',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_tenant_lcia_method_name', 'tenant_lcia_method', ['name'], unique=False)
    op.create_table('tenant_impact_category',
    sa.Column('id', mysql.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('indicator', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('unit', mysql.VARCHAR(length=255), nullable=False),
    sa.Column('description', mysql.VARCHAR(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    mysql_collate='utf8mb4_0900_ai_ci',
    mysql_default_charset='utf8mb4',
    mysql_engine='InnoDB'
    )
    op.create_index('uq_tenant_impact_category', 'tenant_impact_category', ['name', 'indicator'], unique=False)
    op.drop_table('tenant_impact_indicator')
    # ### end Alembic commands ###
