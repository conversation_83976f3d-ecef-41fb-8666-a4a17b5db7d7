"""create prediction override table

Revision ID: 8701f247b507
Revises: 0ada90263fad
Create Date: 2025-02-28 10:35:43.767522

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8701f247b507'
down_revision: Union[str, None] = '0ada90263fad'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('prediction_override',
    sa.Column('api_name', sqlmodel.sql.sqltypes.AutoString(80), nullable=False),
    sa.Column('query', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('override_value', sa.Text, nullable=False),
    sa.<PERSON>umn('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('query', 'api_name')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('prediction_override')
    # ### end Alembic commands ###
