"""load emissions factor values from shared into tenant

Revision ID: 601e74e20530
Revises: 643eef1f4665
Create Date: 2024-12-21 22:26:54.827654

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from sqlalchemy.dialects import mysql


# revision identifiers, used by Alembic.
revision: str = '601e74e20530'
down_revision: Union[str, None] = '643eef1f4665'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    connection = op.get_bind()
    connection.execute(sa.text("UPDATE product SET lca_output = NULL"))

    op.drop_column('tenant_emissions_factor', 'kg_co2e')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenant_emissions_factor', sa.Column('kg_co2e', sa.Float(), nullable=True))
    # ### end Alembic commands ###
