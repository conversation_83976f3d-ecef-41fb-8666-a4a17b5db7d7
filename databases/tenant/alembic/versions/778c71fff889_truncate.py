"""truncate

Revision ID: 778c71fff889
Revises: 
Create Date: 2024-11-12 19:51:46.964177

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '778c71fff889'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('address',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('address_1', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=True),
    sa.Column('address_2', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=True),
    sa.Column('latitude', sa.Float(), nullable=True),
    sa.Column('longitude', sa.Float(), nullable=True),
    sa.Column('city', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=True),
    sa.Column('state_or_province', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('postal_code', sqlmodel.sql.sqltypes.AutoString(length=5), nullable=True),
    sa.Column('country', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ingredient',
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('chem_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('proprietary_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('hsc', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('ean', sqlmodel.sql.sqltypes.AutoString(length=15), nullable=True),
    sa.Column('cas', sqlmodel.sql.sqltypes.AutoString(length=13), nullable=True),
    sa.Column('ingredient_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('flags', sqlmodel.sql.sqltypes.AutoString(length=150), nullable=False),
    sa.Column('ef_kgco2e', sa.Float(), nullable=True),
    sa.Column('ef_source', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('synonyms', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('keywords', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('name'),
    sa.UniqueConstraint('name')
    )
    op.create_table('product_attribute_def',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('key', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('default_value', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('type', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('key')
    )
    op.create_table('tenant_emissions_factor',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('activity_name', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.Column('geography', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=120), nullable=False),
    sa.Column('activity_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('reference_product', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.Column('reference_product_amount', sa.Float(), nullable=False),
    sa.Column('kg_co2e', sa.Float(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('activity_description', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('activity_name', 'reference_product', 'geography', 'source', name='uq_emissions_factors_composite')
    )
    op.create_table('supplier',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('supplier_level', sa.Integer(), nullable=False),
    sa.Column('supplier_type', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=False),
    sa.Column('supplier_name', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=True),
    sa.Column('tax_registry_number', sqlmodel.sql.sqltypes.AutoString(length=11), nullable=True),
    sa.Column('primary_contact_name', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=True),
    sa.Column('primary_contact_email', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('primary_address_id', sa.Integer(), nullable=True),
    sa.Column('website', sqlmodel.sql.sqltypes.AutoString(length=250), nullable=True),
    sa.ForeignKeyConstraint(['primary_address_id'], ['address.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('tenant_intermediate_exchange',
    sa.Column('exchange_emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('parent_emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('exchange_name', sqlmodel.sql.sqltypes.AutoString(length=120), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('input_stream', sa.Boolean(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=64), nullable=False),
    sa.ForeignKeyConstraint(['exchange_emissions_factor_id'], ['tenant_emissions_factor.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['parent_emissions_factor_id'], ['tenant_emissions_factor.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('product',
    sa.Column('supplier_id', sa.Integer(), nullable=True),
    sa.Column('annual_sales_volume_units', sa.BigInteger(), nullable=True),
    sa.Column('factory_locale', sa.JSON(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('product_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('cloned_from_product_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('product_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('brand', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('content_weight', sa.Float(), nullable=True),
    sa.Column('total_weight', sa.Float(), nullable=True),
    sa.Column('uses_per_package', sa.Float(), nullable=True),
    sa.Column('weight_unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('primary_category', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('product_image', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('back_of_pack_image', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('country_of_use', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('storage', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('source', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('package_type', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('functional_type', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('functional_unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('recycling_information', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('other_information', sqlmodel.sql.sqltypes.AutoString(length=3000), nullable=True),
    sa.Column('manufacturer', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('reusable_packaging', sa.Boolean(), nullable=True),
    sa.Column('number_of_reuses', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['supplier_id'], ['supplier.id'], ),
    sa.PrimaryKeyConstraint('product_id'),
    sa.UniqueConstraint('cloned_from_product_id', name='cloned_from_product_id_unique_key'),
    sa.UniqueConstraint('product_name', 'brand', 'source', name='product_name_composite_unique_key')
    )
    op.create_index(op.f('ix_product_cloned_from_product_id'), 'product', ['cloned_from_product_id'], unique=False)
    op.create_index(op.f('ix_product_product_id'), 'product', ['product_id'], unique=False)
    op.create_table('ingredient_suppliers',
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('supplier_id', sa.Integer(), nullable=True),
    sa.Column('origin_location', sa.JSON(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('ingredient_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('percentage_sourced', sa.Float(), nullable=False),
    sa.Column('supplier_name', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=True),
    sa.Column('wastage_rate', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['supplier_id'], ['supplier.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ingredient_suppliers_ingredient_name'), 'ingredient_suppliers', ['ingredient_name'], unique=False)
    op.create_table('node',
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('location_id', sa.Integer(), nullable=True),
    sa.Column('emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('supplier_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('amount', sa.Float(), nullable=True),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('scrap_rate', sa.Float(), nullable=True),
    sa.Column('node_type', sqlmodel.sql.sqltypes.AutoString(length=120), nullable=False),
    sa.Column('recycled_content_rate', sa.Float(), nullable=True),
    sa.Column('disposed_weight_kg', sa.Float(), nullable=True),
    sa.Column('component_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('packaging_level', sqlmodel.sql.sqltypes.AutoString(length=15), nullable=True),
    sa.Column('segment_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('transport_mode', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.ForeignKeyConstraint(['emissions_factor_id'], ['tenant_emissions_factor.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['location_id'], ['address.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['supplier_id'], ['supplier.id'], onupdate='CASCADE', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('product_id', 'name', name='uix_product_id_name')
    )
    op.create_index(op.f('ix_node_node_type'), 'node', ['node_type'], unique=False)
    op.create_table('product_attributes',
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('attribute_def_id', sa.Integer(), nullable=False),
    sa.Column('value', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.ForeignKeyConstraint(['attribute_def_id'], ['product_attribute_def.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_id', 'attribute_def_id', name='product_attributes_composite_primary_key')
    )
    op.create_table('product_ingredient',
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('ingredient_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('weight', sa.Float(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('loss_factor', sa.Float(), nullable=False),
    sa.Column('disposed_weight', sa.Integer(), nullable=True),
    sa.Column('recycled_content_rate', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['emissions_factor_id'], ['tenant_emissions_factor.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_id', 'ingredient_name', name='product_ingredient_composite_primary_key')
    )
    op.create_table('product_manufacturing',
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('tenant_emissions_factor_id', sa.Integer(), nullable=True),
    sa.Column('manufacturing_process', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('electricity_kwh', sa.Float(), nullable=False),
    sa.Column('water_liters', sa.Float(), nullable=False),
    sa.Column('amount_of_product_kg', sa.Float(), nullable=False),
    sa.Column('geography_iso3', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tenant_emissions_factor_id'], ['tenant_emissions_factor.id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_id', 'manufacturing_process')
    )
    op.create_table('product_packaging',
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('supplier_id', sa.Integer(), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('packaging_level', sqlmodel.sql.sqltypes.AutoString(length=15), nullable=False),
    sa.Column('packaging_material', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('packaging_item', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('alternate_name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('weight_grams', sa.Float(), nullable=False),
    sa.Column('recycled_material', sa.Boolean(), nullable=False),
    sa.Column('recycled_content_rate', sa.Float(), nullable=False),
    sa.Column('recyclable', sa.Boolean(), nullable=False),
    sa.Column('loss_factor', sa.Float(), nullable=False),
    sa.Column('number_of_units', sa.Integer(), nullable=False),
    sa.Column('reusable_packaging', sa.Boolean(), nullable=False),
    sa.Column('number_of_reuses', sa.Float(), nullable=False),
    sa.Column('origin_address_id', sa.Integer(), nullable=False),
    sa.Column('destination_address_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['destination_address_id'], ['address.id'], ),
    sa.ForeignKeyConstraint(['origin_address_id'], ['address.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['supplier_id'], ['supplier.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('product_transport',
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('segment_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('transport_mode', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('segment_name', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('distance_km', sa.Float(), nullable=True),
    sa.Column('origin_address_id', sa.Integer(), nullable=True),
    sa.Column('destination_address_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['destination_address_id'], ['address.id'], ),
    sa.ForeignKeyConstraint(['origin_address_id'], ['address.id'], ),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('product_id', 'segment_type', 'transport_mode')
    )
    op.create_table('edge',
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('to_node_id', sa.Integer(), nullable=False),
    sa.Column('from_node_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['from_node_id'], ['node.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['to_node_id'], ['node.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('to_node_id', 'from_node_id', name='edge_primary_key')
    )
    op.create_table('reuse_resource',
    sa.Column('product_packaging_id', sa.Integer(), nullable=True),
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('emissions_kg_co2e_per_g', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['product_id'], ['product.product_id'], onupdate='CASCADE', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['product_packaging_id'], ['product_packaging.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', 'product_packaging_id', 'product_id', name='uq_reuse_resource')
    )
    op.create_index(op.f('ix_reuse_resource_name'), 'reuse_resource', ['name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_reuse_resource_name'), table_name='reuse_resource')
    op.drop_table('reuse_resource')
    op.drop_table('edge')
    op.drop_table('product_transport')
    op.drop_table('product_packaging')
    op.drop_table('product_manufacturing')
    op.drop_table('product_ingredient')
    op.drop_table('product_attributes')
    op.drop_index(op.f('ix_node_node_type'), table_name='node')
    op.drop_table('node')
    op.drop_index(op.f('ix_ingredient_suppliers_ingredient_name'), table_name='ingredient_suppliers')
    op.drop_table('ingredient_suppliers')
    op.drop_index(op.f('ix_product_product_id'), table_name='product')
    op.drop_index(op.f('ix_product_cloned_from_product_id'), table_name='product')
    op.drop_table('product')
    op.drop_table('tenant_intermediate_exchange')
    op.drop_table('supplier')
    op.drop_table('tenant_emissions_factor')
    op.drop_table('product_attribute_def')
    op.drop_table('ingredient')
    op.drop_table('address')
    # ### end Alembic commands ###
