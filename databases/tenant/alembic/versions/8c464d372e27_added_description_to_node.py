"""added description to node

Revision ID: 8c464d372e27
Revises: 116dd5b4990f
Create Date: 2025-01-17 00:03:49.397330

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '8c464d372e27'
down_revision: Union[str, None] = '116dd5b4990f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('node', sa.Column('description', sqlmodel.sql.sqltypes.AutoString(), nullable=True))

    op.create_unique_constraint('uix_product_id_name_new', 'node', ['product_id', 'name', 'component_name'])

    with op.batch_alter_table('node') as batch_op:
        batch_op.drop_constraint('uix_product_id_name', type_='unique')

    op.execute('ALTER TABLE node RENAME INDEX uix_product_id_name_new TO uix_product_id_name')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('uix_product_id_name_old', 'node', ['product_id', 'name'])

    with op.batch_alter_table('node') as batch_op:
        batch_op.drop_constraint('uix_product_id_name', type_='unique')

    op.execute('ALTER TABLE node RENAME INDEX uix_product_id_name_old TO uix_product_id_name')
    
    op.drop_column('node', 'description')
    # ### end Alembic commands ###
