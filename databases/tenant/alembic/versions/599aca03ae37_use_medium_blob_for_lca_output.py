"""use medium blob for lca output

Revision ID: 599aca03ae37
Revises: 0ada90263fad
Create Date: 2025-03-08 17:37:11.300783

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '599aca03ae37'
down_revision: Union[str, None] = '8701f247b507'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('product', 'lca_output', type_=sa.dialects.mysql.MEDIUMBLOB())
    # ### end Alembic commands ###


def downgrade() -> None:
    op.alter_column('product', 'lca_output', type_=sa.dialects.mysql.BLOB())
