"""add fulltext index for emissions_factors.activity_name

Revision ID: cdac9f27a812
Revises: a1aafbfdba29
Create Date: 2025-04-18 17:00:52.758709

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'cdac9f27a812'
down_revision: Union[str, None] = 'a1aafbfdba29'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE tenant_emissions_factor ADD FULLTEXT INDEX `fulltext`(activity_name);")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    raise ValueError("Downgrade not supported for this migration.")
    # ### end Alembic commands ###
