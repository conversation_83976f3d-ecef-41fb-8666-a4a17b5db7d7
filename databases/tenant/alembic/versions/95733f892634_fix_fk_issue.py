"""fix fk issue

Revision ID: 95733f892634
Revises: 66482ce8155b
Create Date: 2024-11-30 11:36:41.497871

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect

# revision identifiers, used by Alembic.
revision: str = '95733f892634'
down_revision: Union[str, None] = '66482ce8155b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


tables_with_fks = {
    'edge': [
        ('fk_edge_from_node', 'node', 'from_node_id', 'id', 'CASCADE', 'CASCADE'),
        ('fk_edge_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_edge_to_node', 'node', 'to_node_id', 'id', 'CASCADE', 'CASCADE'),
    ],
    'node': [
        ('fk_node_emissions_factor', 'tenant_emissions_factor', 'emissions_factor_id', 'id', 'CASCADE', 'SET NULL'),
        ('fk_node_location', 'address', 'location_id', 'id', 'CASCADE', 'CASCADE'),
        ('fk_node_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_node_supplier', 'supplier', 'supplier_id', 'id', 'CASCADE', 'SET NULL'),
    ],
    'ingredient_suppliers': [
        ('fk_ingredient_suppliers_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_ingredient_suppliers_supplier', 'supplier', 'supplier_id', 'id', 'CASCADE', 'SET NULL'),
    ],
    'product_attributes': [
        ('fk_product_attributes_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_product_attributes_attribute_def', 'product_attribute_def', 'attribute_def_id', 'id', 'CASCADE', 'CASCADE'),
    ],
    'product_ingredient': [
        ('fk_product_ingredient_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_product_ingredient_emissions_factor', 'tenant_emissions_factor', 'emissions_factor_id', 'id', 'CASCADE', 'SET NULL'),
    ],
    'product_manufacturing': [
        ('fk_product_manufacturing_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_product_manufacturing_emissions_factor', 'tenant_emissions_factor', 'tenant_emissions_factor_id', 'id', 'CASCADE', 'SET NULL'),
    ],
    'product_packaging': [
        ('fk_product_packaging_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_product_packaging_supplier', 'supplier', 'supplier_id', 'id', 'CASCADE', 'SET NULL'),
        ('fk_product_packaging_origin_address', 'address', 'origin_address_id', 'id', 'CASCADE', 'CASCADE'),
        ('fk_product_packaging_destination_address', 'address', 'destination_address_id', 'id', 'CASCADE', 'CASCADE'),
    ],
    'product_transport': [
        ('fk_product_transport_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_product_transport_origin_address', 'address', 'origin_address_id', 'id', 'CASCADE', 'CASCADE'),
        ('fk_product_transport_destination_address', 'address', 'destination_address_id', 'id', 'CASCADE', 'CASCADE'),
    ],
    'reuse_resource': [
        ('fk_reuse_resource_product', 'product', 'product_id', 'product_id', 'CASCADE', 'CASCADE'),
        ('fk_reuse_resource_product_packaging', 'product_packaging', 'product_packaging_id', 'id', 'CASCADE', 'CASCADE'),
    ],
    # Add other tables and their foreign keys as needed
}

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)

    for table, fks in tables_with_fks.items():
        foreign_keys = inspector.get_foreign_keys(table)
        for fk in foreign_keys:
            op.drop_constraint(fk['name'], table, type_='foreignkey')

    for table, fks in tables_with_fks.items():
        for fk_name, referred_table, constrained_column, referred_column, onupdate, ondelete in fks:
            op.create_foreign_key(
                fk_name,
                table,
                referred_table,
                [constrained_column],
                [referred_column],
                onupdate=onupdate,
                ondelete=ondelete
            )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop foreign keys in reverse order of creation
    raise ValueError("Downgrade not supported for this migration.")

    # ### end Alembic commands ###