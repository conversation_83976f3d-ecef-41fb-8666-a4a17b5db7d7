"""added scrap fate column

Revision ID: c0ee1cb3da14
Revises: 599aca03ae37
Create Date: 2025-03-10 15:05:13.585704

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = 'c0ee1cb3da14'
down_revision: Union[str, None] = '599aca03ae37'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('node', sa.Column('scrap_fate', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('node', 'scrap_fate')
    # ### end Alembic commands ###
