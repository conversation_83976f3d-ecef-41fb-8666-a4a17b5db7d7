"""add lca_output column

Revision ID: c07742330a90
Revises: 95733f892634
Create Date: 2024-12-03 19:15:03.257226

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'c07742330a90'
down_revision: Union[str, None] = '95733f892634'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product', sa.Column('lca_output', sa.BLOB(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('product', 'lca_output')
    # ### end Alembic commands ###
