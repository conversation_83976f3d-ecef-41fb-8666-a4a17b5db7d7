"""add cascade update

Revision ID: 66482ce8155b
Revises: 877dcfc462d5
Create Date: 2024-11-14 12:57:09.109434

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '66482ce8155b'
down_revision: Union[str, None] = '877dcfc462d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('edge_ibfk_2', 'edge', type_='foreignkey')
    op.create_foreign_key(
        'fk_edge_product_id',
        'edge',
        'product',
        ['product_id'],
        ['product_id'],
        onupdate="CASCADE",
        ondelete="CASCADE",
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('fk_edge_product_id', 'edge', type_='foreignkey')
    op.create_foreign_key(
        'edge_ibfk_2',
        'edge',
        'product',
        ['product_id'],
        ['product_id'],
        ondelete="CASCADE",
    )