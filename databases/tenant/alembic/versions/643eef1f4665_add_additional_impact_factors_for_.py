"""add additional impact factors for tenant db

Revision ID: 643eef1f4665
Revises: c07742330a90
Create Date: 2024-12-10 11:45:58.313042

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel

# revision identifiers, used by Alembic.
revision: str = '643eef1f4665'
down_revision: Union[str, None] = 'c07742330a90'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tenant_impact_category',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('indicator', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('unit', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', 'indicator', name='uq_tenant_impact_category')
    )
    op.create_table('tenant_lcia_method',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', name='uq_tenant_lcia_method_name')
    )
    op.create_table('tenant_emissions_factor_value',
    sa.Column('emissions_factor_id', sa.Integer(), nullable=False),
    sa.Column('impact_category_id', sa.Integer(), nullable=False),
    sa.Column('lcia_method_id', sa.Integer(), nullable=False),
    sa.Column('amount', sa.Float(), nullable=False),
    sa.ForeignKeyConstraint(['emissions_factor_id'], ['tenant_emissions_factor.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['impact_category_id'], ['tenant_impact_category.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['lcia_method_id'], ['tenant_lcia_method.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('emissions_factor_id', 'impact_category_id', 'lcia_method_id')
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('tenant_emissions_factor_value')
    op.drop_table('tenant_lcia_method')
    op.drop_table('tenant_impact_category')
    # ### end Alembic commands ###
