"""added mass allocation and disposal mode rate columns

Revision ID: a1aafbfdba29
Revises: c0ee1cb3da14
Create Date: 2025-03-20 22:20:20.923710

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = 'a1aafbfdba29'
down_revision: Union[str, None] = 'c0ee1cb3da14'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('node', sa.Column('mass_allocation_per_kg', sa.<PERSON>(), nullable=False))
    op.add_column('node', sa.Column('eol_recycling_rate', sa.Float(), nullable=False))
    op.add_column('node', sa.Column('eol_landfill_rate', sa.Float(), nullable=False))
    op.add_column('node', sa.Column('eol_incineration_rate', sa.Float(), nullable=False))
    op.add_column('node', sa.Column('eol_composting_rate', sa.Float(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('node', 'eol_composting_rate')
    op.drop_column('node', 'eol_incineration_rate')
    op.drop_column('node', 'eol_landfill_rate')
    op.drop_column('node', 'eol_recycling_rate')
    op.drop_column('node', 'mass_allocation_per_kg')
    # ### end Alembic commands ###
