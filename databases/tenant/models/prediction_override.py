from datetime import datetime, timezone
from sqlmodel import SQLModel, Field, Column
from sqlalchemy.sql import func
from sqlalchemy import Text


class PredictionOverride(SQLModel, table=True):
    __tablename__ = "prediction_override"

    query: str = Field(primary_key=True, max_length=255)
    api_name: str = Field(primary_key=True, max_length=80)
    override_value: str = Field(sa_column=Column(Text), nullable=False)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    class Config:
        arbitrary_types_allowed = True
        fields = {
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }
