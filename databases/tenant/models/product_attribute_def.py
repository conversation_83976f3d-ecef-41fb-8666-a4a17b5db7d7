from typing import Optional
from sqlmodel import (
    SQLModel,
    Field,
    Enum,
)


# pylint: disable=too-many-ancestors
class Type(str, Enum):
    STRING = "string"


class ProductAttributeDef(SQLModel, table=True):
    __tablename__ = "product_attribute_def"
    id: int = Field(primary_key=True)
    key: str = Field(nullable=False, unique=True)
    default_value: Optional[str] = Field(nullable=True)
    type: Type = Field(nullable=False, default=Type.STRING)

    class Config:
        arbitrary_types_allowed = True
