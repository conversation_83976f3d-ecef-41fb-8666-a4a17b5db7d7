from sqlmodel import (
    SQLModel,
    Field,
    String,
    Integer,
    PrimaryKeyConstraint,
    ForeignKey,
    Column,
    Relationship,
)


class ProductAttributes(SQLModel, table=True):
    __tablename__ = "product_attributes"
    __table_args__ = (
        PrimaryKeyConstraint(
            "product_id",
            "attribute_def_id",
            name="product_attributes_composite_primary_key",
        ),
    )
    product_id: str = Field(
        sa_column=Column(
            String(255),
            ForeignKey(
                "product.product_id",
                ondelete="CASCADE",
                onupdate="CASCADE",
            ),
        ),
    )
    attribute_def_id: int = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "product_attribute_def.id",
                ondelete="CASCADE",
            ),
        ),
    )
    value: str = Field(nullable=False)
    attribute_def: "ProductAttributeDef" = Relationship()

    class Config:
        arbitrary_types_allowed = True
