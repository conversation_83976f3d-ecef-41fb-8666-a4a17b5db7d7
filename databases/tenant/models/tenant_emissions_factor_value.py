from datetime import datetime
from pydantic import BaseModel
from sqlalchemy.sql import func
from sqlmodel import (
    SQLModel,
    Field,
    Column,
    Integer,
    ForeignKey,
    Relationship,
)
from databases.tenant.models.tenant_impact_indicator import TenantImpactIndicator
from databases.tenant.models.tenant_emissions_factor import TenantEmissionsFactor
from databases.shared.models.emissions_factor_value import EmissionFactorValueRW


class TenantEmissionsFactorValue(SQLModel, table=True):
    __tablename__ = "tenant_emissions_factor_value"
    emissions_factor_id: int = Field(
        nullable=False,
        sa_column=Column(
            Integer,
            ForeignKey(
                "tenant_emissions_factor.id",
                ondelete="CASCADE",
            ),
            key="fk_tenant_emissions_factor_id",
            primary_key=True,
        ),
        index=True,
    )
    emissions_factor: TenantEmissionsFactor = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[TenantEmissionsFactorValue.emissions_factor_id]",
        }
    )
    impact_indicator_id: int = Field(
        nullable=False,
        sa_column=Column(
            Integer,
            ForeignKey("tenant_impact_indicator.id", ondelete="CASCADE"),
            key="fk_tenant_impact_indicator_id",
            primary_key=True,
        ),
        index=True,
    )
    impact_indicator: TenantImpactIndicator = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[TenantEmissionsFactorValue.impact_indicator_id]",
        }
    )
    amount: float
    created_at: datetime = Field(
        default_factory=lambda: func.now(),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: func.now(),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    class Config:
        arbitrary_types_allowed = True
        fields = {
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }

class TenantEmissionsFactorValueRead(BaseModel):
    emissions_factor: TenantEmissionsFactor
    impact_indicator: TenantImpactIndicator
    amount: float

    def to_emission_factor_value_rw(self) -> EmissionFactorValueRW:
        """
        Convert TenantEmissionsFactorValueRead to EmissionFactorValueRW.

        Returns:
            EmissionFactorValueRW object with values from this instance
        """
        return EmissionFactorValueRW(
            lcia_method=self.impact_indicator.lcia_method,
            impact_category_name=self.impact_indicator.category,
            impact_category_indicator=self.impact_indicator.indicator,
            impact_category_unit=self.impact_indicator.unit,
            amount=self.amount
        )
