from datetime import datetime, timezone
from sqlmodel import SQLModel, Field, UniqueConstraint
from sqlalchemy.sql import func


class TenantEmissionsFactor(SQLModel, table=True):
    __tablename__ = "tenant_emissions_factor"
    __table_args__ = (
        UniqueConstraint(
            "activity_name",
            "reference_product",
            "geography",
            "source",
            name="uq_emissions_factors_composite"
        ),
    )
    id: int = Field(primary_key=True)
    activity_name: str = Field(max_length=250)
    geography: str = Field(max_length=50)
    source: str = Field(max_length=120)
    activity_type: str = Field(max_length=50, nullable=False)
    reference_product: str = Field(max_length=250, nullable=False)
    reference_product_amount: float = Field(nullable=False)
    unit: str = Field(max_length=20, nullable=False)
    activity_description: str = Field(max_length=1000, nullable=False)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    class Config:
        arbitrary_types_allowed = True
        fields = {
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }
