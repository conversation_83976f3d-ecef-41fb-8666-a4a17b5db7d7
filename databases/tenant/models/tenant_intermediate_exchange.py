from typing import Optional
from datetime import datetime, timezone
from pydantic import BaseModel
from sqlmodel import (
    SQLModel,
    Field,
    Column,
    ForeignKey,
    Integer,
    Relationship
)
from sqlalchemy.sql import func
from .tenant_emissions_factor import TenantEmissionsFactor


class TenantIntermediateExchange(SQLModel, table=True):
    __tablename__ = "tenant_intermediate_exchange"
    id: int = Field(primary_key=True)
    exchange_name: str = Field(max_length=120)
    amount: float
    input_stream: bool
    unit: str = Field(max_length=64)
    exchange_emissions_factor_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "tenant_emissions_factor.id",
                ondelete="CASCADE",
            ),
            nullable=True
        )
    )
    parent_emissions_factor_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "tenant_emissions_factor.id",
                ondelete="CASCADE",
            ),
            nullable=True,
        )
    )
    exchange_emissions_factor: Optional[TenantEmissionsFactor] = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[TenantIntermediateExchange.exchange_emissions_factor_id]",
        }
    )
    parent_emissions_factor: Optional[TenantEmissionsFactor] = Relationship(
        sa_relationship_kwargs={
            "foreign_keys": "[TenantIntermediateExchange.parent_emissions_factor_id]",
        }
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    class Config:
        orm_mode = True
        arbitrary_types_allowed = True
        fields = {
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }

class TenantIntermediateExchangeRead(BaseModel):
    id: int = Field(primary_key=True)
    exchange_name: str = Field(max_length=120)
    amount: float
    input_stream: bool
    unit: str = Field(max_length=64)
    exchange_emissions_factor: Optional[TenantEmissionsFactor]
    parent_emissions_factor: Optional[TenantEmissionsFactor]
