from sqlmodel import (
    Field,
    SQLModel,
    Relationship,
)
from .address import Address


class Supplier(SQLModel, table=True):
    __tablename__ = "supplier"
    id: int = Field(primary_key=True)
    supplier_level: int
    supplier_type: str = Field(max_length=250, nullable=False)
    supplier_name: str | None = Field(max_length=250, nullable=True)
    tax_registry_number: str | None = Field(max_length=11, nullable=True)
    primary_contact_name: str | None = Field(max_length=250, nullable=True)
    primary_contact_email: str | None = Field(max_length=100, nullable=True)
    primary_address_id: int = Field(default=None, foreign_key="address.id")
    primary_address: Address = Relationship()
    website: str | None = Field(max_length=250, nullable=True)
