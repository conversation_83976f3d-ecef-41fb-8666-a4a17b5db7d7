from datetime import datetime, timezone
from pydantic import BaseModel
from sqlmodel import (
    SQLModel,
    Field,
    Column,
    Relationship,
    String,
    Integer,
    ForeignKey,
)
from sqlalchemy.sql import func
from databases.tenant.models.tenant_emissions_factor import TenantEmissionsFactor
from databases.tenant.models.address import Address
from databases.tenant.models.supplier import Supplier


class Node(SQLModel, table=True):
    """Process model graph node."""
    __tablename__ = "node"

    id: int = Field(primary_key=True)
    product_id: str = Field(
        sa_column=Column(
            String(255),
            ForeignKey(
                "product.product_id",
                ondelete="CASCADE",
                onupdate="CASCADE"
            ),
        ),
    )
    component_id: str | None = Field(
        sa_column=Column(
            String(255),
            ForeignKey(
                "product.product_id",
                ondelete="SET NULL",
                onupdate="CASCADE"
            ),
            nullable=True
        ),
    )
    component_version: str | None = Field(
        sa_column=Column(
            String(255),
            nullable=True
        )
    )
    name: str = Field(nullable=False)
    amount: float | None = Field(nullable=True)
    unit: str = Field(default="kg")
    quantity: int = Field(nullable=False, default=1)
    node_type: str = Field(index=True, nullable=False, max_length=120)
    location_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "address.id",
                ondelete="CASCADE",
                onupdate="CASCADE"
            ),
            nullable=True
        )
    )
    location: Address | None = Relationship()
    emissions_factor_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "tenant_emissions_factor.id",
                ondelete="SET NULL",
                onupdate="CASCADE"
            ),
            nullable=True
        )
    )
    emissions_factor: TenantEmissionsFactor | None = Relationship()
    supplier_id: int | None = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "supplier.id",
                ondelete="SET NULL",
                onupdate="CASCADE"
            ),
            nullable=True
        )
    )
    supplier: Supplier | None = Relationship()

    # Material Node fields
    recycled_content_rate: float = Field(default=0)
    disposed_weight_kg: float | None = Field(nullable=True)

    mass_allocation_per_kg: bool = Field(default=False)
    eol_recycling_rate: float = Field(default=0.0)
    eol_landfill_rate: float = Field(default=0.0)
    eol_incineration_rate: float = Field(default=0.0)
    eol_composting_rate: float = Field(default=0.0)

    # Packaging Node fields
    component_name: str | None = Field(nullable=True)
    packaging_level: str | None = Field(nullable=True)
    description: str | None = Field(nullable=True)

    # Transportation Node fields
    segment_type: str | None = Field(nullable=True)
    transport_mode: str | None = Field(nullable=True)

    # Production Node fields
    scrap_rate: float | None = Field(nullable=True)
    scrap_fate: str | None = Field(nullable=True)
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    class Config:
        arbitrary_types_allowed = True
        fields = {
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }

    def __hash__(self):
        return hash((self.product_id, self.id))

    def __eq__(self, other):
        return self.id == other.id and self.product_id == other.product_id

    def clone(self) -> "Node":
        return Node(
            **self.dict(),
            location=Address(**self.location.dict()) if self.location else None,
            emissions_factor=(
                TenantEmissionsFactor(**self.emissions_factor.dict())
                if self.emissions_factor
                else None
            ),
            supplier=Supplier(**self.supplier.dict()) if self.supplier else None
        )


class NodeRead(BaseModel):
    id: int
    product_id: str
    component_id: str | None
    component_version: str | None
    name: str
    node_type: str
    amount: float | None
    unit: str
    quantity: int
    location: Address | None
    emissions_factor: TenantEmissionsFactor | None
    supplier: Supplier | None

    # Material Node fields
    recycled_content_rate: float | None
    disposed_weight_kg: float | None
    mass_allocation_per_kg: bool | None
    eol_recycling_rate: float | None
    eol_landfill_rate: float | None
    eol_incineration_rate: float | None
    eol_composting_rate: float | None

    # Packaging Node fields
    component_name: str | None
    packaging_level: str | None
    description: str | None

    # Transportation Node fields
    segment_type: str | None
    transport_mode: str | None

    # Production Node fields
    scrap_rate: float | None
    scrap_fate: str | None

    def clone(self) -> "NodeRead":
        return NodeRead(
            **self.dict(exclude={"location": True, "emissions_factor": True, "supplier": True}),
            location=Address(**self.location.dict()) if self.location else None,
            emissions_factor=(
                TenantEmissionsFactor(**self.emissions_factor.dict())
                if self.emissions_factor
                else None
            ),
            supplier=Supplier(**self.supplier.dict()) if self.supplier else None
        )
