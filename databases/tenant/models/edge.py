from typing import Optional
from datetime import datetime, timezone
from sqlmodel import (
    SQLModel,
    Field,
    Column,
    ForeignKey,
    Integer,
    String,
    Relationship,
    PrimaryKeyConstraint,
)
from sqlalchemy.sql import func
from .node import Node


class Edge(SQLModel, table=True):
    __tablename__ = "edge"
    __table_args__ = (
        PrimaryKeyConstraint(
            "to_node_id",
            "from_node_id",
            name="edge_primary_key",
        ),
    )
    product_id: str = Field(
        sa_column=Column(
            String,
            ForeignKey(
                "product.product_id",
                ondelete="CASCADE",
                onupdate="CASCADE"
            ),
            nullable=False
        )
    )
    to_node_id: int = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "node.id",
                ondelete="CASCADE",
            ),
            nullable=False
        )
    )
    from_node_id: int = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "node.id",
                ondelete="CASCADE",
            ),
            nullable=False
        )
    )
    from_node: Optional[Node] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Edge.from_node_id]"},
    )
    to_node: Optional[Node] = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[Edge.to_node_id]"},
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    def __eq__(self, other: "Edge"):
        return (
            self.product_id,
            self.from_node_id,
            self.to_node_id
        ) == (other.product_id, other.from_node_id, other.to_node_id)

    def __hash__(self):
        return hash((self.product_id, self.from_node_id, self.to_node_id))

    def clone(self) -> "Edge":
        return Edge(**self.dict())

    class Config:
        arbitrary_types_allowed = True
        fields = {
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }
