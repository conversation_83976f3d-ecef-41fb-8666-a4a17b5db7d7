from sqlmodel import (
    SQLModel,
    Field,
    UniqueConstraint
)

class TenantImpactIndicator(SQLModel, table=True):
    __tablename__ = "tenant_impact_indicator"
    __table_args__ = (
        UniqueConstraint(
            "lcia_method",
            "category",
            "indicator",
            name="uq_tenant_impact_indicator",
        ),
    )
    id: int = Field(primary_key=True)
    lcia_method: str = Field(max_length=255)
    category: str = Field(max_length=255)
    indicator: str = Field(max_length=255)
    unit: str = Field(max_length=255)
    description: str = Field(max_length=255)
