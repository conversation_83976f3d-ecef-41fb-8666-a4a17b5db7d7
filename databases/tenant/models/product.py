from typing import Optional, Dict, Any
from datetime import datetime, timezone
import uuid
import pickle
from lz4.frame import decompress, compress
from pydantic import BaseModel
import sqlalchemy as sa
from sqlalchemy.sql import func
from sqlmodel import (
    SQLModel,
    Field,
    UniqueConstraint,
    Integer,
    BigInteger,
    ForeignKey,
    Column,
    Relationship,
)
from .supplier import Supplier


# pylint: disable=too-many-ancestors
class CONST:
    class DEFAULTS:
        # pylint: disable=line-too-long
        product_image = "https://static.thenounproject.com/png/6878332-200.png"

        class FactoryLocale(BaseModel):
            city: str | None = "London"
            country: str = "United Kingdom"
            code: str = "GBR"

class Product(SQLModel, table=True):
    __tablename__ = "product"
    __table_args__ = (
        UniqueConstraint(
            "cloned_from_product_id",
            name="cloned_from_product_id_unique_key",
        ),
    )
    product_id: str = Field(
        primary_key=True,
        index=True,
        nullable=False,
    )
    product_name: str
    product_type: str
    version: str = Field(default_factory=lambda: str(uuid.uuid4()))
    cloned_from_product_id: Optional[str] = Field(
        default=None,
        index=True,
        nullable=True,
    )
    supplier_id: Optional[int] = Field(
        sa_column=Column(
            Integer(),
            ForeignKey(
                "supplier.id",
            ),
            nullable=True,
        )
    )
    status: str = Field(nullable=False, default="inactive")
    supplier: Optional[Supplier] = Relationship()
    brand: str | None = None
    content_weight: Optional[float] = None
    total_weight: Optional[float] = None
    uses_per_package: Optional[float] = None
    weight_unit: Optional[str] = None
    primary_category: Optional[str] = None
    product_image: Optional[str] = None
    back_of_pack_image: Optional[str] = None
    country_of_use: str | None = None
    storage: Optional[str] = None
    package_type: Optional[str] = None
    functional_type: Optional[str] = None
    functional_unit: Optional[str] = None
    recycling_information: Optional[str] = Field(nullable=True, max_length=1000)
    other_information: Optional[str] = Field(nullable=True, max_length=3000)
    manufacturer: Optional[str] = Field(nullable=True, max_length=1000)
    reusable_packaging: Optional[bool] = None
    number_of_reuses: Optional[int] = Field(nullable=True, default=None)
    annual_sales_volume_units: Optional[int] = Field(
        nullable=True, default=None, sa_column=Column(BigInteger())
    )
    factory_locale: Optional[dict] = Field(
        sa_column=sa.Column(sa.JSON),
        default=CONST.DEFAULTS.FactoryLocale().dict(),
        nullable=True,
    )
    tags: Optional[str] = Field(
        sa_column=sa.Column(sa.Text),
        default=None,
    )
    lca_output: Optional[bytes] = Field(
        sa_column=Column(sa.LargeBinary(length=16 * 1024 * 1024 - 1)), # Mediumblob
        default=None,
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now()}
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        sa_column_kwargs={"server_default": func.now(), "onupdate": func.now()}
    )

    def set_lca_output_dict(self, value: Optional[Dict[str, Any]]) -> None:
        """Serialize the dictionary to binary data."""
        self.lca_output = compress(pickle.dumps(value)) if value else None

    def get_lca_output_dict(self) -> Optional[Dict[str, Any]]:
        """Deserialize the binary data to a dictionary."""
        if self.lca_output:
            return pickle.loads(decompress(self.lca_output))
        return None

    class Config:
        arbitrary_types_allowed = True
        fields = {
            'lca_output': {'exclude': True},
            'created_at': {'exclude': True},
            'updated_at': {'exclude': True},
        }
