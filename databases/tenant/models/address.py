from sqlmodel import (
    Field,
    SQLModel,
)


class Address(SQLModel, table=True):
    __tablename__ = "address"
    id: int = Field(primary_key=True)
    address_1: str | None = Field(max_length=250, nullable=True)
    address_2: str | None = Field(max_length=250, nullable=True)
    latitude: float | None = Field(nullable=True)
    longitude: float | None = Field(nullable=True)
    city: str | None = Field(max_length=200, nullable=True)
    state_or_province: str | None = Field(max_length=100, nullable=True)
    postal_code: str | None = Field(max_length=5, nullable=True)
    country: str = Field(max_length=100, nullable=False)
