from typing import List
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from databases.engine import engine_pool
from databases.tenant.models import Edge
from exceptions import DatabaseError


class EdgeRepo:

    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get(self, edge_id: int) -> Edge | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Edge)
                    .options(
                        selectinload(Edge.to_node),
                        selectinload(Edge.from_node)
                    )
                    .where(Edge.id == edge_id)
                )
                edge = session.exec(statement).one_or_none()

                return edge
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def create(self, edge: Edge) -> Edge:
        with Session(self.engine) as session:
            try:
                session.add(edge)
                session.commit()
                session.refresh(edge)

                return edge
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def delete(self, edge: Edge) -> Edge:
        with Session(self.engine) as session:
            try:
                session.delete(edge)
                session.commit()

                return edge
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def get_all(self) -> List[Edge]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Edge)
                    .options(
                        selectinload(Edge.to_node),
                        selectinload(Edge.from_node)
                    )
                )
                edges = session.exec(statement).all()

                return edges
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all_by_product_id(self, product_id: int) -> List[Edge]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Edge)
                    .options(
                        selectinload(Edge.to_node),
                        selectinload(Edge.from_node)
                    )
                    .where(Edge.product_id == product_id)
                )
                edges = session.exec(statement).all()

                return edges
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all_to_nodes(self, node_id: int) -> List[Edge]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Edge)
                    .options(
                        selectinload(Edge.to_node),
                        selectinload(Edge.from_node)
                    )
                    .where(Edge.to_node_id == node_id)
                )
                edges = session.exec(statement).all()

                return edges
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all_from_nodes(self, node_id: int) -> List[Edge]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Edge)
                    .options(
                        selectinload(Edge.to_node),
                        selectinload(Edge.from_node)
                    )
                    .where(Edge.from_node_id == node_id)
                )
                edges = session.exec(statement).all()
                return edges
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error
