from sqlmodel import Session, and_, select, update
from databases.engine import engine_pool
from databases.tenant.models import ProductAttributes, ProductAttributeDef
from exceptions import DatabaseError
from utils.logger import logger_instance

logger = logger_instance.get_logger()


class ProductAttributesRepo:
    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get_attribute_defs(self) -> list[ProductAttributeDef] | None:
        with Session(self.engine) as session:
            try:
                attributes = session.query(ProductAttributeDef).all()
                return attributes
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_attribute_def_by_key(self, key: str) -> ProductAttributeDef | None:
        with Session(self.engine) as session:
            try:
                attribute = (
                    session.query(ProductAttributeDef)
                    .filter(ProductAttributeDef.key == key)
                    .one_or_none()
                )
                return attribute
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_attribute_def_by_id(self, attribute_id: str) -> ProductAttributeDef | None:
        with Session(self.engine) as session:
            try:
                attribute = session.get(ProductAttributeDef, attribute_id)
                return attribute
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product_attribute_by_id(
        self, product_id: str, attribute_def_id: int
    ) -> tuple[ProductAttributes, ProductAttributeDef] | None:
        with Session(self.engine) as session:
            try:
                attribute = (
                    session.query(ProductAttributes, ProductAttributeDef)
                    .join(
                        ProductAttributeDef,
                        ProductAttributes.attribute_def_id == ProductAttributeDef.id,
                    )
                    .filter(ProductAttributes.product_id == product_id)
                    .filter(ProductAttributes.attribute_def_id == attribute_def_id)
                    .one_or_none()
                )
                print(attribute)
                return attribute
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product_attributes(
        self, product_id: str | None = None
    ) -> list[tuple[ProductAttributes, ProductAttributeDef]] | None:
        with Session(self.engine) as session:
            try:
                attributes_with_def = select(
                    ProductAttributes, ProductAttributeDef
                ).join(
                    ProductAttributeDef,
                    ProductAttributes.attribute_def_id == ProductAttributeDef.id,
                )
                if product_id:
                    attributes_with_def = attributes_with_def.where(
                        ProductAttributes.product_id == product_id
                    )

                return session.exec(attributes_with_def).all()
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def create_attribute_def(
        self, product_attribute_def: ProductAttributeDef
    ) -> ProductAttributeDef | None:
        with Session(self.engine) as session:
            try:
                session.add(product_attribute_def)
                session.commit()
                session.refresh(product_attribute_def)
                return product_attribute_def
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def upsert_attribute_def(
        self, product_attribute_def: ProductAttributeDef
    ) -> ProductAttributeDef | None:
        with Session(self.engine) as session:
            try:
                if product_attribute_def.id is not None:
                    existing_attribute = session.get(
                        ProductAttributeDef, product_attribute_def.id
                    )
                    if existing_attribute:
                        for key, value in product_attribute_def.dict(
                            exclude_unset=True
                        ).items():
                            setattr(existing_attribute, key, value)
                        session.commit()
                        session.refresh(existing_attribute)
                        return existing_attribute

                session.add(product_attribute_def)
                session.commit()
                session.refresh(product_attribute_def)
                return product_attribute_def
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def update_product_id(
        self,
        product_id: str,
        new_product_id: str
    ) -> ProductAttributes | None:
        with Session(self.engine) as session:
            try:
                session.execute(
                    update(ProductAttributes)
                    .where(ProductAttributes.product_id == product_id)
                    .values({"product_id": new_product_id})
                )
                session.commit()
                return

            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def upsert_product_attribute(
        self, product_attribute: ProductAttributes
    ) -> ProductAttributes | None:
        with Session(self.engine) as session:
            try:
                existing_attribute = (
                    session.query(ProductAttributes)
                    .filter(
                        and_(
                            ProductAttributes.product_id
                            == product_attribute.product_id,
                            ProductAttributes.attribute_def_id
                            == product_attribute.attribute_def_id,
                        )
                    )
                    .one_or_none()
                )

                if existing_attribute:
                    for key, value in product_attribute.dict(
                        exclude_unset=True
                    ).items():
                        setattr(existing_attribute, key, value)
                else:
                    session.add(product_attribute)
                    existing_attribute = product_attribute

                session.commit()
                session.refresh(existing_attribute)
                return existing_attribute
            except Exception as error:
                print(error)
                session.rollback()
                raise DatabaseError("SQL Error", error) from error
