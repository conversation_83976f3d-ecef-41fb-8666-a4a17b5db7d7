from typing import List
import json
from sqlmodel import update, select
from databases.engine import engine_pool
from databases.tenant.constants.product import ProductStatus
from databases.tenant.models.product import CONST
from databases.tenant.models import Product, Node
from databases.shared.constants.node_type import NodeTypes
from pipelines_v2.utils.unit import UnitType
from utils.logger import logger_instance
from utils.geocoding import get_country_info
from exceptions import DatabaseError
from . import query, Session


logger = logger_instance.get_logger()


class ProductRepo:
    def __init__(self, tenant_id: str) -> None:
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get_products(
        self,
        product_type: str | None = None,
        status_list: List[ProductStatus] = (ProductStatus.ACTIVE,)
    ) -> List[Product]:
        with Session(self.engine) as session:
            try:
                statement = select(Product)
                statement = statement.where(Product.status.in_([s.value for s in status_list]))

                if product_type:
                    statement = statement.where(Product.product_type == product_type)

                products = session.exec(statement).all()
                return products
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product(self, product_id: str) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.get(Product, product_id)
                return product
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product_by_name(self, product_name: str) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.exec(
                    select(Product).where(Product.product_name == product_name)
                ).one_or_none()
                return product
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_product_clones(self, product_id: str) -> List[Product]:
        with Session(self.engine) as session:
            try:
                clones = session.exec(
                    select(Product).where(Product.cloned_from_product_id == product_id)
                ).all()
                return clones
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def update_product_status(
        self, product_id: str, status: ProductStatus
    ) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.get(Product, product_id)
                product.status = status.value
                session.commit()
                session.refresh(product)
                return product
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def update_product_factory_location(
        self,
        product_id: str,
        city: str | None,
        country: str,
    ) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.get(Product, product_id)
                if not product:
                    return None

                country_info = get_country_info(country)
                factory_locale = CONST.DEFAULTS.FactoryLocale(
                    city=(
                        city
                        if city
                        else None
                    ),
                    country=country_info.name,
                    code=country_info.alpha_3_code,
                )
                product.factory_locale = factory_locale.dict()
                session.commit()
                session.refresh(product)
                return product
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def update_product_lca_output(self, product_id: str, lca_output: dict | None) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.get(Product, product_id)
                if not product:
                    return None

                product.set_lca_output_dict(lca_output)
                session.commit()
                session.refresh(product)
                return product
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def aggregate_total_weight(self, product_id: str) -> float | None:
        with Session(self.engine) as session:
            try:
                packaging_nodes = session.exec(
                    select(Node)
                    .where(Node.product_id == product_id)
                    .where(Node.node_type == NodeTypes.PACKAGING.value)
                ).all()

                packaging_weight = sum(
                    UnitType.convert_from_abbrev(
                        node.amount,
                        node.unit,
                        "g",
                    ) * node.quantity
                    for node in packaging_nodes
                )

                material_nodes = session.exec(
                    select(Node)
                    .where(Node.product_id == product_id)
                    .where(Node.node_type == NodeTypes.MATERIAL.value)
                ).all()

                material_weight = sum(
                    UnitType.convert_from_abbrev(
                        node.amount,
                        node.unit,
                        "g",
                    ) * node.quantity
                    for node in material_nodes
                )

                total_weight = packaging_weight + material_weight

                session.execute(
                    f"""
                    UPDATE {Product.__tablename__}
                    SET total_weight = :total_weight,
                        content_weight = :content_weight
                    WHERE product_id = :product_id;
                    """,
                    {
                        "product_id": product_id,
                        "total_weight": total_weight,
                        "content_weight": material_weight
                    },
                )
                session.commit()
                return total_weight
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def create_product(self, product: Product) -> Product | None:
        with Session(self.engine) as session:
            try:
                session.add(product)
                session.commit()
                session.refresh(product)
                return product
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def delete_product(self, product_id: str) -> None:
        with Session(self.engine) as session:
            try:
                session.execute(
                    f"""
                    DELETE FROM {Product.__tablename__}
                    WHERE product_id = :product_id
                    OR cloned_from_product_id = :product_id;
                    """,
                    {"product_id": product_id},
                )
                session.commit()
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def update_product_tags(self, product_id: str, tags: List[str]) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.get(Product, product_id)
                if not product:
                    return None

                product.tags = ",".join(tags) if tags else None
                session.commit()
                session.refresh(product)
                return product
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def rename_product(
        self, product_id: str, new_product_id: str, new_product_name: str
    ) -> Product | None:
        with Session(self.engine) as session:
            try:
                product = session.get(Product, product_id)
                if not product:
                    return None

                product.product_id = new_product_id
                product.product_name = new_product_name

                session.execute(
                    f"""
                    UPDATE {Product.__tablename__}
                    SET cloned_from_product_id = :new_product_id
                    WHERE cloned_from_product_id = :product_id;
                    """,
                    {"product_id": product_id, "new_product_id": new_product_id},
                )

                session.commit()
                session.refresh(product)
                return product
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

def fetch_products_by_brand(
    tenant_id: str,
    brand: str,
) -> Product | None:
    engine = engine_pool.get_tenant_engine(tenant_id)
    try:
        product = query(
            "SELECT * FROM product WHERE brand = :brand;",
            {"brand": brand},
            engine=engine,
        )

        if not product:
            return None

        return product

    except Exception as error:
        raise DatabaseError("SQL Error", error) from error


def fetch_product_clone(
    tenant_id: str,
    product_id: str,
) -> Product | None:
    engine = engine_pool.get_tenant_engine(tenant_id)
    try:
        product = query(
            "SELECT * FROM product WHERE cloned_from_product_id = :product_id;",
            {"product_id": product_id},
            engine=engine,
        )

        if not product:
            return None

        return Product(**product[0])
    except Exception as error:
        raise DatabaseError("SQL Error", error) from error


def delete_all_products_except_clones(tenant_id: str):
    engine = engine_pool.get_tenant_engine(tenant_id)
    with Session(engine) as session:
        try:
            session.execute("DELETE FROM product WHERE cloned_from_product_id IS NULL")
            session.commit()
        except Exception as error:
            session.rollback()
            raise DatabaseError("SQL Error", error) from error


def fetch_products_by_tenant_id(tenant_id: str) -> List[Product]:
    engine = engine_pool.get_tenant_engine(tenant_id)
    try:
        products = query(query_str="SELECT * FROM product", params={}, engine=engine)
        if not products:
            return []
        return products

    except Exception as error:
        raise DatabaseError("SQL Error", error) from error


def fetch_factory_locale_by_product_name(
    tenant_id: str,
    product_name: str,
) -> CONST.DEFAULTS.FactoryLocale | None:
    engine = engine_pool.get_tenant_engine(tenant_id)
    try:
        product_factory_locale = query(
            (
                "SELECT factory_locale "
                "FROM product "
                "WHERE product_name = :product_name;"
            ),
            {"product_name": product_name},
            engine=engine,
        )

        if not product_factory_locale:
            return None

        product_factory_locale = json.loads(product_factory_locale[0][0])

        return CONST.DEFAULTS.FactoryLocale(**product_factory_locale)

    except Exception as error:
        raise DatabaseError("SQL Error", error) from error


def fetch_product_by_name(
    tenant_id: str,
    product_name: str,
) -> Product | None:
    engine = engine_pool.get_tenant_engine(tenant_id)
    try:
        product = query(
            "SELECT * FROM product WHERE product_name = :product_name;",
            {"product_name": product_name},
            engine=engine,
        )

        if not product:
            return None

        product_dict = dict(product[0])

        if isinstance(product_dict["factory_locale"], str):
            product_dict["factory_locale"] = json.loads(product_dict["factory_locale"])

        return Product(**product_dict)

    except Exception as error:
        raise DatabaseError("SQL Error", error) from error


def create_product(tenant_id: str, product: Product) -> Product | None:
    engine = engine_pool.get_tenant_engine(tenant_id)
    with Session(engine) as session:
        try:
            session.add(product)
            session.commit()
            session.refresh(product)
            return product
        except Exception as error:
            session.rollback()
            raise DatabaseError("SQL Error", error) from error


def update_product(
    tenant_id: str,
    product_id: str,
    fields: list,
    values: list,
):
    engine = engine_pool.get_tenant_engine(tenant_id)
    with Session(engine) as session:
        try:
            session.execute(
                update(Product)
                .where(Product.product_id == product_id)
                .values(dict(zip(fields, values)))
            )
            session.commit()
        except Exception as error:
            session.rollback()
            raise DatabaseError("SQL Error", error) from error


def update_product_supplier_id(
    tenant_id: str,
    product_id: str,
    supplier_id: int,
):
    engine = engine_pool.get_tenant_engine(tenant_id)
    with Session(engine) as session:
        try:
            session.execute(
                update(Product)
                .where(Product.product_id == product_id)
                .values({"supplier_id": supplier_id})
            )
            session.commit()
        except Exception as error:
            session.rollback()
            raise DatabaseError("SQL Error", error) from error
