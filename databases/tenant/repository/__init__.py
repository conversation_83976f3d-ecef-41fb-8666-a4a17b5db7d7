from typing import List, Any
from sqlmodel import Session


def query(query_str: str, params: dict, engine: Any) -> List[Any] | None:
    try:
        with Session(engine) as session:
            result = session.execute(query_str, params=params)
            return result.all()
    except Exception as error:
        raise error

#pylint: disable=wrong-import-position,import-self,cyclic-import
from .product import *
from .supplier import SupplierRepo
from .product_attributes import ProductAttributesRepo
from .tenant_emissions_factor import TenantEmissionsFactorRepo
from .tenant_impact_indicator import TenantImpactIndicatorRepo
from .tenant_emissions_factor_value import TenantEmissionsFactorValueRepo
from .tenant_intermediate_exchange import TenantIntermediateExchangeRepo
from .node import NodeRepo
from .edge import EdgeRepo
from .prediction_override import PredictionOverrideRepo
