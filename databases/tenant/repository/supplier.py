from sqlmodel import Session, select
from sqlalchemy.sql.operators import is_
from sqlalchemy.orm import selectinload
from databases.engine import engine_pool
from databases.tenant.models import Supplier, Address
from exceptions import DatabaseError


class SupplierRepo:
    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get_supplier(self, supplier_id: int) -> Supplier | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Supplier)
                    .join(Supplier.primary_address)
                    .options(selectinload(Supplier.primary_address))
                    .where(Supplier.id == supplier_id)
                )

                result = session.exec(statement).first()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_supplier_by_info(
        self,
        supplier_type: str,
        supplier_level: int,
        supplier_name: str | None = None,
    ) -> Supplier | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Supplier)
                    .join(Supplier.primary_address)
                    .options(selectinload(Supplier.primary_address))
                    .where(Supplier.supplier_level == supplier_level)
                    .where(Supplier.supplier_type == supplier_type)
                )

                if supplier_name:
                    statement = statement.where(Supplier.supplier_name == supplier_name)
                else:
                    statement = statement.where(is_(Supplier.supplier_name, None))

                result = session.execute(statement).scalar_one_or_none()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_suppliers(
        self,
        supplier_type: str | None=None,
        supplier_level: str | None=None,
    ) -> list[Supplier]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Supplier)
                    .join(Supplier.primary_address)
                    .options(selectinload(Supplier.primary_address))
                )

                if supplier_type:
                    statement = (
                        statement
                        .where(Supplier.supplier_type == supplier_type)
                    )

                if supplier_level:
                    statement = (
                        statement
                        .where(Supplier.supplier_level == supplier_level)
                    )

                results = session.exec(statement).all()
                return results
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_suppliers_by_type(self, supplier_type: str) -> list[Supplier]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Supplier)
                    .join(Supplier.primary_address)
                    .options(selectinload(Supplier.primary_address))
                    .where(Supplier.supplier_type == supplier_type)
                )

                results = session.exec(statement).all()
                return results
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def upsert_supplier(self, supplier: Supplier, supplier_address: Address):
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Supplier)
                    .where(Supplier.supplier_type == supplier.supplier_type)
                    .where(Supplier.supplier_level == supplier.supplier_level)
                    .where(Supplier.supplier_name == supplier.supplier_name)
                )

                existing_supplier = session.execute(statement).scalar_one_or_none()

                if existing_supplier:
                    address_statement = (
                        select(Address)
                        .where(Address.id == existing_supplier.primary_address_id)
                    )

                    existing_supplier_address = (
                        session
                        .execute(address_statement)
                        .scalar_one_or_none()
                    )

                    for key, value in supplier_address.dict(exclude_unset=True).items():
                        setattr(existing_supplier_address, key, value)

                    supplier.primary_address_id = existing_supplier_address.id
                    for key, value in supplier.dict(exclude_unset=True).items():
                        setattr(existing_supplier, key, value)

                    session.flush()
                    return existing_supplier

                session.add(supplier_address)
                session.commit()
                session.refresh(supplier_address)

                supplier.primary_address_id = supplier_address.id
                session.add(supplier)
                session.commit()
                session.refresh(supplier)
                return supplier
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def create_supplier(self, supplier: Supplier, supplier_address: Address):
        with Session(self.engine) as session:
            try:
                session.add(supplier_address)
                session.commit()
                session.refresh(supplier_address)

                supplier.primary_address_id = supplier_address.id
                session.add(supplier)
                session.commit()
                session.refresh(supplier)

                return self.get_supplier(supplier.id)
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error
