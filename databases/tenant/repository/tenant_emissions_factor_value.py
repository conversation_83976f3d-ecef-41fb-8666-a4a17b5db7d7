from typing import List, Optional
from sqlalchemy.orm import selectinload
from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.tenant.models import (
  TenantEmissionsFactorValue,
  TenantEmissionsFactorValueRead
)
from exceptions import DatabaseError


def move_model_to_read(value: TenantEmissionsFactorValue) -> TenantEmissionsFactorValueRead:
    return TenantEmissionsFactorValueRead(
        emissions_factor=value.emissions_factor.dict(),
        impact_indicator=value.impact_indicator.dict(),
        amount=value.amount,
    )

class TenantEmissionsFactorValueRepo:
    def __init__(self, tenant_id: str) -> None:
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def add_emissions_factor_value(
        self,
        emissions_factor_value: TenantEmissionsFactorValue,
    ) -> None:
        with Session(self.engine) as session:
            try:
                session.add(emissions_factor_value)
                session.commit()
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_emissions_factor_values(
        self,
        emissions_factor_id: int,
        impact_indicator_ids: Optional[List[int]] = None,
    ) -> List[TenantEmissionsFactorValueRead]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(TenantEmissionsFactorValue)
                    .options(
                        selectinload(TenantEmissionsFactorValue.impact_indicator),
                    )
                    .where(TenantEmissionsFactorValue.emissions_factor_id == emissions_factor_id)
                )

                if impact_indicator_ids is not None:
                    statement = statement.where(
                        TenantEmissionsFactorValue.impact_indicator_id.in_(impact_indicator_ids)
                    )

                values = session.exec(statement).all()
                return [
                    move_model_to_read(value)
                    for value in values
                ]
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error
