from typing import List
from sqlmodel import Session, select
from sqlalchemy.orm import aliased, selectinload

from databases.engine import engine_pool
from databases.tenant.models import (
    TenantEmissionsFactor,
    TenantIntermediateExchange,
    TenantIntermediateExchangeRead,
)
from exceptions import DatabaseError


def move_model_to_read(exchange: TenantIntermediateExchange) -> TenantIntermediateExchangeRead:
    return TenantIntermediateExchangeRead(
        **exchange.dict(
            exclude={
                "parent_emissions_factor_id",
                "exchange_emissions_factor_id",
            }
        ),
        parent_emissions_factor=exchange.parent_emissions_factor.dict(),
        exchange_emissions_factor=exchange.exchange_emissions_factor.dict(),
    )


class TenantIntermediateExchangeRepo:
    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get_by_id(self, exchange_id: int) -> TenantIntermediateExchangeRead:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(TenantIntermediateExchange)
                    .options(
                        selectinload(TenantIntermediateExchange.parent_emissions_factor),
                        selectinload(
                            TenantIntermediateExchange.exchange_emissions_factor
                        ),
                    )
                    .where(TenantIntermediateExchange.id == exchange_id)
                )

                result = session.exec(statement).one_or_none()
                if not result:
                    return None

                return move_model_to_read(result)
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all_for_emissions_factor_by_id(
        self,
        emissions_factor_id: int,
    ) -> List[TenantIntermediateExchangeRead]:
        with Session(self.engine) as session:
            try:
                parent_emissions_factor = aliased(TenantEmissionsFactor)
                statement = (
                    select(TenantIntermediateExchange)
                    .options(
                        selectinload(TenantIntermediateExchange.exchange_emissions_factor),
                        selectinload(TenantIntermediateExchange.parent_emissions_factor)
                    )
                    .join(
                        parent_emissions_factor,
                        TenantIntermediateExchange.parent_emissions_factor
                    )
                    .where(parent_emissions_factor.id == emissions_factor_id)
                )

                result = session.exec(statement).all()
                return [move_model_to_read(ie) for ie in result]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all_for_emissions_factor(
        self,
        activity_name: str,
        reference_product: str,
        geography: str,
        source: str,
    ) -> List[TenantIntermediateExchangeRead]:
        with Session(self.engine) as session:
            try:
                parent_emissions_factor = aliased(TenantEmissionsFactor)
                statement = (
                    select(TenantIntermediateExchange)
                    .options(
                        selectinload(TenantIntermediateExchange.exchange_emissions_factor),
                        selectinload(TenantIntermediateExchange.parent_emissions_factor)
                    )
                    .join(
                        parent_emissions_factor,
                        TenantIntermediateExchange.parent_emissions_factor
                    )
                    .where(parent_emissions_factor.activity_name == activity_name)
                    .where(parent_emissions_factor.geography == geography)
                    .where(parent_emissions_factor.reference_product == reference_product)
                    .where(parent_emissions_factor.source == source)
                )

                result = session.exec(statement).all()
                return [move_model_to_read(ie) for ie in result]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def add_exchanges(
        self,
        exchanges: List[TenantIntermediateExchangeRead]
    ) -> List[TenantIntermediateExchangeRead]:
        tenant_exchanges = [
            TenantIntermediateExchange(**exchange.dict())
            for exchange in exchanges
        ]

        with Session(self.engine) as session:
            try:
                session.add_all(tenant_exchanges)
                session.commit()

                created_exchanges = []
                for exchange in tenant_exchanges:
                    session.refresh(exchange)
                    created_exchanges.append(move_model_to_read(exchange))

                return created_exchanges
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def delete_exchange(self, exchange_id: str) -> None:
        with Session(self.engine) as session:
            try:
                session.execute(
                    f"""
                    DELETE FROM {TenantIntermediateExchange.__tablename__}
                    WHERE id = :exchange_id
                    """,
                    {"exchange_id": exchange_id},
                )
                session.commit()
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error
