from typing import List
from sqlmodel import Session, select
from pydantic import BaseModel
from sqlalchemy.sql import func

from databases.engine import engine_pool
from databases.shared.models import EmissionsFactors
from databases.shared.models.emissions_factor_value import EmissionFactorValueRW
from databases.shared.repository.emission_factor_value import emission_factor_value
from databases.common.emissions_factor_query import build_emissions_factor_search_query
from databases.tenant.models import (
    TenantEmissionsFactor,
    TenantEmissionsFactorValue,
    TenantImpactIndicator,
)
from exceptions import DatabaseError

class EmissionFactorValueInputs(BaseModel):
    emissions_factor_id: int
    lcia_method_name: str
    category_name: str
    indicator_name: str
    unit: str
    amount: float

class ImpactValueInputs(BaseModel):
    ef_value: EmissionFactorValueRW
    total_impact_value: float
    emissions_factor_id: int

class TenantEmissionsFactorRepo:
    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)
        self.tenant_id = tenant_id

    def get_emissions_factor_by_id(self, emissions_factor_id: int) -> TenantEmissionsFactor | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(TenantEmissionsFactor)
                    .where(TenantEmissionsFactor.id == emissions_factor_id)
                )

                return session.exec(statement).one_or_none()
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_emissions_factor(
        self,
        activity_name: str,
        geography: str,
        reference_product: str,
        source: str,
    ) -> TenantEmissionsFactor | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(TenantEmissionsFactor)
                    .where(TenantEmissionsFactor.activity_name == activity_name)
                    .where(TenantEmissionsFactor.geography == geography)
                    .where(TenantEmissionsFactor.reference_product == reference_product)
                    .where(TenantEmissionsFactor.source == source)
                )

                result = session.exec(statement).one_or_none()
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all(self) -> List[TenantEmissionsFactor]:
        with Session(self.engine) as session:
            try:
                return session.exec(select(TenantEmissionsFactor)).all()
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def search_emissions_factors(
        self,
        activity_name: str,
        geography: str | None = None,
        unit: str | None = None,
        source: str | None = None,
        paging: int | None = 200,
    ) -> List[TenantEmissionsFactor]:
        with Session(self.engine) as session:
            try:
                statement = build_emissions_factor_search_query(
                    session=session,
                    model=TenantEmissionsFactor,
                    activity_name=activity_name,
                    geography=geography,
                    unit=unit,
                    source=source,
                    paging=paging
                )

                result = session.exec(statement).all()
                return [r[0] for r in result]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_custom_emissions_factors(self) -> List[TenantEmissionsFactor]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(TenantEmissionsFactor)
                    .where(func.lower(TenantEmissionsFactor.source) == func.lower(self.tenant_id))
                )

                result = session.exec(statement).all()
                print(result)
                return result
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def add_emissions_factor(
        self,
        activity_name: str,
        activity_type: str,
        reference_product: str,
        geography: str,
        source: str,
        unit: str,
        activity_description: str | None = None,
    ) -> TenantEmissionsFactor:
        tenant_emissions_factor = TenantEmissionsFactor(
            activity_name=activity_name,
            activity_type=activity_type,
            reference_product=reference_product,
            reference_product_amount=1,
            geography=geography,
            source=source,
            unit=unit,
            activity_description=activity_description or "",
        )

        with Session(self.engine) as session:
            try:
                session.add(tenant_emissions_factor)
                session.commit()
                session.refresh(tenant_emissions_factor)
                return tenant_emissions_factor
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def update_emissions_factor(
        self,
        emissions_factor_id: int,
        activity_name: str,
        activity_type: str,
        reference_product: str,
        geography: str,
        source: str,
        unit: str,
        activity_description: str | None = None,
    ) -> TenantEmissionsFactor:
        with Session(self.engine) as session:
            try:
                emissions_factor = session.get(
                    TenantEmissionsFactor,
                    emissions_factor_id,
                )

                emissions_factor.activity_name = activity_name
                emissions_factor.activity_type = activity_type
                emissions_factor.reference_product = reference_product
                emissions_factor.geography = geography
                emissions_factor.source = source
                emissions_factor.unit = unit
                if activity_description is not None:
                    emissions_factor.activity_description = activity_description

                session.add(emissions_factor)
                session.commit()
                session.refresh(emissions_factor)
                return emissions_factor
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error


    def delete_emissions_factor(self, emissions_factor_id: int) -> TenantEmissionsFactor:
        with Session(self.engine) as session:
            try:
                emissions_factor = session.get(
                    TenantEmissionsFactor,
                    emissions_factor_id,
                )
                session.delete(emissions_factor)
                session.commit()
                return emissions_factor
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def copy_shared_emissions_factor(
        self,
        emissions_factor: EmissionsFactors,
        emissions_factor_name: str | None = None,
    ) -> TenantEmissionsFactor:
        tenant_emissions_factor = TenantEmissionsFactor(
            **emissions_factor.dict(exclude={"id": True}),
        )

        if emissions_factor_name:
            tenant_emissions_factor.activity_name = emissions_factor_name

        # Use the shared repository to get values
        values = emission_factor_value.get_emissions_factor_values(emissions_factor.id)

        with Session(self.engine) as session:
            try:
                session.add(tenant_emissions_factor)
                session.flush()

                for value in values:
                    self._add_ef_value(
                        session=session,
                        emissions_factor_id=tenant_emissions_factor.id,
                        lcia_method_name=value.lcia_method,
                        category_name=value.impact_category_name,
                        indicator_name=value.impact_category_indicator,
                        unit=value.impact_category_unit,
                        amount=value.amount
                    )

                session.commit()
                session.refresh(tenant_emissions_factor)

                return tenant_emissions_factor
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def _add_ef_value(
        self,
        session: Session,
        emissions_factor_id: int,
        lcia_method_name: str,
        category_name: str,
        indicator_name: str,
        unit: str,
        amount: float,
    ) -> None:
        """
        Add tenant emissions factor value, creating necessary category and method records.

        Args:
            session: Active database session
            emissions_factor_id: ID of the emissions factor to add the value to
            lcia_method_name: Name of the LCIA method
            category_name: Name of the impact category
            indicator_name: Name of the impact indicator
            amount: Impact amount value
            unit: Optional unit for the impact category
        """
        # Check/create impact category
        statement = (
            select(TenantImpactIndicator)
            .where(TenantImpactIndicator.lcia_method == lcia_method_name)
            .where(TenantImpactIndicator.category == category_name)
            .where(TenantImpactIndicator.indicator == indicator_name)
        )
        impact_indicator = session.exec(statement).one_or_none()
        if not impact_indicator:
            impact_indicator = TenantImpactIndicator(
                lcia_method=lcia_method_name,
                category=category_name,
                indicator=indicator_name,
                unit=unit,
                description="",
            )
            session.add(impact_indicator)
            session.flush()
            session.refresh(impact_indicator)

        tenant_value = TenantEmissionsFactorValue(
            emissions_factor_id=emissions_factor_id,
            impact_indicator_id=impact_indicator.id,
            amount=amount,
        )
        session.add(tenant_value)

    def _bulk_add_ef_values(
        self,
        session: Session,
        values: List[EmissionFactorValueInputs]
    ) -> None:
        """
        Add multiple tenant emissions factor values
        """

        unique_indicators = {
            (
                v.lcia_method_name,
                v.category_name,
                v.indicator_name,
                v.unit
            )
            for v in values
        }
        existing_indicators = {}

        for lcia_method, category, indicator, unit in unique_indicators:
            statement = (
                select(TenantImpactIndicator)
                .where(TenantImpactIndicator.lcia_method == lcia_method)
                .where(TenantImpactIndicator.category == category)
                .where(TenantImpactIndicator.indicator == indicator)
            )
            result = session.exec(statement).one_or_none()
            if result:
                existing_indicators[(lcia_method, category, indicator, unit)] = result

        new_indicators = []
        for lcia_method, category, indicator, unit in unique_indicators:
            if (lcia_method, category, indicator, unit) not in existing_indicators:
                new_indicator = TenantImpactIndicator(
                    lcia_method=lcia_method,
                    category=category,
                    indicator=indicator,
                    unit=unit,
                    description="",
                )
                new_indicators.append(new_indicator)
                existing_indicators[(lcia_method, category, indicator, unit)] = new_indicator

        if new_indicators:
            session.add_all(new_indicators)
            session.flush()
            for indicator in new_indicators:
                session.refresh(indicator)

        ef_values = []
        for value in values:
            indicator_obj = existing_indicators[
                (
                    value.lcia_method_name,
                    value.category_name,
                    value.indicator_name,
                    value.unit
                )
            ]
            ef_values.append(
                TenantEmissionsFactorValue(
                    emissions_factor_id=value.emissions_factor_id,
                    impact_indicator_id=indicator_obj.id,
                    amount=value.amount,
                )
            )

        session.add_all(ef_values)

    def add_impact_value(
        self,
        ef_value: EmissionFactorValueRW,
        total_impact_value: float,
        emissions_factor_id: int
    ) -> None:
        with Session(self.engine) as session:
            try:
                self._add_ef_value(
                    session=session,
                    emissions_factor_id=emissions_factor_id,
                    lcia_method_name=ef_value.lcia_method,
                    category_name=ef_value.impact_category_name,
                    indicator_name=ef_value.impact_category_indicator,
                    unit=ef_value.impact_category_unit,
                    amount=total_impact_value
                )
                session.commit()
            except Exception as error:
                session.rollback()
                raise DatabaseError("Error adding impact value", error) from error

    def bulk_replace_ef_values(
        self,
        emissions_factor_id: int,
        impact_values: List[ImpactValueInputs]
    ) -> None:
        """Replace all emission factor values for a given tenant emission factor with new values.

        This method performs a bulk delete of existing values followed by a bulk
        insert of new values in a single transaction to ensure atomicity.

        Args:
            emissions_factor_id: ID of the tenant emission factor to replace values for
            impact_values: List of impact values to add

        Raises:
            DatabaseError: If there is an error during delete or insert operations
        """
        with Session(self.engine) as session:
            try:
                # Delete existing values for this emission factor
                session.query(TenantEmissionsFactorValue).filter(
                    TenantEmissionsFactorValue.emissions_factor_id == emissions_factor_id
                ).delete()

                # Prepare and add new values
                values = [
                    EmissionFactorValueInputs(
                        emissions_factor_id=emissions_factor_id,
                        lcia_method_name=value.ef_value.lcia_method,
                        category_name=value.ef_value.impact_category_name,
                        indicator_name=value.ef_value.impact_category_indicator,
                        unit=value.ef_value.impact_category_unit,
                        amount=value.total_impact_value
                    )
                    for value in impact_values
                ]
                self._bulk_add_ef_values(session, values)
                session.commit()
            except Exception as error:
                session.rollback()
                raise DatabaseError("Error replacing impact values", error) from error
