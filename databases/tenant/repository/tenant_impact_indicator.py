from sqlmodel import Session, select
from databases.engine import engine_pool
from databases.tenant.models import TenantImpactIndicator
from exceptions import DatabaseError


class TenantImpactIndicatorRepo:
    def __init__(self, tenant_id: str) -> None:
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def add_impact_indicator(self, impact_indicator: TenantImpactIndicator) -> None:
        with Session(self.engine) as session:
            try:
                session.add(impact_indicator)
                session.commit()
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_impact_indicator(
        self,
        lcia_method: str,
        category: str,
        indicator: str | None = None
    ) -> TenantImpactIndicator:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(TenantImpactIndicator)
                    .where(TenantImpactIndicator.lcia_method == lcia_method)
                    .where(TenantImpactIndicator.category == category)
                )
                if indicator:
                    statement = statement.where(TenantImpactIndicator.indicator == indicator)
                result = session.exec(statement)
                return result.one_or_none()
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error
