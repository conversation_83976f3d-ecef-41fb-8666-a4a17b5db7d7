from sqlmodel import Session, select, update
from databases.engine import engine_pool
from databases.tenant.models import PredictionOverride
from exceptions import DatabaseError


class PredictionOverrideRepo:

    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get_prediction_override(self, api_name: str, query: str) -> PredictionOverride | None:
        try:
            with Session(self.engine) as session:
                statement = (
                    select(PredictionOverride)
                    .where(PredictionOverride.api_name == api_name)
                    .where(PredictionOverride.query == query)
                )
                return session.exec(statement).one_or_none()
        except Exception as error:
            raise DatabaseError("SQL Error", error) from error

    def create_prediction_override(
        self,
        prediction_override: PredictionOverride,
    ) -> PredictionOverride:
        try:
            with Session(self.engine) as session:
                session.add(prediction_override)
                session.commit()
                session.refresh(prediction_override)
                return prediction_override
        except Exception as error:
            raise DatabaseError("SQL Error", error) from error

    def update_prediction_override(
        self,
        prediction_override: PredictionOverride,
    ) -> None:
        try:
            with Session(self.engine) as session:
                session.execute(
                    update(PredictionOverride)
                    .where(PredictionOverride.api_name == prediction_override.api_name)
                    .where(PredictionOverride.query == prediction_override.query)
                    .values({"override_value": prediction_override.override_value})
                )
                session.commit()
        except Exception as error:
            raise DatabaseError("SQL Error", error) from error
