from typing import List
from sqlmodel import Session, select
from sqlalchemy.orm import selectinload
from databases.engine import engine_pool
from databases.tenant.models import Node, NodeRead, Address
from exceptions import DatabaseError


def move_node_to_read(node: Node) -> NodeRead:
    return NodeRead(
        **node.dict(),
        emissions_factor=node.emissions_factor.dict() if node.emissions_factor else None,
        location=node.location.dict() if node.location else None,
        supplier=node.supplier.dict() if node.supplier else None,
    )

class NodeRepo:

    def __init__(self, tenant_id: str):
        self.engine = engine_pool.get_tenant_engine(tenant_id)

    def get_node(self, node_id: int) -> NodeRead | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Node)
                    .options(selectinload(Node.emissions_factor))
                    .options(selectinload(Node.location))
                    .options(selectinload(Node.supplier))
                    .where(Node.id == node_id)
                )
                node = session.exec(statement).one_or_none()

                return move_node_to_read(node)
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_node_by_name(self, product_id: str, name: str) -> NodeRead | None:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Node)
                    .where(Node.product_id == product_id)
                    .where(Node.name == name)
                )
                node = session.exec(statement).one_or_none()

                return move_node_to_read(node)
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all(self) -> List[NodeRead]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Node)
                    .options(selectinload(Node.emissions_factor))
                    .options(selectinload(Node.location))
                    .options(selectinload(Node.supplier))
                )
                nodes = session.exec(statement).all()

                return [move_node_to_read(node) for node in nodes]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def get_all_by_product_id(self, product_id: int) -> List[NodeRead]:
        with Session(self.engine) as session:
            try:
                statement = (
                    select(Node)
                    .options(selectinload(Node.emissions_factor))
                    .options(selectinload(Node.location))
                    .options(selectinload(Node.supplier))
                    .where(Node.product_id == product_id)
                )
                nodes = session.exec(statement).all()

                return [move_node_to_read(node) for node in nodes]
            except Exception as error:
                raise DatabaseError("SQL Error", error) from error

    def create(self, node: Node, address: Address | None = None) -> Node:
        with Session(self.engine) as session:
            try:
                if address:
                    session.add(address)
                    session.flush()
                    session.refresh(address)

                node.location_id = address.id if address else None

                session.add(node)
                session.commit()
                session.refresh(node)

                return node
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def delete(self, node_id: int) -> Node:
        with Session(self.engine) as session:
            try:
                node = session.get(Node, node_id)
                session.delete(node)
                session.commit()

                return node
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error

    def update(self, node_id: int, node: Node) -> NodeRead:
        with Session(self.engine) as session:
            try:
                _node = session.get(Node, node_id)
                if not _node:
                    raise ValueError(f"Node with ID {node_id} not found")

                for key, value in node.dict(exclude_unset=True, exclude_none=True).items():
                    setattr(_node, key, value)

                session.add(_node)
                session.commit()
                session.refresh(_node)

                statement = (
                    select(Node)
                    .options(selectinload(Node.emissions_factor))
                    .options(selectinload(Node.location))
                    .options(selectinload(Node.supplier))
                    .where(Node.id == _node.id)
                )
                refreshed_node = session.exec(statement).one()

                return move_node_to_read(refreshed_node)
            except Exception as error:
                session.rollback()
                raise DatabaseError("SQL Error", error) from error
