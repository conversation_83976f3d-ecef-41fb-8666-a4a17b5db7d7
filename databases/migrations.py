import os
import shutil
from alembic import command
from alembic.config import Config
from alembic.script import ScriptDirectory
from utils.logger import logger_instance


class MigrationManager:
    def __init__(
        self,
        config_file_path: str,
        script_location: str,
        db_url: str | None=None,
    ) -> None:
        self.config = Config(config_file_path)
        self.script_location = script_location
        self._logger = logger_instance.get_logger()

        if db_url:
            self.set_db_url(db_url)

        self.config.set_main_option(
            "script_location",
            script_location,
        )

    def set_db_url(self, db_url: str):
        self.config.set_main_option("sqlalchemy.url", db_url)

    def get_current_head(self) -> str:
        script = ScriptDirectory.from_config(self.config)
        return script.get_current_head()

    def upgrade_head(self) -> None:
        command.upgrade(self.config, "head")

    def downgrade_head(self) -> None:
        command.downgrade(self.config, "-1")

    def revision(
        self,
        message: str="autogenerated revision",
    ) -> None:
        command.revision(self.config, autogenerate=True, message=message)

    def clean_init(self) -> None:
        self._logger.warning(
            "Dangerous operation: Performing a clean initialization of alembic versioning",
        )
        versions_path = os.path.join(
            self.script_location,
            "versions",
        )

        try:
            if os.path.exists(versions_path):
                shutil.rmtree(versions_path)
            os.makedirs(versions_path)
        except Exception as error:
            self._logger.error(f"Error cleaning the versions directory: {error}")
            return

        self.revision("Initial revision")

    def revert_revision(self) -> None:
        self._logger.warning(
            "Dangerous operation: Reverting the last revision",
        )
        revision_id = self.get_current_head()
        version_dir = os.path.join(
            self.script_location,
            "versions",
        )
        files = os.listdir(version_dir)

        try:
            self.downgrade_head()
            head_revision_file = next(
                (
                    os.path.join(version_dir, f)
                    for f in files
                    if f.split("_")[0] == revision_id
                ),
                None,
            )

            if head_revision_file:
                if os.path.exists(head_revision_file):
                    os.remove(head_revision_file)

        except Exception as error:
            self._logger.error(f"Error reverting the last revision: {error}")
            return
