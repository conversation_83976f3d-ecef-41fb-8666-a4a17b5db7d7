import re
from typing import Any
from threading import Lock
from sqlalchemy.exc import OperationalError
from sqlalchemy.pool import QueuePool
from sqlmodel import create_engine, Session
from clients.propelauth import get_orgs
from databases.migrations import MigrationManager
from config import config
from exceptions import ResourceNotFoundException
from utils.logger import logger


Engine = Any


class EnginePool:
    def __init__(
        self,
        db_connection_string: str = config.DB_CONNECTION_STRING,
        pool_size: int = 5,
        max_overflow: int = 10,
        pool_timeout: int = 30,
        pool_recycle: int = 3600,  # Recycle connections after 1 hour
        pool_pre_ping: bool = True,
    ) -> None:
        self._engines = {}
        self._lock = Lock()
        self._db_connection_string = db_connection_string
        self._pool_size = pool_size
        self._max_overflow = max_overflow
        self._pool_timeout = pool_timeout
        self._pool_recycle = pool_recycle
        self._pool_pre_ping = pool_pre_ping

    def _get_engine(self, db_name: str = "", local_infile: bool = False) -> Engine:
        engine_url = f"{self._db_connection_string}/{db_name.lower()}"
        if local_infile:
            engine_url += "?local_infile=1"

        with self._lock:
            if engine_url not in self._engines:
                self._engines[engine_url] = create_engine(
                    engine_url,
                    poolclass=QueuePool,
                    pool_size=self._pool_size,
                    max_overflow=self._max_overflow,
                    pool_timeout=self._pool_timeout,
                    pool_recycle=self._pool_recycle,
                    pool_pre_ping=self._pool_pre_ping,
                )

            return self._engines[engine_url]

    def _purge_engine(self, db_name: str = "", local_infile: bool = False):
        engine_url = f"{self._db_connection_string}/{db_name.lower()}"
        if local_infile:
            engine_url += "?local_infile=1"

        with self._lock:
            if engine_url in self._engines:
                del self._engines[engine_url]

    def get_root_engine(self) -> Engine:
        """Return engine for root database."""
        return self._get_engine()

    def get_admin_engine(self) -> Engine:
        """Return engine for admin database."""
        return self._get_engine(config.ADMIN_DB_NAME)

    def get_shared_engine(self) -> Engine:
        """Return engine for shared database."""
        return self._get_engine(config.SHARED_DB_NAME, local_infile=True)

    def get_tenant_engine(self, tenant_id: str) -> Engine:
        """Return engine for tenant database."""
        db_name = get_db_name(tenant_id)
        engine = self._get_engine(db_name)

        try:
            with Session(engine) as session:
                session.execute("SELECT 1")

            return engine

        except OperationalError as error:
            logger.warning(f"Database not found: {db_name}")

            if tenant_id.lower().strip() in get_orgs():
                migration_manager = MigrationManager(
                    config_file_path="./databases/tenant/alembic.ini",
                    script_location="./databases/tenant/alembic",
                )

                with Session(self.get_root_engine()) as session:
                    session.execute(f"CREATE DATABASE IF NOT EXISTS {db_name};")
                    session.commit()

                logger.info(f"Created database {db_name}")

                logger.info("Running alembic upgrade head")

                db_url = f"{config.DB_CONNECTION_STRING}/{db_name}"
                migration_manager.set_db_url(db_url)
                migration_manager.upgrade_head()
                self._purge_engine(db_name)

                return self._get_engine(db_name)
            raise ResourceNotFoundException("Tenant", tenant_id) from error

def get_db_name(tenant_id: str) -> str:
    # Only allow letters, numbers, and underscores.
    if not re.match(r'^[a-zA-Z0-9_]+$', tenant_id):
        raise ValueError("Invalid tenant id")
    return f"tenant_{tenant_id.lower()}"

engine_pool = EnginePool()
