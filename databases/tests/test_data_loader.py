from unittest import TestCase
from unittest.mock import patch, MagicMock, Mock
from databases.utils.data_loader import DataLoader


class TestDataLoader(TestCase):
    @patch('databases.utils.data_loader.update_latest_version')
    @patch('databases.utils.data_loader.check_if_file_is_same_version')
    @patch('databases.utils.data_loader.Session')
    @patch('databases.utils.data_loader.load_dataset')
    @patch('databases.utils.data_loader.login')
    def test_check_skip_seeding(self, mock_login, mock_load_dataset, mock_session, mock_check_if_file_is_same_version, mock_update_latest_version):
        mock_login.return_value = None
        mock_dataset = {'train': Mock()}
        mock_load_dataset.return_value = mock_dataset

        mock_session_obj = Mock()
        mock_session.return_value.__enter__.return_value = mock_session_obj

        mock_execute = Mock()
        mock_session_obj.execute = mock_execute

        # Configure the return value for execute().first()
        mock_execute.return_value.first.return_value = True

        mock_check_if_file_is_same_version.return_value = True
        mock_update_latest_version.return_value = None

        data_loader = DataLoader(
            engine=MagicMock(),
            repo_id="test",
            filename="test",
            seeding_function=MagicMock(),
            table_name="test",
            delete_from_table_query_statements=None,
        )

        self.assertTrue(data_loader.check_skip_seeding())


    @patch('databases.utils.data_loader.update_latest_version')
    @patch('databases.utils.data_loader.check_if_file_is_same_version')
    @patch('databases.utils.data_loader.Session')
    @patch('databases.utils.data_loader.load_dataset')
    @patch('databases.utils.data_loader.login')
    def test_check_file_version_are_different(
        self,
        mock_login,
        mock_load_dataset,
        mock_session,
        mock_check_if_file_is_same_version,
        mock_update_latest_version,
    ):
        mock_login.return_value = None
        mock_dataset = {'train': Mock()}
        mock_load_dataset.return_value = mock_dataset

        mock_session_obj = Mock()
        mock_session.return_value.__enter__.return_value = mock_session_obj

        mock_execute = Mock()
        mock_session_obj.execute = mock_execute

        # Configure the return value for execute().first()
        mock_execute.return_value.first.return_value = True

        mock_check_if_file_is_same_version.return_value = False
        mock_update_latest_version.return_value = None

        data_loader = DataLoader(
            engine=MagicMock(),
            repo_id="test",
            filename="test",
            seeding_function=MagicMock(),
            table_name="test",
            delete_from_table_query_statements=None,
        )

        self.assertFalse(data_loader.check_skip_seeding())

    @patch('databases.utils.data_loader.update_latest_version')
    @patch('databases.utils.data_loader.check_if_file_is_same_version')
    @patch('databases.utils.data_loader.Session')
    @patch('databases.utils.data_loader.load_dataset')
    @patch('databases.utils.data_loader.login')
    def test_check_file_version_are_same_with_empty_table(
        self,
        mock_login,
        mock_load_dataset,
        mock_session,
        mock_check_if_file_is_same_version,
        mock_update_latest_version,
    ):
        mock_login.return_value = None
        mock_dataset = {'train': Mock()}
        mock_load_dataset.return_value = mock_dataset

        mock_session_obj = Mock()
        mock_session.return_value.__enter__.return_value = mock_session_obj

        mock_execute = Mock()
        mock_session_obj.execute = mock_execute

        # Configure the return value for execute().first()
        mock_execute.return_value.first.return_value = False

        mock_check_if_file_is_same_version.return_value = True
        mock_update_latest_version.return_value = None

        data_loader = DataLoader(
            engine=MagicMock(),
            repo_id="test",
            filename="test",
            seeding_function=MagicMock(),
            table_name="test",
            delete_from_table_query_statements=None,
        )

        self.assertFalse(data_loader.check_skip_seeding())

    @patch('databases.utils.data_loader.update_latest_version')
    @patch('databases.utils.data_loader.check_if_file_is_same_version')
    @patch('databases.utils.data_loader.Session')
    @patch('databases.utils.data_loader.load_dataset')
    @patch('databases.utils.data_loader.login')
    @patch('databases.utils.data_loader.DataLoader.clear_table')
    def test_check_clear_table_and_call_seeding_function_for_seeding(
        self,
        mock_login,
        mock_load_dataset,
        mock_session,
        mock_check_if_file_is_same_version,
        mock_update_latest_version,
        mock_clear_table,
    ):
        mock_login.return_value = None
        mock_dataset = {'train': Mock()}
        mock_load_dataset.return_value = mock_dataset

        mock_session_obj = Mock()
        mock_session.return_value.__enter__.return_value = mock_session_obj

        mock_execute = Mock()
        mock_session_obj.execute = mock_execute

        # Configure the return value for execute().first()
        mock_execute.return_value.first.return_value = True

        mock_check_if_file_is_same_version.return_value = False
        mock_update_latest_version.return_value = None

        mock_seeding_function = Mock()
        mock_seeding_function.return_value = None

        mock_clear_table.return_value = None

        data_loader = DataLoader(
            engine=MagicMock(),
            repo_id="test",
            filename="test",
            seeding_function=mock_seeding_function,
            table_name="test",
            delete_from_table_query_statements=None,
        )

        data_loader.seed_data()

        mock_clear_table.assert_called_once()
        mock_seeding_function.assert_called_once()

    def tearDown(self):
        if not self._outcome.success:
            return

        test_method_name = self._testMethodName
        print(f"Test passed: {test_method_name}")
