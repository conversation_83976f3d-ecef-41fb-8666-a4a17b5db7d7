import unittest
from sqlmodel import Session
from databases.engine import engine_pool
from databases.common.emissions_factor_query import (
    build_emissions_factor_search_query,
    clean_search_term
)
from databases.shared.models import EmissionsFactors

class TestEmissionsFactorQuery(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # Use the shared database engine
        cls.engine = engine_pool.get_shared_engine()

    def setUp(self):
        # Create a session
        self.session = Session(self.engine)

    def tearDown(self):
        # Close session
        self.session.close()

    def test_clean_search_term(self):
        """Test that special characters are properly cleaned from search terms."""
        # Test with various special characters
        self.assertEqual(clean_search_term("steel+production"), "steel production")
        self.assertEqual(clean_search_term("steel-alloy"), "steel alloy")
        self.assertEqual(clean_search_term("steel (raw)"), "steel  raw ")
        self.assertEqual(clean_search_term("steel@copper"), "steel copper")
        self.assertEqual(clean_search_term("steel>iron"), "steel iron")
        self.assertEqual(clean_search_term("steel<iron"), "steel iron")
        self.assertEqual(clean_search_term("steel~copper"), "steel copper")
        self.assertEqual(clean_search_term("steel*"), "steel ")
        self.assertEqual(clean_search_term('"steel"'), " steel ")

    def test_basic_search(self):
        """Test basic search functionality with simple terms."""
        # Search for a common term that should exist in the database
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="steel"
        )
        results = self.session.exec(query).all()

        # Should return some results
        self.assertGreater(len(results), 0)

        # Check that results contain the search term
        for result in results:
            self.assertIn("steel", result[0].activity_name.lower())

    def test_exact_match_priority(self):
        """Test that exact matches are prioritized in search results."""
        # Use a common term that should exist in the database
        exact_term = "steel"

        # Now search for this exact term
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name=exact_term
        )
        results = self.session.exec(query).all()

        # First result should contain the search term
        self.assertIn(exact_term.lower(), results[0][0].activity_name.lower())

        # Check that the exact match score is either 50 or 100
        # 100 means exact match, 50 means contains the term
        self.assertIn(results[0][3], [50, 100])  # Assuming index 3 is exact_match_score

    def test_length_scoring(self):
        """Test that shorter strings are prioritized when exact match scores are equal."""
        # Search for a term that will have multiple non-exact matches
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="production"  # Common term that should have many matches
        )
        results = self.session.exec(query).all()

        if len(results) < 2:
            self.skipTest("Not enough results to test length scoring")

        # Get the exact match scores and lengths
        exact_match_scores = [r[3] for r in results]  # Assuming index 3 is exact_match_score
        lengths = [len(r[0].activity_name) for r in results]

        # Find results with the same exact match score
        for score in set(exact_match_scores):
            same_score_indices = [i for i, s in enumerate(exact_match_scores) if s == score]
            if len(same_score_indices) > 1:
                # Within this group, check that shorter strings come before longer ones
                for i in range(len(same_score_indices) - 1):
                    idx1 = same_score_indices[i]
                    idx2 = same_score_indices[i + 1]
                    # With our recent change, shorter strings should have lower length_score values
                    # and be sorted in ascending order
                    self.assertLessEqual(lengths[idx1], lengths[idx2])

    def test_special_character_search(self):
        """Test that searches with special characters work correctly after cleaning."""
        # Search with special characters
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="steel+production"
        )
        results1 = self.session.exec(query).all()

        # Search with the cleaned version
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="steel production"
        )
        results2 = self.session.exec(query).all()

        # Both searches should return results
        self.assertGreater(len(results1), 0)
        self.assertGreater(len(results2), 0)

        # The top results should be similar (might not be identical due to other factors)
        # but at least they should both contain "steel" and "production"
        for result in results1[:5]:  # Check top 5 results
            self.assertIn("steel", result[0].activity_name.lower())

        for result in results2[:5]:  # Check top 5 results
            self.assertIn("steel", result[0].activity_name.lower())

    def test_geography_filter(self):
        """Test that geography filtering works correctly."""
        # Use a common geography that should exist in the database
        geography = "GLO"

        # Search with this geography filter
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            geography=geography
        )
        results = self.session.exec(query).all()

        # All results should have the specified geography
        for result in results:
            self.assertEqual(result[0].geography, geography)

    def test_unit_filter(self):
        """Test that unit filtering works correctly."""
        # Use a common unit that should exist in the database
        unit = "kg"

        # Search with this unit filter
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            unit=unit
        )
        results = self.session.exec(query).all()

        # All results should have the specified unit (case-insensitive)
        for result in results:
            self.assertEqual(result[0].unit.lower(), unit.lower())

    def test_source_filter(self):
        """Test that source filtering works correctly."""
        # Use a common source that should exist in the database
        source = "Ecoinvent"

        # Search with this source filter
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            source=source
        )
        results = self.session.exec(query).all()

        # All results should have the specified source
        for result in results:
            self.assertEqual(result[0].source, source)

    def test_carbon_only_filter(self):
        """Test that carbon_only filtering works correctly."""
        # Search with carbon_only=True
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            carbon_only=True
        )
        results = self.session.exec(query).all()

        # Should only return Ecoinvent sources
        for result in results:
            self.assertTrue(result[0].source.startswith("Ecoinvent"))

    def test_empty_search(self):
        """Test that empty search terms return results without full-text search ordering."""
        # Search with empty string
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name=""
        )
        results = self.session.exec(query).all()

        # Should return some results
        self.assertGreater(len(results), 0)

        # Results should be limited by the default paging value (200)
        self.assertLessEqual(len(results), 200)

if __name__ == "__main__":
    unittest.main()
