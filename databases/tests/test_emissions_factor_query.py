import unittest
from sqlmodel import Session
from databases.engine import engine_pool
from databases.common.emissions_factor_query import (
    build_emissions_factor_search_query,
    clean_search_term
)
from databases.shared.models import EmissionsFactors

class TestEmissionsFactorQuery(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # Use the shared database engine
        cls.engine = engine_pool.get_shared_engine()

    def setUp(self):
        # Create a session
        self.session = Session(self.engine)

    def tearDown(self):
        # Close session
        self.session.close()

    def test_clean_search_term(self):
        """Test that special characters are properly cleaned from search terms."""
        # Test with various special characters
        self.assertEqual(clean_search_term("steel+production"), "steel production")
        self.assertEqual(clean_search_term("steel-alloy"), "steel alloy")
        self.assertEqual(clean_search_term("steel (raw)"), "steel  raw ")
        self.assertEqual(clean_search_term("steel@copper"), "steel copper")
        self.assertEqual(clean_search_term("steel>iron"), "steel iron")
        self.assertEqual(clean_search_term("steel<iron"), "steel iron")
        self.assertEqual(clean_search_term("steel~copper"), "steel copper")
        self.assertEqual(clean_search_term("steel*"), "steel ")
        self.assertEqual(clean_search_term('"steel"'), " steel ")

    def test_basic_search(self):
        """Test basic search functionality with simple terms."""
        # Search for a common term that should exist in the database
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="steel"
        )
        results = self.session.exec(query).all()

        # Should return some results
        self.assertGreater(len(results), 0)

        # Check that results contain the search term
        for result in results:
            self.assertIn("steel", result[0].activity_name.lower())

    def test_exact_match_priority(self):
        """Test that exact matches are prioritized in search results."""
        # Use a common term that should exist in the database
        exact_term = "steel"

        # Now search for this exact term
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name=exact_term
        )
        results = self.session.exec(query).all()

        # First result should contain the search term
        self.assertIn(exact_term.lower(), results[0][0].activity_name.lower())

        # Check that the exact match score is either 50 or 100
        # 100 means exact match, 50 means contains the term
        self.assertIn(results[0][3], [50, 100])  # Assuming index 3 is exact_match_score

    def test_length_scoring(self):
        """Test that shorter strings are prioritized when exact match scores are equal."""
        # Search for a term that will have multiple non-exact matches
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="production"  # Common term that should have many matches
        )
        results = self.session.exec(query).all()

        if len(results) < 2:
            self.skipTest("Not enough results to test length scoring")

        # Get the exact match scores and lengths
        exact_match_scores = [r[3] for r in results]  # Assuming index 3 is exact_match_score
        lengths = [len(r[0].activity_name) for r in results]

        # Find results with the same exact match score
        for score in set(exact_match_scores):
            same_score_indices = [i for i, s in enumerate(exact_match_scores) if s == score]
            if len(same_score_indices) > 1:
                # Within this group, check that shorter strings come before longer ones
                for i in range(len(same_score_indices) - 1):
                    idx1 = same_score_indices[i]
                    idx2 = same_score_indices[i + 1]
                    # With our recent change, shorter strings should have lower length_score values
                    # and be sorted in ascending order
                    self.assertLessEqual(lengths[idx1], lengths[idx2])

    def test_special_character_search(self):
        """Test that searches with special characters work correctly after cleaning."""
        # Search with special characters
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="steel+production"
        )
        results1 = self.session.exec(query).all()

        # Search with the cleaned version
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="steel production"
        )
        results2 = self.session.exec(query).all()

        # Both searches should return results
        self.assertGreater(len(results1), 0)
        self.assertGreater(len(results2), 0)

        # The top results should be similar (might not be identical due to other factors)
        # but at least they should both contain "steel" and "production"
        for result in results1[:5]:  # Check top 5 results
            self.assertIn("steel", result[0].activity_name.lower())

        for result in results2[:5]:  # Check top 5 results
            self.assertIn("steel", result[0].activity_name.lower())

    def test_geography_filter(self):
        """Test that geography filtering works correctly with AND logic."""
        # Test with a single geography value first (should work the same as before)
        geography_single = "GLO"
        query_single = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            geography=geography_single
        )
        results_single = self.session.exec(query_single).all()

        # All results should have the specified geography
        for result in results_single:
            self.assertEqual(result[0].geography, geography_single)

        # Now test with multiple geography values (should return empty with AND logic)
        geography_multiple = "GLO,US"
        query_multiple = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            geography=geography_multiple
        )
        results_multiple = self.session.exec(query_multiple).all()

        # Since a record can't have both GLO and US geographies simultaneously,
        # we expect no results with AND logic
        self.assertEqual(len(results_multiple), 0)

    def test_unit_filter(self):
        """Test that unit filtering works correctly with AND logic."""
        # Test the core AND logic behavior with multiple different units
        # This should return no results since no record can have multiple units
        unit_multiple = "kg,ton,liter"
        query_multiple = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            unit=unit_multiple
        )
        results_multiple = self.session.exec(query_multiple).all()

        # Since a record can't have multiple different units simultaneously,
        # we expect no results with AND logic
        self.assertEqual(len(results_multiple), 0)

        # Test that single unit filtering still works
        # Get some data to find a valid unit
        query_all = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
        )
        all_results = self.session.exec(query_all).all()

        if len(all_results) > 0:
            # Get a unit that exists in the database
            existing_unit = all_results[0][0].unit

            # Test with this existing unit
            query_single = build_emissions_factor_search_query(
                session=self.session,
                model=EmissionsFactors,
                activity_name="",  # Empty to get all results
                unit=existing_unit
            )
            results_single = self.session.exec(query_single).all()

            # Should get some results
            self.assertGreater(len(results_single), 0)

            # All results should have the specified unit
            for result in results_single:
                self.assertEqual(result[0].unit, existing_unit)

    def test_source_filter(self):
        """Test that source filtering works correctly with AND logic."""
        # Test with a single source value first (should work the same as before)
        source_single = "Ecoinvent"
        query_single = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            source=source_single
        )
        results_single = self.session.exec(query_single).all()

        # All results should have the specified source
        for result in results_single:
            self.assertEqual(result[0].source, source_single)

        # Now test with multiple source values (should return empty with AND logic)
        source_multiple = "Ecoinvent,DEFRA"
        query_multiple = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            source=source_multiple
        )
        results_multiple = self.session.exec(query_multiple).all()

        # Since a record can't have both Ecoinvent and DEFRA sources simultaneously,
        # we expect no results with AND logic
        self.assertEqual(len(results_multiple), 0)

    def test_carbon_only_filter(self):
        """Test that carbon_only filtering works correctly."""
        # Search with carbon_only=True
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",  # Empty to get all results
            carbon_only=True
        )
        results = self.session.exec(query).all()

        # Should only return Ecoinvent sources
        for result in results:
            self.assertTrue(result[0].source.startswith("Ecoinvent"))

    def test_empty_search(self):
        """Test that empty search terms return results without full-text search ordering."""
        # Search with empty string
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name=""
        )
        results = self.session.exec(query).all()

        # Should return some results
        self.assertGreater(len(results), 0)

        # Results should be limited by the default paging value (200)
        self.assertLessEqual(len(results), 200)

    def test_and_logic_with_whitespace(self):
        """Test that AND logic handles whitespace correctly in comma-separated values."""
        # Test with whitespace around values
        geography_with_spaces = " GLO , US "
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",
            geography=geography_with_spaces
        )
        results = self.session.exec(query).all()

        # Should return no results due to AND logic
        self.assertEqual(len(results), 0)

    def test_and_logic_with_empty_values(self):
        """Test that AND logic handles empty values correctly."""
        # Test with empty values in comma-separated list
        geography_with_empty = "GLO,,US"
        query = build_emissions_factor_search_query(
            session=self.session,
            model=EmissionsFactors,
            activity_name="",
            geography=geography_with_empty
        )
        results = self.session.exec(query).all()

        # Should return no results due to AND logic (GLO AND US)
        self.assertEqual(len(results), 0)

if __name__ == "__main__":
    unittest.main()
