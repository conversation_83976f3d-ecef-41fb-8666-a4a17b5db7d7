from unittest import TestCase
from unittest.mock import patch
from sqlmodel import Session
from databases.engine import engine_pool
from exceptions import ResourceNotFoundException


def check_if_database_exists(db_name):
    engine = engine_pool.get_root_engine()

    with Session(engine) as session:
        result = session.exec(
            (
                "SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA "
                f"WHERE SCHEMA_NAME = '{db_name}'"
            )
        )
        return result.first() is not None

class TestDataLoader(TestCase):
    @patch('databases.engine.get_orgs')
    def test_setup_database_for_valid_tenant(self, mock_get_orgs):
        """
        If a tenant engine is requested and the DB
        is not yet setup, create the database and setup
        """
        tenant_id = "test"
        db_name = f"tenant_{tenant_id}"

        with Session(engine_pool.get_root_engine()) as session:
            session.exec(f"DROP DATABASE IF EXISTS {db_name};")
            session.commit()

        mock_get_orgs.return_value = [tenant_id]
        engine_pool.get_tenant_engine(tenant_id)

        self.assertTrue(check_if_database_exists(db_name))

        with Session(engine_pool.get_root_engine()) as session:
            session.exec(f"DROP DATABASE IF EXISTS {db_name};")
            session.commit()

    @patch('databases.engine.get_orgs')
    def test_404_for_invalid_tenant(self, mock_get_orgs):
        """If a tenant is not a valid PropelAuth Org, ignore setup and throw 404."""
        valid_tenant_id = "valid"
        invalid_tenant_id = "invalid"
        invalid_db_name = f"tenant_{invalid_tenant_id}"

        mock_get_orgs.return_value = [valid_tenant_id]

        with self.assertRaises(ResourceNotFoundException):
            engine_pool.get_tenant_engine(invalid_tenant_id)

        self.assertFalse(check_if_database_exists(invalid_db_name))

        with Session(engine_pool.get_root_engine()) as session:
            session.exec(f"DROP DATABASE IF EXISTS {invalid_db_name};")
            session.commit()
