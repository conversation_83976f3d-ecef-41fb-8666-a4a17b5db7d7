import os
from datetime import datetime
from typing import Callable, Dict, Any
from unittest.mock import Magic<PERSON><PERSON>, <PERSON><PERSON>
from sqlmodel import Session
from datasets import load_dataset
from huggingface_hub import login
from databases.engine import Engine
from databases.admin.repository import (
    check_if_file_is_same_version,
    update_latest_version,
    get_last_modified_date,
)
from config import config
from utils.logger import logger_instance


logger = logger_instance.get_logger()


class DataLoader:
    """
    This class is responsible for loading the dataset from HuggingFace Hub
    and returning it as a dictionary and seeding the database with the data.
    """

    def __init__(
        self,
        engine: Any,
        repo_id: str,
        filename: str,
        seeding_function: Callable[[Engine, Dict[Any, Any]], None],
        table_name: str | None = None,
        delete_from_table_query_statements: list[str] | None = None,
        tenant_id: str | None = None,
        force_login: bool = False,
        hf_token_path: str = "/root/.cache/huggingface/token",
        truncate_table: bool = True,
    ):
        self.engine = engine
        self.table_name = table_name
        self.repo_id = repo_id
        self.tenant_id = tenant_id
        self.filename = filename
        self.truncate_table = truncate_table

        if not os.path.exists(hf_token_path) or force_login:
            login(token=config.HF_TOKEN)

        try:
            loader_args = {
                "path": self.repo_id,
                "data_files": self.filename,
            }

            if self.filename.endswith(".csv"):
                loader_args.update({
                    "keep_default_na": False,
                    "na_values": ["","N/A"],
                })

            self.dataset = load_dataset(**loader_args)["train"]
            self.fingerprint = (
                datetime.now().isoformat()
                if isinstance(self.dataset, (MagicMock, Mock))
                else get_last_modified_date(self.repo_id, self.filename)
            )
        except Exception:
            logger.exception(f"Dataset {self.filename} not found in {self.repo_id}")
            self.dataset = {}
            self.fingerprint = None

        self.seeding_function = seeding_function
        self.delete_from_table_query_statements = delete_from_table_query_statements

    def check_skip_seeding(self) -> bool:
        """
        Checks if the seeding should be skipped.
        """
        if not self.dataset:
            return False

        if check_if_file_is_same_version(
            repo_id=self.repo_id,
            filename=self.filename,
            fingerprint=self.fingerprint,
        ):
            if self.table_name:
                with Session(self.engine) as session:
                    result = session.execute(
                        f"SELECT * FROM {self.table_name};"
                    ).first()

                if result:
                    return True
            else:
                return True

        return False

    def clear_table(self) -> None:
        """
        Clears the table.

        If the delete_from_table_query_statement is provided, it will be used
        to delete the data from the table. Otherwise, it will delete all the
        data from the table.
        """
        with Session(self.engine) as session:
            try:
                if self.delete_from_table_query_statements:
                    for statement in self.delete_from_table_query_statements:
                        session.execute(statement)
                        session.commit()
                else:
                    session.execute(f"DELETE FROM {self.table_name};")
                    session.commit()

                logger.success(f"Deleted data from {self.table_name}")
            except Exception as error:
                logger.exception(f"Error deleting data from {self.table_name}: {error}")
                session.rollback()
                return

    def seed_data(self) -> None:
        """
        Seeds the database with the data from the dataset.
        """
        if self.check_skip_seeding():
            logger.info(f"{self.filename} data is up to date")
            return

        if self.table_name and self.truncate_table:
            self.clear_table()

        try:
            args = [self.engine, self.dataset]
            if self.tenant_id is not None:
                args.append(self.tenant_id)

            self.seeding_function(*args)
        except Exception as error:
            raise Exception(f"Error seeding {self.table_name}: {error}") from error

        if self.fingerprint:
            update_latest_version(
                repo_id=self.repo_id,
                filename=self.filename,
                fingerprint=self.fingerprint,
            )
