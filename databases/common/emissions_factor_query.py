from typing import Type, TypeVar
import re
from sqlmodel import Session, SQLModel, text
from sqlalchemy.orm import Query

T = TypeVar('T', bound=SQLModel)

def clean_search_term(text_input: str) -> str:
    """
    Clean search terms for MySQL full-text search by removing special characters
    that would be interpreted as operators in BOOLEAN MODE.
    """
    # Replace special characters with spaces
    return re.sub(r'[+\-><()~*"@]', ' ', text_input)

def build_emissions_factor_search_query(
    session: Session,
    model: Type[T],
    activity_name: str,
    geography: str | None = None,
    unit: str | None = None,
    source: str | None = None,
    paging: int | None = 200,
    carbon_only: bool | None = False,
) -> Query:
    """
    Builds a common query for searching emissions factors with full-text search.

    Args:
        session: SQLModel session
        model: The SQLModel class to query
        activity_name: The activity name to search for
        geography: Optional geography filter (comma-separated)
        unit: Optional unit filter (comma-separated)
        source: Optional source filter (comma-separated)
        paging: Optional limit for results
        carbon_only: Optional filter for carbon-only sources

    Returns:
        Query object with full-text search and filters applied
    """
    if not activity_name.strip():
        statement = session.query(model)
    else:
        # Clean the activity name for search
        cleaned_activity_name = clean_search_term(activity_name)

        # For natural language search(stop words are ignored)
        natural_phrase = cleaned_activity_name

        # For boolean mode with term-by-term matching
        # Only use + prefix for words with 4 or more characters to avoid issues with stopwords
        terms = " ".join(
            f"+{word}" if len(word) >= 4 else word
            for word in cleaned_activity_name.split()
            if word.strip()
        )

        # Query with stronger prioritization
        statement = session.query(
            model,
            text("""MATCH(activity_name) AGAINST (:natural_phrase IN NATURAL LANGUAGE MODE)
                AS phrase_score"""),
            text("MATCH(activity_name) AGAINST (:terms IN BOOLEAN MODE) AS terms_score"),
            # More explicit exact match scoring with higher priority for exact matches
            text("""
                CASE
                    WHEN LOWER(activity_name) = LOWER(:exact_term) THEN 100  -- Exact match gets highest priority
                    WHEN LOWER(activity_name) LIKE CONCAT('%', LOWER(:exact_term), '%') THEN 50  -- Contains the exact term
                    ELSE 0
                END AS exact_match_score
            """),
            # Add a separate column for length scoring
            # Shorter strings have lower scores
            text("""LENGTH(activity_name)/10 AS length_score""")
        ).filter(
            text("""MATCH(activity_name) AGAINST (:terms IN BOOLEAN MODE) > 0
                OR LOWER(activity_name) LIKE CONCAT('%', LOWER(:exact_term), '%')""")
        ).params(
            exact_term=activity_name.strip(),
            natural_phrase=natural_phrase,
            terms=terms
        )

        # Order by custom scoring system
        statement = statement.order_by(
            text("exact_match_score DESC"),  # Exact matches first
            text("length_score ASC"),       # Then shorter strings
            text("phrase_score DESC"),       # Then by natural language relevance
            text("terms_score DESC")         # Then by boolean mode relevance
        )

    if geography:
        statement = statement.filter(
            model.geography.in_(geography.split(","))
        )

    if unit:
        statement = statement.filter(
            model.unit.in_(unit.split(","))
        )

    if source:
        statement = statement.filter(
            model.source.in_(source.split(","))
        )

    if carbon_only:
        statement = statement.filter(
            model.source.startswith("Ecoinvent")
        )

    return statement.limit(paging)
